class Finance::Admin::SubProjectsExcel
  include TalltyImportExport::Importable

  class Import
    def import_headers(*args)
      [
        { key: 'parent_project', name: '父资金卡编号', skip: true, proc: ->(val, context) {
          context.parent_project = Finance::MainProject.find_by!(uid: val);
          context.parent_project
        } },
        { key: 'project_id', name: '资金卡编号', primary_key: true, proc: ->(val, context) {
          context.project = context.parent_project.sub_projects.where(uid: val).first_or_create!(name: 'to be merge')
          context.project.id
        } },
        { key: 'project_name', name: '资金卡名称', skip: true, proc: ->(val, context) {
          context.project.update name: val
        } },
        { key: 'parent_budget', primary_key: true, name: '父三级明细', proc: ->(val, context) {
          context.parent_project.budgets.find_by!(name: val)
        } },
        { key: 'department_code', name: '部门名称', skip: true, proc: ->(val, context) {
          context.project.update department_code: ::Department.find_by_name(val)&.code
        } },
        { key: 'owner', name: '项目负责人工号', skip: true, proc: ->(val, context) {
          context.project.update owner: ::Teacher.find_by_code(val.to_i)
        }},
        { key: 'name', primary_key: true, name: '三级明细' },
        { key: 'number', name: '数量' },
        { key: 'unit_price', name: '单价' },
        { key: 'amount', name: '金额' },
      ]
    end
  end
end
