class Api::InstancesController < ApplicationController
  def create
    instance = Bpm::Instance.create(
      creator_type: 'Teacher',
      creator_id: ENV['SHANGDIANTONG_USER_ID'],
      workflow_id: 392,
      payload: {
        'textarea_1669699974765': params[:desc],
        'file_1669699987657': params[:attachments],
        'input_1669699965598': params[:phone],
        'input_1669706165326': params[:name]
      }
    )
    instance.tokens.first&.fire!(action: 'submit')
    render json: { code: 1, message: 'ok' }
  end
end