class Inform::Admin::IntroductionsController < SimpleController::BaseController
  defaults(
    resource_class: Inform::Introduction,
    collection_name: 'introductions',
    instance_name: 'introduction',
    view_path: 'inform/introductions'
  )

  auth_action :teacher # 身份认证
  permit_action :portal_admin # 权限认证

  protected

    def begin_of_association_chain
      current_teacher.school
    end

    def method_for_association_chain
      :inform_introduction
    end

  private

    def introduction_params
      params.require(:introduction).permit(
        :title, :content, :introduction_type
      )
    end
end
