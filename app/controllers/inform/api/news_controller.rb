class Inform::Api::NewsController < SimpleController::BaseController
  defaults(
    resource_class: Inform::News,
    collection_name: 'news',
    instance_name: 'news',
    view_path: 'inform/news'
  )

  after_action :increment_view_count!, only: :show

  private

  def begin_of_association_chain
    School.first
  end

  def increment_view_count!
    @news.increment!(:view_count, touch: true)
  end
end
