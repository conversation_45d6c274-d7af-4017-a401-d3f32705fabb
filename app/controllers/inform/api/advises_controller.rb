class Inform::Api::AdvisesController < SimpleController::BaseController
  defaults(
    resource_class: Inform::Advise,
    collection_name: 'advises',
    instance_name: 'advise',
    view_path: 'inform/advises'
  )

  after_action :increment_view_count!, only: :show

  private

  def begin_of_association_chain
    School.first
  end

  def increment_view_count!
    @advise.increment!(:view_count, touch: true)
  end
end
