class Inform::User::NewsController < SimpleController::BaseController
  defaults(
    resource_class: Inform::News,
    collection_name: 'news',
    instance_name: 'news',
    view_path: 'inform/news'
  )

  belongs_to :mod, class_name: 'Inform::Mod', optional: true

  auth_action [:teacher, :student]
  permit_action :portal_admin, only: [:create, :update, :destroy]

  after_action :increment_view_count!, only: :show

  def create
    @news = create_association.create!(news_params)
    respond_with @news, template: "#{view_path}/show", status: 201
  end

  def update
    my_news.find(params[:id]).update!(news_params)
    head 204
  end

  def destroy
    my_news.find(params[:id]).destroy!
    head 204
  end

  private

  def begin_of_association_chain
    current_auth.school
  end

  def news_params
    images_params = {}
    banners = params.require(:news).fetch(:banners, [])&.map(&:permit!)
    cover_image = params.require(:news).fetch(:cover_image, [])&.map(&:permit!)
    images_params.merge!(banners: banners) if params.require(:news)[:banners]
    images_params.merge!(cover_image: cover_image) if params.require(:news)[:cover_image]

    params.require(:news).permit(
      :title, :sub_title, :content, :top, :catalog,
      :position, :inform_mod_id, :publish_time, tag_list: [], platform: [],
    ).merge(creator: current_auth).merge(images_params)
  end

  def my_news
    current_auth.school.news#.where(creator: current_auth)
  end

  def create_association
    current_auth.school.inform_mods.find(news_params[:inform_mod_id]).news.where(creator: current_auth)
  end

  def increment_view_count!
    @news.increment!(:view_count, touch: true)
  end
end
