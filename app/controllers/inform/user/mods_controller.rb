class Inform::User::ModsController < SimpleController::BaseController
  defaults(
    resource_class: Inform::Mod,
    collection_name: 'inform_mods',
    instance_name: 'inform_mod',
    view_path: 'inform/mods'
  )

  auth_action [:teacher, :student]
  permit_action :inform_admin, only: [:create, :update, :destroy]

  private

  def begin_of_association_chain
    current_auth.school
  end

  def inform_mod_params
    params.require(:mod).permit(
      :title, :school_id, :position
    )
  end
end
