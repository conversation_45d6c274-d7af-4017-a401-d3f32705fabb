class Inform::User::AdvisesController < SimpleController::BaseController
  defaults(
    resource_class: Inform::Advise,
    collection_name: 'advises',
    instance_name: 'advise',
    view_path: 'inform/advises'
  )

  auth_action [:teacher, :student]
  permit_action :portal_admin, only: [:create, :update, :destroy]
  after_action :increment_view_count!, only: :show

  def create
    @advise = create_association.create!(advise_params)
    respond_with @advise, template: "#{view_path}/show", status: 201
  end

  def update
    my_advise.find(params[:id]).update!(advise_params)
    head 204
  end

  def destroy
    my_advise.find(params[:id]).destroy!
    head 204
  end

  private

  def begin_of_association_chain
    current_auth.school
  end

  def advise_params
    images_params = {}
    banners = params.require(:advise).fetch(:banners, [])&.map(&:permit!)
    cover_image = params.require(:advise).fetch(:cover_image, [])&.map(&:permit!)
    images_params.merge!(banners: banners) if params.require(:advise)[:banners]
    images_params.merge!(cover_image: cover_image) if params.require(:advise)[:cover_image]

    params.require(:advise).permit(
      :title, :sub_title, :content, :top, :catalog,
      :position, :inform_mod_id, :publish_time, tag_list: [], platform: [],
    ).merge(creator: current_auth).merge(images_params)
  end

  def my_advise
    current_auth.school.advises#.where(creator: current_auth)
  end

  def create_association
    current_auth.school.inform_mods.find(advise_params[:inform_mod_id]).advise.where(creator: current_auth)
  end

  private

  def increment_view_count!
    @advise.increment!(:view_count, touch: true)
  end
end
