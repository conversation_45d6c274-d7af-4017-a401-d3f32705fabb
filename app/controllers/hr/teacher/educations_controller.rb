class Hr::Teacher::EducationsController < SimpleController::BaseController
  defaults(
    resource_class: Education,
    collection_name: 'educations',
    instance_name: 'education',
    view_path: 'educations'
  )
  auth_action :teacher

  private

  def begin_of_association_chain
    current_teacher
  end

  def education_params
    params.require(:education).permit(
      :user_type, :user_id, :type, :state, :school, :major, :degree, :edu, :body,
      :country, :start_at, :end_at, :deleted_at,
      attachments: {}, meta:{}
    )
  end
end
