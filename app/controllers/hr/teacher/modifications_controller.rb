class Hr::Teacher::ModificationsController < SimpleController::BaseController
  defaults(
    resource_class: Modification,
    collection_name: 'modifications',
    instance_name: 'modification',
    view_path: 'modifications'
  )

  auth_action :teacher

  protected
    def begin_of_association_chain
      current_teacher
    end

  private
    def modification_params
      params.require(:modification).permit(
        :state, meta: {}, info: {}
      ).merge(apply_user: current_teacher, school: current_teacher.school)
    end
end
