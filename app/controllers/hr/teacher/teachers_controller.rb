class Hr::Teacher::TeachersController < SimpleController::BaseController
  defaults(
    resource_class: Teacher,
    collection_name: 'teachers',
    instance_name: 'teacher',
    view_path: 'teachers',
    importable_class: ::Teacher,
  )

  auth_action [:teacher, :student]

  def star
    current_auth.create_action(:star, target: resource)
    respond_with resource, template: "#{view_path}/show", status: 201
  end

  def unstar
    current_auth.destroy_action(:star, target: resource)
    respond_with resource, template: "#{view_path}/show", status: 201
  end

  def find_by_file
    xlsx_file = params[:file] || importable_class.import_excel_klass.new(params[:uid])
    resource_ids = importable_class.exchange_to_ids(xlsx_file, collection, **params.to_unsafe_h.symbolize_keys)
    @teachers = collection.where(id: resource_ids).paginate(page: 1, per_page: 999999)
    respond_with @teachers, template: "#{view_path}/index", status: 200
  end

  protected
    def begin_of_association_chain
      current_auth.school
    end

    def after_association_chain association
      if params[:action] == 'index'
        association = association.avaliable
      end
      # 如果当前用户是学生，则只查询当前学生所在学院的老师
      association.enabled
    end
end
