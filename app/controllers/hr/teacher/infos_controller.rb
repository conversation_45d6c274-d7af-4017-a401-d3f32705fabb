class Hr::Teacher::In<PERSON>sController < SimpleController::BaseController
  defaults(
    resource_class: Teacher,
    collection_name: 'teachers',
    instance_name: 'teacher',
    view_path: 'teachers'
  )

  auth_action :teacher

  def resource
    @teacher = current_teacher
    association = current_teacher.school.instances
    @instance_stat_count = {
      todo: association.ransack(todo: [current_teacher.id, current_teacher.class.base_class.to_s]).result.count,
      approving: association.ransack(approving: [current_teacher.id, current_teacher.class.base_class.to_s]).result.count,
      created: association.ransack(created: [current_teacher.id, current_teacher.class.base_class.to_s]).result.count,
      notified: association.ransack(notified: [current_teacher.id, current_teacher.class.base_class.to_s]).result.count
    }
  end

  private
    def teacher_params
      params.require(:info).permit(
        :name, :name_pinyin, :identity_id, :identity_type, :code, :birthday, :sex, :phone, :tel, :email, :department_id, :office, :address, :tag_list,
        :state, :to_school_at, :leave_school_at, :education, :degree, :work_way, :category, :place, :wage, :politics, :_duty_id, dynamic_attrs: {},
        teacher_titles_attributes: [:id, :_destroy, :title_category_id, :teacher_id, :country_title_id, :country_level, :country_auth_at, :teacher_title_id, :teacher_level, :teacher_auth_at, :school_title_id, :school_level, :school_auth_at],
        edus_attributes: [:_destroy, :id, :start_at, :end_at, :degree, :edu, :school, :major, :state, :body, :country],
        trains_attributes: [:_destroy, :id, :user_id, :user_type, :start_at, :end_at, :degree, :edu, :school, :major, :state, :body, :country, attachments: {}]
      )
    end
end
