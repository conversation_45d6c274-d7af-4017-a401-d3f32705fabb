class Hr::Admin::ExpertsController < SimpleController::BaseController
  defaults(
    resource_class: Expert,
    collection_name: 'experts',
    instance_name: 'expert',
    view_path: 'experts'
  )
  auth_action :teacher
  permit_action :hr_admin

  def activate
    resource.activate!
    head 201
  end

  def block
    resource.activate!
    head 201
  end

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def update_expert_params
      params.require(:expert).permit(
        :company, :bank, :bank_account, :duty, :remark, :sex, :identity_type, :identity_id
      )
    end

    def create_expert_params
      params.require(:expert).permit(
        :name, :phone, :company, :bank, :bank_account, :duty, :remark, :sex, :identity_type, :identity_id
      )
    end
end
