class Hr::Admin::ModificationsController < SimpleController::BaseController
  defaults(
    resource_class: Modification,
    collection_name: 'modifications',
    instance_name: 'modification',
    view_path: 'modifications'
  )

  auth_action :teacher
  permit_action :hr_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def modification_params
      params.require(:modification).permit(
        :state
      ).merge(audit_user: current_teacher)
    end
end
