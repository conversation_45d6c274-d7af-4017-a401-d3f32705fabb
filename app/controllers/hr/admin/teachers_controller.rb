class Hr::Admin::TeachersController < SimpleController::BaseController
  defaults(
    resource_class: ::Teacher,
    collection_name: 'teachers',
    instance_name: 'teacher',
    view_path: 'hr/teachers',
    exportable_class: ::Teacher,
  )

  auth_action :teacher
  permit_action :hr_admin

  protected

  def begin_of_association_chain
    current_teacher.school
  end

  def after_association_chain association
    relation = association.ransack(
      params[:q]&.except(
        :work_way_eq,
        :work_way_other,
        :state_eq,
      )
    ).result

    @statistics = relation.group(:work_way).count.merge(
      count: relation.count,
      other_count: relation.work_way_other.count,
      退休: relation.where(state: '退休').count,
    )
    association
  end

  private

  def teacher_params
    params.require(:teacher).permit(
      :name, :name_pinyin, :identity_id, :identity_type, :code, :birthday, :sex, :phone, :tel, :email, :department_id, :office, :address, :tag_list, :password_raw,
      :certificate_type, :expert_post_at,
      :state, :to_school_at, :leave_school_at, :education, :degree, :work_way, :category, :place, :wage, :politics, :_duty_id, dynamic_attrs: {}, title_ids: [], duty_ids: [], department_ids: [],
      teacher_titles_attributes: [:id, :_destroy, :title_category_id, :teacher_id, :country_title_id, :country_level, :country_auth_at, :teacher_title_id, :teacher_level, :teacher_auth_at, :school_title_id, :school_level, :school_auth_at],
      edus_attributes: [:_destroy, :id, :start_at, :end_at, :degree, :edu, :school, :major, :state, :body, :country],
      trains_attributes: [:_destroy, :id, :user_id, :user_type, :start_at, :end_at, :degree, :edu, :school, :major, :state, :body, :country, attachments: {}]
    )
  end
end
