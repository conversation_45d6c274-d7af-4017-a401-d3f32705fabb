class Hr::Student::TeachersController < SimpleController::BaseController
  defaults(
    resource_class: Teacher,
    collection_name: 'teachers',
    instance_name: 'teacher',
    view_path: 'teachers'
  )

  auth_action :student

  protected
    def begin_of_association_chain
      current_student.school
    end

    def after_association_chain association
      if params[:action] == 'index'
        # association = association.avaliable.ransack(departments_id_eq: current_student.department_id).result
        teacher_ids = Duty.find_by(name: '校领导')&.teachers&.pluck(:id)
        association = association.where.not(id: teacher_ids) if teacher_ids.count > 0
      end
      # 如果当前用户是学生，则只查询当前学生所在学院的老师
      association.enabled
    end
end
