class Hr::Student::<PERSON><PERSON>sController < SimpleController::BaseController
  defaults(
    resource_class: Student,
    collection_name: 'students',
    instance_name: 'student',
    view_path: 'students'
  )

  auth_action :student

  def resource
    @student = current_student
    association = current_student.school.instances
    @instance_stat_count = {
      todo: association.ransack(todo: [current_student.id, current_student.class.base_class.to_s]).result.count,
      approving: association.ransack(approving: [current_student.id, current_student.class.base_class.to_s]).result.count,
      created: association.ransack(created: [current_student.id, current_student.class.base_class.to_s]).result.count,
      notified: association.ransack(notified: [current_student.id, current_student.class.base_class.to_s]).result.count
    }
  end
end
