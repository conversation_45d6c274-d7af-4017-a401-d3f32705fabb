class Hr::Expert::InfosController < SimpleController::BaseController
  defaults(
    resource_class: Expert,
    collection_name: 'experts',
    instance_name: 'expert',
    view_path: 'experts'
  )

  auth_action :expert

  def resource
    @expert = current_expert
  end

  private
    def expert_params
      params.require(:expert).permit(
        :company, :bank, :bank_account, :duty, :remark
      )
    end
end
