class Netdisk::Admin::MenusController < SimpleController::BaseController
  defaults(
    resource_class: Netdisk::Menu,
    collection_name: 'menus',
    instance_name: 'menu',
    view_path: 'netdisk/menus'
  )

  auth_action :teacher
  permit_action :netdisk_admin

  protected

  def begin_of_association_chain
    current_teacher.school
  end

  def method_for_association_chain
    :netdisk_menus
  end

  private
    def menu_params
      params.require(:menu).permit(
        :name, :category
      )
    end
end
