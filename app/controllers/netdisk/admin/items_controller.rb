class Netdisk::Admin::ItemsController < SimpleController::BaseController
  defaults(
    resource_class: Netdisk::Item,
    collection_name: 'items',
    instance_name: 'item',
    view_path: 'netdisk/items'
  )

  auth_action :teacher
  permit_action :netdisk_admin

  belongs_to :netdisk_menus, param: :menu_id, optional: true

  def begin_of_association_chain
    current_auth.school
  end

  def method_for_association_chain
    :netdisk_items
  end

  def	after_association_chain association
    association.order(name: :asc)
  end

  private
    def item_params
      params.require(:item).permit(
        :creator_id, :creator_type, :type, :name, :desc, :target_type, :target_id, :parent_id, attachment: {}, shared_teacher_ids: [], manage_teacher_ids: []
      )
    end
end
