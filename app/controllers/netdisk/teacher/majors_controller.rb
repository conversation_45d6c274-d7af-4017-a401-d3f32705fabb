class Netdisk::Teacher::MajorsController < SimpleController::BaseController
  defaults(
    resource_class: Major,
    collection_name: 'majors',
    instance_name: 'major',
    view_path: 'netdisk/majors'
  )

  auth_action :teacher

  belongs_to :college, optional: true

  protected

  def begin_of_association_chain
    current_auth.has_role?('netdisk_admin') ?
      current_auth.school : current_auth
  end
end
