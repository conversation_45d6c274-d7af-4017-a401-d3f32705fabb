class Netdisk::Teacher::CourseSetsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::CourseSet,
    collection_name: 'course_sets',
    instance_name: 'course_set',
    view_path: 'netdisk/course_sets'
  )
  belongs_to :college, optional: true
  belongs_to :major, optional: true

  auth_action :teacher

  protected

  def begin_of_association_chain
    current_auth
  end

  def method_for_association_chain
    :course_sets
  end

end
