class Netdisk::Teacher::CourseDirsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::CourseDir,
    collection_name: 'course_dirs',
    instance_name: 'course_dir',
    view_path: 'netdisk/course_dirs'
  )

  belongs_to :major, optional: true

  auth_action :teacher

  protected

  def begin_of_association_chain
    current_auth.has_role?('netdisk_admin') ?
      current_auth.school : current_auth
  end

  def method_for_association_chain
    :course_dirs
  end
end
