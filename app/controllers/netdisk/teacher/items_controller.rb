class Netdisk::Teacher::ItemsController < SimpleController::BaseController
  defaults(
    resource_class: Netdisk::Item,
    collection_name: 'items',
    instance_name: 'item',
    view_path: 'netdisk/items'
  )

  auth_action :teacher

  belongs_to :college, optional: true
  belongs_to :course_set, optional: true
  belongs_to :course_dir, optional: true
  belongs_to :major, optional: true
  belongs_to :intl_activity, optional: true
  belongs_to :netdisk_menus, param: :menu_id, optional: true

  protected

  def begin_of_association_chain
    current_auth.has_role?('netdisk_admin') ?
      current_auth.school : current_auth
  end

  def method_for_association_chain
    :netdisk_items
  end

  def	after_association_chain association
    if params[:menu_id].present?
      if current_auth.has_any_role?(:netdisk_admin, :netdisk_view, :netdisk_operate)
        association.order(name: :asc)
      else
        associated_netdisk_items = current_auth.associated_netdisk_items.to_a
        ids = association.map do |record|
          associated_netdisk_items.any? do |item|
            item.id == record.id || # 同样的
              record.ancestor_ids.include?(item.id) ||
              item.ancestor_ids.include?(record.id)
          end ? record.id : nil
        end.compact
        association.where(id: ids).order(name: :asc)
      end
    else
      association.order(name: :asc)
    end
  end

  private
    def update_item_params
      params.require(:item).permit(
        :type, :name, :desc, :target_type, :target_id, :parent_id, attachment: {}, shared_teacher_ids: [], manage_teacher_ids: []
      )
    end

    def create_item_params
      update_item_params.merge(
        creator: current_auth
      )
    end
end
