class Netdisk::Shared::ItemsController < SimpleController::BaseController
  defaults(
    resource_class: Netdisk::Item,
    collection_name: 'items',
    instance_name: 'item',
    view_path: 'netdisk/items'
  )
  auth_action :teacher

  protected

  def begin_of_association_chain
    current_auth
  end

  def method_for_association_chain
    params[:q]&.[](:children_of).present? ?
      :school_netdisk_items : :associated_netdisk_items
  end

end
