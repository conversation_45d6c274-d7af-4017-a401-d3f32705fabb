class Netdisk::Owned::ItemsController < SimpleController::BaseController
  defaults(
    resource_class: Netdisk::Item,
    collection_name: 'items',
    instance_name: 'item',
    view_path: 'netdisk/items'
  )
  auth_action :teacher

  protected

  def begin_of_association_chain
    current_auth
  end

  def method_for_association_chain
    :netdisk_items
  end

  private
    def update_item_params
      params.require(:item).permit(
        :type, :name, :desc, :target_type, :target_id, :parent_id, :course_id, attachment: {}, shared_teacher_ids: [], manage_teacher_ids: []
      )
    end

    def create_item_params
      update_item_params.merge(
        creator: current_auth
      )
    end
end
