class Comm::User::ScoreScopesController < SimpleController::BaseController
  defaults(
    resource_class: ScoreScope,
    collection_name: 'score_scopes',
    instance_name: 'score_scope',
    view_path: 'score_scopes'
  )

  auth_action [:teacher, :student]

  protected
    def begin_of_association_chain
      current_auth
    end

  private
    def score_scope_params
      params.require(:score_scope).permit(
        :source_type, :source_id, :name, :weight, :flag, attachments: {}, meta: {}, files: []
      )
    end
end
