class Comm::User::ThesesController < SimpleController::BaseController
  defaults(
    resource_class: Thesis,
    collection_name: 'theses',
    instance_name: 'thesis',
    view_path: 'theses'
  )

  auth_action [:student, :teacher]

  def docx
    url = Thesis.convert(html: params[:html], title: params[:title])
    render json: { url: url}, status: 201
  end

  protected
    def begin_of_association_chain
      current_auth
    end

  private
    def thesis_params
      params.require(:thesis).permit(
        :source_type, :source_id, :type, :title, :body, :state, :template_id, :position, attachments: {}, meta: {}, files: [],
        thesis_infos_attributes: [:id, :repeat, :type, :title, :body, :state, :_destroy, :template_info_id, attachments: {}, meta: {}, files: []]
      ).merge(school: current_auth.school)
    end
end
