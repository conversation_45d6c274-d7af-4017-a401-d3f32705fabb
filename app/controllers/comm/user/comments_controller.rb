class Comm::User::CommentsController < SimpleController::BaseController
  defaults(
    resource_class: Comment,
    collection_name: 'comments',
    instance_name: 'comment',
    view_path: 'comments'
  )

  auth_action [:teacher, :student, :expert]

  protected
    def begin_of_association_chain
      params[:commentable].present? && params[:commentable_id] ?
        params[:commentable].classify.constantize.find(params[:commentable_id]) :
        current_auth
    end

  private
    def comment_params
      params.require(:comment).permit(
        :commentable_id, :commentable_type, :title, :body, :subject, :parent_id, attachments: {}
      )
    end
end
