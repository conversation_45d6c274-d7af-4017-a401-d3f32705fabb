class Comm::User::ReportsController < SimpleController::BaseController
  defaults(
    resource_class: Report,
    collection_name: 'reports',
    instance_name: 'report',
    view_path: 'reports'
  )

  auth_action [:student, :teacher]

  protected
    def begin_of_association_chain
      current_auth.school
    end

  private
    def create_report_params
      update_report_params.merge(
        school: current_auth.school,
        user: current_auth,
      )
    end

    def update_report_params
      params.require(:report).permit(
        :source_type, :source_id, :title, :body, :state, :flag, :type, :score, :end_at, meta: {}, attachments: {}, files: []
      )
    end
end
