class Comm::User::TopicsController < SimpleController::BaseController
  defaults(
    resource_class: Topic,
    collection_name: 'topics',
    instance_name: 'topic',
    view_path: 'topics'
  )

  auth_action [:teacher, :student]

  protected
    def begin_of_association_chain
      current_auth
    end

  private
    def topic_params
      params.require(:topic).permit(
        :source_type, :source_id, :type, :title, :body, :state, :cover_image, :view_permit, :reply_permit, :suggest_at, :visible_conf, meta: {}
      ).merge(school: current_auth.school)
    end
end
