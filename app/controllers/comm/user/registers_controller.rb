class Comm::User::RegistersController < SimpleController::BaseController
  defaults(
    resource_class: Register,
    collection_name: 'registers',
    instance_name: 'register',
    view_path: 'registers'
  )

  auth_action [:student, :teacher]

  def address
    data = Register.query_address(lon: params[:lon], lat: params[:lat], ip: params[:ip])
    render json: data, status: 201
  end

  protected

    def resource
      @register = Register.by_nonce(params[:nonce] || register_params[:nonce]).where(user: current_auth).first
      unless @register
        raise Error::BaseError.new(status: 404, message: '无效二维码')
      end
      return @register
    end

  private

    def register_params
      params.require(:register).permit(
        # 使用 nonce= , times 要在 nonce 前
        :times, :nonce, :type, :lon, :lat, :state
      )
    end

end
