class Comm::UserOwn::AnswerSetsController < SimpleController::BaseController
  defaults(
    resource_class: AnswerSet,
    collection_name: 'answer_sets',
    instance_name: 'answer_set',
    view_path: 'answer_sets'
  )

  auth_action [:teacher, :student]

  protected
    def begin_of_association_chain
      current_auth
    end

  private
    def answer_set_params
      params.require(:answer_set).permit(
        :type, :state, :question_set_id, :answerable_type, :answerable_id, meta: {}, answers_attributes: [:id, :value, :question_id, meta: {}]
      )
    end
end
