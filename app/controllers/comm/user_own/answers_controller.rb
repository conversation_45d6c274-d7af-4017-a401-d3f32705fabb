class Comm::UserOwn::AnswersController < SimpleController::BaseController
  defaults(
    resource_class: Answer,
    collection_name: 'answers',
    instance_name: 'answer',
    view_path: 'answers'
  )

  auth_action [:teacher, :student]

  belongs_to :answer_set

  protected
    def begin_of_association_chain
      current_auth
    end

  private
    def answer_params
      params.require(:answer).permit(
        :type, :question_id, :value, meta: {}, attachments: {}
      )
    end
end
