class Comm::UserOwn::QuestionsController < SimpleController::BaseController
  defaults(
    resource_class: Question,
    collection_name: 'questions',
    instance_name: 'question',
    view_path: 'questions'
  )
  auth_action :teacher

  belongs_to :question_set

  def batch_delete
    current_auth.question_sets.find(params[:question_set_id]).questions.where(id: question_batch_delete_params[:ids])&.destroy_all
    head 201
  end

  protected
    def begin_of_association_chain
      current_auth
    end

  private
    def question_params
      params.require(:question).permit(
        :type, :title, :position, choices: {}, answer_meta: {}, attachments: {}
      )
    end

    def question_batch_delete_params
      params.require(:question).permit(
        ids: []
      )
    end
end
