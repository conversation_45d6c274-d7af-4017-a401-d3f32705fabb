class Comm::UserOwn::QuestionSetsController < SimpleController::BaseController
  defaults(
    resource_class: QuestionSet,
    collection_name: 'question_sets',
    instance_name: 'question_set',
    view_path: 'question_sets'
  )

  auth_action :teacher

  protected
    def begin_of_association_chain
      current_auth
    end

  private
    def update_question_set_params
      params.require(:question_set).permit(
        :type, :name, :category, :meta, :questionable_type, :questionable_id, :answer_mode, meta: {}
      )
    end

    def create_question_set_params
      update_question_set_params.merge(
        creator: current_auth
      )
    end
end
