class Comm::Student::AnswerSetsController < SimpleController::BaseController
  defaults(
    resource_class: AnswerSet,
    collection_name: 'answer_sets',
    instance_name: 'answer_set',
    view_path: 'answer_sets'
  )

  auth_action :student

  protected
    def begin_of_association_chain
      current_student
    end

  private
    def update_answer_set_params
      params.require(:answer_set).permit(
        :question_set_id, :state, meta: {}, answers_attributes: [:id, :value, meta: {}]
      )
    end

    def create_answer_set_params
      update_answer_set_params.merge(
        creator: current_student,
      )
    end
end
