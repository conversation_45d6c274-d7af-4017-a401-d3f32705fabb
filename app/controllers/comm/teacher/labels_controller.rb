class Comm::Teacher::LabelsController < SimpleController::BaseController
  defaults(
    resource_class: Label,
    collection_name: 'labels',
    instance_name: 'label',
    view_path: 'labels'
  )

  auth_action :teacher

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def label_params
      params.require(:label).permit(
        :name, :type, :flag
      ).merge(teacher: current_teacher)
    end
end
