class Comm::Teacher::FormworksController < SimpleController::BaseController
  defaults(
    resource_class: Formwork,
    collection_name: 'formworks',
    instance_name: 'formwork',
    view_path: 'formworks'
  )

  auth_action :teacher

  protected
    def begin_of_association_chain
      current_teacher.school
    end

    def after_association_chain association
      association.active
    end
end
