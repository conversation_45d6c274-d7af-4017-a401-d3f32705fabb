class Comm::Teacher::CommentsController < SimpleController::BaseController
  defaults(
    resource_class: Comment,
    collection_name: 'comments',
    instance_name: 'comment',
    view_path: 'comments'
  )

  auth_action :teacher

  protected
    def begin_of_association_chain
      parent? ? nil : current_teacher
    end

  private
    def create_comment_params
      params.require(:comment).permit(
        :commentable_id, :commentable_type, :title, :body, :subject, attachments: {}
      ).merge(user: current_teacher)
    end

    def update_comment_params
      params.require(:comment).permit(
        :title, :body, :subject, attachments: {}
      )
    end
end
