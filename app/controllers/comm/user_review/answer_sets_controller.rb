class Comm::UserReview::AnswerSetsController < SimpleController::BaseController
  defaults(
    resource_class: AnswerSet,
    collection_name: 'answer_sets',
    instance_name: 'answer_set',
    view_path: 'answer_sets'
  )

  auth_action :teacher

  belongs_to :question_set

  protected
    def begin_of_association_chain
      current_teacher
    end

  private
    def answer_set_params
      params.require(:answer_set).permit(
        :state
      )
    end
end
