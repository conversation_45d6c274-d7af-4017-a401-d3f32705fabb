class Comm::UserReview::AnswersController < SimpleController::BaseController
  defaults(
    resource_class: Answer,
    collection_name: 'answers',
    instance_name: 'answer',
    view_path: 'answers'
  )

  auth_action :teacher

  belongs_to :question_set do
    belongs_to :answer_set
  end

  protected
    def begin_of_association_chain
      current_teacher
    end

  private
    def answer_params
      params.require(:answer).permit(
        :result_score
      )
    end
end
