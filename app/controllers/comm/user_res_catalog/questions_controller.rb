class Comm::UserResCatalog::QuestionsController < SimpleController::BaseController
  defaults(
    resource_class: Question,
    collection_name: 'questions',
    instance_name: 'question',
    view_path: 'questions'
  )

  auth_action :teacher

  belongs_to :question_catalog do
    belongs_to :question_set
  end

  protected
    def begin_of_association_chain
      current_auth
    end

  private
    def question_params
      params.require(:question).permit(
        :type, :title, :question_set_id, :position, choices: {}, answer_meta: {}, attachments: {}
      )
    end
end
