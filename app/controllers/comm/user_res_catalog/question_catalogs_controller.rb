class Comm::UserResCatalog::QuestionCatalogsController < SimpleController::BaseController
  defaults(
    resource_class: QuestionCatalog,
    collection_name: 'res_catalogs',
    instance_name: 'res_catalog',
    view_path: 'res_catalogs'
  )

  auth_action :teacher

  protected
    def begin_of_association_chain
      current_auth
    end

    def method_for_association_chain
      :question_catalogs
    end

  private
    def update_res_catalog_params
      params.require(:res_catalog).permit(
        :title, :view_permit, :edit_permit, :source_type, :source_id
      )
    end

    def create_res_catalog_params
      update_res_catalog_params.merge(
        type: 'QuestionCatalog',
        owner: current_auth,
      )
    end
end
