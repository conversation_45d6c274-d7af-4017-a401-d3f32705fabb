class Comm::UserResCatalog::QuestionSetsController < SimpleController::BaseController
  defaults(
    resource_class: QuestionSet,
    collection_name: 'question_sets',
    instance_name: 'question_set',
    view_path: 'question_sets'
  )

  auth_action :teacher

  belongs_to :question_catalog

  def copy
    @question_set = resource.clone
    @question_set.update! copy_question_set_params
    @question_set.reload.questionable&.touch
    respond_with @question_set, template: "#{view_path}/show", status: 201
  end

  # def create
  #   @question_set = current_auth.question_catalogs.find(params[:question_catalog_id]).question_sets.create(
  #     create_question_set_params
  #   )
  #   respond_with @question_set, template: "#{view_path}/show", status: 201
  # end

  protected
    def begin_of_association_chain
      current_auth
    end

    def method_for_association_build
      :create
    end

  private
    def update_question_set_params
      params.require(:question_set).permit(
        :type, :name, :category, :school_id, :answer_mode, meta: {}
      )
    end

    def create_question_set_params
      update_question_set_params.merge(
        creator: current_auth
      )
    end

    def copy_question_set_params
      params.require(:question_set).permit(
        :name, :questionable_type, :questionable_id, :answer_mode
      ).merge(
        creator: current_auth
      )
    end
end
