class Comm::Admin::TemplatesController < SimpleController::BaseController
  defaults(
    resource_class: Template,
    collection_name: 'templates',
    instance_name: 'template',
    view_path: 'templates'
  )

  auth_action :teacher

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def template_params
      params.require(:template).permit(
        :title, :type, :state, meta: {},
        template_infos_attributes: [:id, :_destroy, :title, :repeat, :maxinput, :mininput, :position, :state, meta: {}]
      ).merge(teacher: current_teacher)
    end
end
