class Comm::Admin::ScoreTemplatesController < SimpleController::BaseController
  defaults(
    resource_class: ScoreTemplate,
    collection_name: 'score_templates',
    instance_name: 'score_template',
    view_path: 'score_templates'
  )
  auth_action :teacher
  permit_action :assessment_admin

  private
    def update_score_template_params
      params.require(:score_template).permit(
        :type, :name, :desc, form: {}
      )
    end

    def create_score_template_params
      update_score_template_params.merge(
        school: current_auth.school,
        user: current_auth,
      )
    end
end
