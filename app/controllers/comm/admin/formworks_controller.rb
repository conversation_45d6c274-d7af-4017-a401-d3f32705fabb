class Comm::Admin::FormworksController < SimpleController::BaseController
  defaults(
    resource_class: Formwork,
    collection_name: 'formworks',
    instance_name: 'formwork',
    view_path: 'formworks'
  )

  auth_action :teacher
  # permit_action :comm_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

    def after_association_chain association
      association.active
    end

  private
    def formwork_params
      params.require(:formwork).permit!.merge(teacher: current_teacher)
    end
end
