class Comm::Admin::DynamicColumnsController < SimpleController::BaseController
  defaults(
    resource_class: DynamicColumn,
    collection_name: 'dynamic_columns',
    instance_name: 'dynamic_column',
    view_path: 'dynamic_columns'
  )

  auth_action :teacher

  private

  def begin_of_association_chain
    current_teacher.school
  end

  def dynamic_column_params
    params.require(:dynamic_column).permit(
      :model, :column, :formable_type, :formable_id,
      form: {}
    )
  end
end
