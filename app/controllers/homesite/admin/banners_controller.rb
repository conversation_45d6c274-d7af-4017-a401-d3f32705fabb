class Homesite::Admin::BannersController < SimpleController::BaseController
  defaults(
    resource_class: Homesite::Banner,
    collection_name: 'banners',
    instance_name: 'banner',
    view_path: 'homesite/banners'
  )

  auth_action :teacher # 身份认证
  permit_action :portal_admin # 权限认证

  protected

  def begin_of_association_chain
    current_teacher.school
  end

  # 定义查询关系的名称
  # current_teacher.school.homesite_banners
  def method_for_association_chain
    :homesite_banners
  end

  def after_association_chain association
    relation = association.ransack(params[:q]&.except(:state_eq, :state_in)).result
    @state_count = relation.group(:state).count.merge(
      count: relation.count
    )
    association.order(position: :asc)
  end

  private
    def banner_params
      params.require(:banner).permit(
        :title, :content, :position, :state, :banner_type, :url, image: {}
      )
    end
end
