class Homesite::BannersController < SimpleController::BaseController
  defaults(
    resource_class: Homesite::Banner,
    collection_name: 'banners',
    instance_name: 'banner',
    view_path: 'homesite/banners'
  )

  belongs_to :school

  protected

  def method_for_association_chain
    :homesite_banners
  end

  def after_association_chain association
    association.published.order(position: :asc)
  end
end
