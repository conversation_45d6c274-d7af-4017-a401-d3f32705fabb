class Wechat::Admin::AppsController < SimpleController::BaseController
  defaults(
    resource_class: Wechat::App,
    collection_name: 'wechat_apps',
    instance_name: 'wechat_app',
    view_path: 'wechat/apps',
    ransack_off: true,
    paginate_off: true,
  )

  auth_action :teacher
  permit_action :bpm_admin

  def collection
    index_params = {
      q: {
        school_id_eq: current_auth.school_id,
      }
    }
    index_params.merge(page: params[:page]) if params[:page].present?
    index_params.merge(per_page: params[:per_page]) if params[:per_page].present?

    @wechat_apps = Wechat::App.all(
      params: index_params
    )
  end

  def create
    @wechat_app = Wechat::App.create(wechat_app_params)
    respond_with @wechat_app, template: "#{view_path}/show", status: 201
  end

  def update
    @wechat_app = Wechat::App.find(params[:id])
    @wechat_app.update(wechat_app_params)
    head 204
  end

  def destroy
    @wechat_app = Wechat::App.find(params[:id])
    @wechat_app.destroy
    head 204
  end

  private
    def wechat_app_params
      params.require(:wechat_app).permit(
        :appid, :secret, :name, :desc
      ).merge(
        school_id: current_auth.school_id
      )
    end
end
