class Wechat::Teacher::ArticlesController < SimpleController::BaseController
  defaults(
    resource_class: Wechat::Article,
    collection_name: 'wechat_articles',
    instance_name: 'wechat_article',
    view_path: 'wechat/articles'
  )
  auth_action :teacher, except: [:show]

  def index
    wechat_app_id = params[:app_id]
    url = ENV['WEIXIN_URL'] + "/#{wechat_app_id}/wechat/articles"
    page = (params[:page] || 1).to_i
    per_page = (params[:per_page] || 10).to_i
    offset = (page - 1) * per_page
    count = per_page
    response = JSON.parse(
      Typhoeus.get(
        url,
        params: { offset: offset, count: count},
        headers: { Accept: 'application/json' },
      ).body
    )
    total_count = response['total_count'] || 0
    total_pages = total_count / per_page + 1
    render json: { current_page: page, total_pages: total_pages, total_count: total_count}.merge(response)
  end

  def show
    wechat_app_id = params[:app_id]
    media_id = params[:id]
    image = params[:image].to_i
    # Image
    if image == 1
      url = ENV['WEIXIN_URL'] + "/#{wechat_app_id}/wechat/image_media"
      body = Typhoeus.get(
        url,
        headers: { Accept: 'application/json' },
        params: { media_id: media_id },
      ).body
      render json: body
    else
      url = ENV['WEIXIN_URL'] + "/#{wechat_app_id}/wechat/article_media"
      body = Typhoeus.get(
        url,
        headers: { Accept: 'application/json' },
        params: { media_id: media_id },
      ).body
      render json: JSON.parse(body)
    end
  end

  def update
    wechat_app_id = params[:app_id]
    url = ENV['WEIXIN_URL'] + "/#{wechat_app_id}/wechat/article_update"
    body = Typhoeus.post(
      url,
      body: article_params.to_h,
      headers: { Accept: 'application/json' },
    ).body
    render json: JSON.parse(body)
  end

  private
    def article_params
      params.require(:article).permit(
        :media_id, :index, articles: [:title, :thumb_media_id, :author, :digest, :show_cover_pic, :content, :content_source_url]
      )
    end
end
