class Fund::Teacher::OutsidePaymentsController < SimpleController::BaseController
  defaults(
    resource_class: StieiFund::OutsidePayment,
    collection_name: 'outside_payments',
    instance_name: 'outside_payment',
    view_path: 'stiei_fund/outside_payments'
  )

  auth_action :teacher

  belongs_to :routine_base, parent_class: StieiFund::RoutineBase, param: :routine_basis_id,  optional: true
  belongs_to :routine_budget, parent_class: StieiFund::RoutineBudget, optional: true
  belongs_to :routine_third, parent_class: StieiFund::RoutineThird, optional: true

  def fire
    event = fire_params[:event]
    resource.aasm.fire!(event.to_sym, event: event, checker: current_teacher.code, opinion: fire_params[:opinion])
    head 201
  end

  def export
    render json: { url: StieiFund::OutsidePayment.export(collection) }
  end

  private
    def fire_params
      params.require(:payment).permit(
        :event, :opinion
      )
    end
end
