class Fund::Teacher::R<PERSON>inePaymentsController < SimpleController::BaseController
  defaults(
    resource_class: StieiFund::RoutinePayment,
    collection_name: 'routine_payments',
    instance_name: 'routine_payment',
    view_path: 'stiei_fund/routine_payments'
  )

  auth_action :teacher

  belongs_to :routine_base, parent_class: StieiFund::RoutineBase, param: :routine_basis_id,  optional: true
  belongs_to :routine_budget, parent_class: StieiFund::RoutineBudget, optional: true
  belongs_to :routine_third, parent_class: StieiFund::RoutineThird, optional: true

  def fire
    event = fire_params[:event]
    resource.aasm.fire!(event.to_sym, event: event, checker: current_teacher.code, opinion: fire_params[:opinion])
    head 201
  end

  def export
    render json: { url: StieiFund::RoutinePayment.export(collection) }
  end

  private
    def fire_params
      params.require(:payment).permit(
        :event, :opinion
      )
    end
end
