class Fund::Teacher::<PERSON><PERSON><PERSON><PERSON><PERSON>ineBudgetsController < SimpleController::BaseController
  defaults(
    resource_class: StieiFund::RoutineBudget,
    collection_name: 'routine_budgets',
    instance_name: 'routine_budget',
    view_path: 'stiei_fund/routine_budgets'
  )

  auth_action :teacher

  protected
    def end_of_association_chain
      current_teacher.execute_routine_budgets
    end
end
