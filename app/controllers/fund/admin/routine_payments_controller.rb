class Fund::Admin::RoutinePaymentsController < SimpleController::BaseController
  defaults(
    resource_class: StieiFund::RoutinePayment,
    collection_name: 'routine_payments',
    instance_name: 'routine_payment',
    view_path: 'stiei_fund/routine_payments'
  )

  auth_action :teacher
  permit_action :fund_admin

  belongs_to :routine_base, parent_class: StieiFund::RoutineBase, param: :routine_basis_id,  optional: true
  belongs_to :routine_budget, parent_class: StieiFund::RoutineBudget, optional: true
  belongs_to :routine_third, parent_class: StieiFund::RoutineThird, optional: true

  def export
    render json: { url: StieiFund::RoutinePayment.export(collection) }
  end
end
