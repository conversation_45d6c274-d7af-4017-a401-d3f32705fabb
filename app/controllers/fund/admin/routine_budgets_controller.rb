class Fund::Admin::RoutineBudgetsController < SimpleController::BaseController
  defaults(
    resource_class: StieiFund::RoutineBudget,
    collection_name: 'routine_budgets',
    instance_name: 'routine_budget',
    view_path: 'stiei_fund/routine_budgets'
  )

  auth_action :teacher
  permit_action :fund_admin

  belongs_to :routine_base, parent_class: StieiFund::RoutineBase, param: :routine_basis_id,  optional: true
end
