class Fund::Admin::OutsidePaymentsController < SimpleController::BaseController
  defaults(
    resource_class: StieiFund::OutsidePayment,
    collection_name: 'outside_payments',
    instance_name: 'outside_payment',
    view_path: 'stiei_fund/outside_payments'
  )

  auth_action :teacher
  permit_action :fund_admin

  belongs_to :routine_base, parent_class: StieiFund::RoutineBase, param: :routine_basis_id,  optional: true
  belongs_to :routine_budget, parent_class: StieiFund::RoutineBudget, optional: true
  belongs_to :routine_third, parent_class: StieiFund::RoutineThird, optional: true

  def export
    render json: { url: StieiFund::OutsidePayment.export(collection) }
  end
end
