class Meeting::User::AttendancesController < SimpleController::BaseController
  defaults(
    resource_class: Meeting::Attendance,
    collection_name: 'meeting_attendances',
    instance_name: 'meeting_attendance',
    view_path: 'meeting/attendances'
  )

  auth_action [:teacher, :student]

  def cancel
    resource.fire(:cancel)
    respond_with @meeting_attendance, template: "#{view_path}/show", status: 201
  end

  protected

  def resource
    @meeting_attendance = params[:activity_id].present? ?
      end_of_association_chain&.first :
      super
  end

  def begin_of_association_chain
    current_auth
  end

  def end_of_association_chain
    params[:activity_id].present? ?
      Meeting::Activity.find(params[:activity_id]).attendances.where(user: current_auth) :
      super
  end


  private
    def meeting_attendance_params
      { user: current_auth }
    end
end
