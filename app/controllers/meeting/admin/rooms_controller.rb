class Meeting::Admin::RoomsController < SimpleController::BaseController
  defaults(
    resource_class: Meeting::Room,
    collection_name: 'meeting_rooms',
    instance_name: 'meeting_room',
    view_path: 'meeting/rooms'
  )

  auth_action :teacher
  permit_action :conference_admin

  private

  def begin_of_association_chain
    current_teacher.school
  end

  def meeting_room_params
    params.require(:meeting_room).permit(
      :building, :name, :state, meta: {}
    )
  end
end
