class Meeting::Admin::TopicsController < SimpleController::BaseController
  defaults(
    resource_class: Meeting::Topic,
    collection_name: 'topics',
    instance_name: 'topic',
    view_path: 'meeting/topics'
  )
  auth_action :teacher
  permit_action :meeting_admin

  protected

  def	begin_of_association_chain
    current_teacher.school
  end

  def	method_for_association_chain
    :meeting_topics
  end

  private
    def topic_params
      params.require(:topic).permit(
        :title, :catalog, :desc, :comment, :state, :execute_user_id, :report_user_id, :completed_at
      )
    end
end
