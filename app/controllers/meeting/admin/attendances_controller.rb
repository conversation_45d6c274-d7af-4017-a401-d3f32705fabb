class Meeting::Admin::AttendancesController < SimpleController::BaseController
  defaults(
    resource_class: Meeting::Attendance,
    collection_name: 'meeting_attendances',
    instance_name: 'meeting_attendance',
    view_path: 'meeting/attendances'
  )

  belongs_to :meeting_activity, param: :activity_id, optional: true

  auth_action :teacher
  permit_action :conference_admin, :meeting_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def meeting_attendance_params
      params.require(:meeting_attendance).permit(
        :user_type, :user_id, :state, meta: {}
      )
    end
end
