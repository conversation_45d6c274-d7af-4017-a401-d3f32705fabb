class Meeting::Admin::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Meeting::Activity,
    collection_name: 'meeting_activities',
    instance_name: 'meeting_activity',
    view_path: 'meeting/activities/admin'
  )

  auth_action :teacher
  permit_action :conference_admin, :meeting_admin

  def export
    render json: { url: MeetingActivityService.new(id: resource.id).export }
  end

  def qrcode
    @meeting_attendance = resource.qrcode_signup qr_code_params[:qrcode]
    respond_with @meeting_attendance, template: 'meeting/attendances/show', status: 201
  end

  protected
    def begin_of_association_chain
      current_teacher
    end

    def method_for_association_chain
      :reserve_meeting_activities
    end

  private
    def create_meeting_activity_params
      update_meeting_activity_params.merge(
        reserver: current_teacher,
        school: current_teacher.school,
      )
    end

    def update_meeting_activity_params
      params.require(:meeting_activity).permit(
        :date, :title, :desc, :meeting_room_id, :begin_time, :end_time, :reserver_type, :reserver_id,
        :type, :location, :published, :limit_count,
        :user_desc, :custom_meeting_room_name,
        teacher_ids: [], student_ids: [], meta: {}
      )
    end

    def qr_code_params
      params.require(:meeting_activity).permit(
        :qrcode
      )
    end
end
