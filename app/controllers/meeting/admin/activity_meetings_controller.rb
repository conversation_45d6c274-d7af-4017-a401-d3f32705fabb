class Meeting::Admin::ActivityMeetingsController < SimpleController::BaseController
  defaults(
    resource_class: Meeting::ActivityMeeting,
    collection_name: 'activity_meetings',
    instance_name: 'activity_meeting',
    view_path: 'meeting/activity_meetings'
  )

  auth_action :teacher
  permit_action :conference_admin

  private

  def after_association_chain association
    relation = association.ransack(params[:q]&.except(:pending_review, :state_eq, :state_in)).result
    @statistics = {
      unreviewed: relation.pending_review.count,
      locked: relation.where(state: 'reviewed').count,
    }
    association
  end

  def activity_meeting_params
    params.require(:activity_meeting).permit(
      :state,
      :date, :title, :desc, :meeting_room_id,
      :begin_time, :end_time, :reserver_type, :reserver_id,
      :type, :location, :school_id, :published, :limit_count,
      :user_desc,
      :custom_meeting_room_name,
      moderator_ids: [], organizer_ids: [], user_ids: [], meeting_department_ids: [],
      meta: {}
    )
  end

  protected

  def begin_of_association_chain
    current_teacher.school
  end
end
