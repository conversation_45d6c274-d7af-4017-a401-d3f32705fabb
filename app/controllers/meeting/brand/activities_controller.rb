class Meeting::Brand::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Meeting::Activity,
    collection_name: 'meeting_activities',
    instance_name: 'meeting_activity',
    view_path: 'meeting/activities'
  )

  def qrcode
    @meeting_attendance = resource.qrcode_signup meeting_activity_params[:qrcode]
    respond_with @meeting_attendance, template: 'meeting/attendances/show', status: 201
  end

  private
    def meeting_activity_params
      params.require(:meeting_activity).permit(
        :qrcode, :card
      )
    end
end
