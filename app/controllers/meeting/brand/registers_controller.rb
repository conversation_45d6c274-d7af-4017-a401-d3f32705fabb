class Meeting::Brand::RegistersController < SimpleController::BaseController
  defaults(
    resource_class: Meeting::ActivityMeeting::Register,
    collection_name: 'registers',
    instance_name: 'register',
    view_path: 'meeting/registers'
  )

  def sign_in
    user_info = parent.qrcode_get_user(sign_in_params[:qrcode])
    @user = user_info['type'].safe_constantize.find_by_code!(user_info['code'])
    @register = end_of_association_chain.where(state: ['unreviewed', 'reviewed', 'done']).where(user: @user).first
    unless @register
      raise ::Error::BaseError.new(message: '未参加本次会议')
    end

    if @register && @register.state === 'done'
      raise ::Error::BaseError.new(message: '您已签到过了哦')
    end

    @register.update!(sign_in_params)
    render json: {
      id: @register.id,
      user_name: @user.name,
      user_code: @user.code,
      signup_at: Time.now,
      state: '已签到',
    }
  end

  private

  def sign_in_params
    params.require(:register).permit(
      :qrcode
    )
  end

  protected

  def end_of_association_chain
    parent.registers
  end

  def parent
    Meeting::ActivityMeeting.find(params[:activity_meeting_id])
  end
end
