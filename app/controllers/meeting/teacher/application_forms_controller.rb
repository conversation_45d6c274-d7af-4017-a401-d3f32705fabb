class Meeting::Teacher::ApplicationFormsController < SimpleController::BaseController
  defaults(
    resource_class: Meeting::ApplicationForm,
    collection_name: 'application_forms',
    instance_name: 'application_form',
    view_path: 'meeting/application_forms'
  )
  
  auth_action :teacher

  private

  def application_form_params
    params.require(:application_form).permit(
      :backup_user_type, :backup_reason, 
      :register_id, backup_user_ids: []
    ).merge(register_id: parent.registers.find_by(user: current_teacher)&.id)
  end

  protected

  def end_of_association_chain
    parent.application_forms
  end

  def parent
    current_teacher.school.meeting_activity_meetings.find(params[:activity_meeting_id])
  end
end
