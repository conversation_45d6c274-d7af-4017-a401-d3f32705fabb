class Meeting::Teacher::ActivityMeetingsController < SimpleController::BaseController
  defaults(
    resource_class: Meeting::ActivityMeeting,
    collection_name: 'activity_meetings',
    instance_name: 'activity_meeting',
    view_path: 'meeting/activity_meetings'
  )

  auth_action :teacher
  before_action :valid_update, only: :update

  def state_stat
    association = current_teacher.reserve_meetings
    render json: {
      successful: association.where(state: ['unreviewed', 'reviewed']).count,
      draft: association.where(state: 'draft').count,
      canceled: association.where(state: 'canceled').count,
    }
  end

  def stat
    render json: {
      member_count: resource.users_count,
      leaving_count: resource.application_forms.where(backup_reason: '请假', state: 'completed').count,
      unreviewed_count: resource.application_forms.where(state: 'processing').count,
    }
  end

  private

  def valid_update
    unless resource.is_moderator?(current_auth) || resource.is_organizer?(current_auth) || resource.reserver === current_auth || current_auth.has_role?('conference_admin')
      raise Error::BaseError.new(message: '没有修改权限')
    end
  end

  def activity_meeting_params
    result = params.require(:activity_meeting).permit(
      :date, :title, :desc, :meeting_room_id,
      :begin_time, :end_time, :reserver_type, :reserver_id,
      :type, :location, :school_id, :published, :limit_count,
      :user_desc, :custom_meeting_room_name,
      moderator_ids: [], organizer_ids: [], user_ids: [],
      meta: {}, meeting_department_ids: [],
    )
    state = params.require(:activity_meeting)[:state]
    if state.in?(['pending', 'draft', 'canceled'])
      result = result.merge(state: state)
    end
    result
  end

  protected

  def begin_of_association_chain
    current_teacher.school
  end

  def after_association_chain association
    relation = association.ransack(params[:q]&.except(:state_eq, :state_in)).result
    @statistics = {
      successful: relation.where(state: ['unreviewed', 'reviewed']).count,
      draft: relation.where(state: 'draft').count,
      canceled: relation.where(state: 'canceled').count,
    }
    association.joined_with(params[:joined] && current_teacher)
  end
end
