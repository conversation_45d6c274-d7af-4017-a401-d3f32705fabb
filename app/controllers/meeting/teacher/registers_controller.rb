class Meeting::Teacher::RegistersController < SimpleController::BaseController
  defaults(
    resource_class: Meeting::ActivityMeeting::Register,
    collection_name: 'registers',
    instance_name: 'register',
    view_path: 'meeting/registers'
  )

  auth_action :teacher

  before_action :have_register_start_auth?, only: :sign_in

  # belongs_to :meeting_activity_meeting, route_name: :activity_meeting

  def batch_create
    ActiveRecord::Base.transaction do
      params[:teacher_ids].each do |teacher_id|
        end_of_association_chain.where(user: Teacher.find(teacher_id)).create!(
          register_params.merge(state: 'reviewed')
        )
      end
    end
    head 201
  end

  def sign_in
    user_info = parent.qrcode_get_user(sign_in_params[:qrcode])
    @user = user_info['type'].safe_constantize.find_by_code!(user_info['code'])
    @register = end_of_association_chain.where(state: ['unreviewed', 'reviewed', 'done']).where(user: @user).first
    unless @register
      raise ::Error::BaseError.new(message: '未参加本次会议')
    end

    if @register && @register.state === 'done'
      raise ::Error::BaseError.new(message: '您已签到过了哦')
    end

    @register.update!(sign_in_params)
    render json: {
      id: @register.id,
      user_name: @user.name,
      user_code: @user.code,
      signup_at: Time.now,
      state: '已签到',
    }
  end

  private

  def register_params
    params.require(:register).permit(
      :user_id, :user_type, meta: {}
    )
  end

  def sign_in_params
    params.require(:register).permit(
      :qrcode
    )
  end

  def have_register_start_auth?
    parent.have_register_start_auth?(current_auth)
  end

  protected

  def end_of_association_chain
    parent.registers
  end

  def parent
    current_teacher.school.meeting_activity_meetings.find(params[:activity_meeting_id])
  end
end
