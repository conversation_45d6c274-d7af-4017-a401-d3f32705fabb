class Oauth2::ProfilesController < SimpleController::BaseController
  respond_to :json

  include AuthAction
  before_action :load_oauth_access_token_resource

  def show!
    render json: {}, status: :unauthorized and return if @access_token_resource.blank?

    @resource = load_auth @access_token_resource
    respond_with @resource, template: "oauth2/profiles/show"
  end

  private

  def load_oauth_access_token_resource
    bearer_token = request.env['HTTP_AUTHORIZATION']
    token_info_uri = "#{ENV['AUTH_URL']}/auth/session/check_oauth_bearer_token"

    # 请求体结构示例
    # {
    #     "code": "2007****",
    #     "name": "****",
    #     "email": "****@stiei.edu.cn",
    #     "tel": null,
    #     "mobile": "*****",
    #     "id_card": "*****",
    #     "type": "Teacher",
    #     "school": 1
    # }
    token_info_response = RestClient.post(token_info_uri, nil, { Authorization: bearer_token })

    @access_token_resource = JSON.parse(token_info_response.body).with_indifferent_access
  rescue Exception => e
    logger.error "oauth2 获取用户信息失败\n#{e}"
    @access_token_resource = nil
  end
end