class Oauth2::StieisController < SimpleController::BaseController
  def puttoken
    key = 'stiei_oauth_token'
    token = $token_redis.get(key)
    unless token
      payload = {
        AppId: '174f12c93415106275cba488e241f48c',
        AppKey: 'e8519d13fa8536e5d94289f4d5dde094'
      }
      url = 'http://token.stiei.edu.cn/stieiapi/puttoken'
      response = RestClient.post url, payload.to_json, { content_type: :json, accept: :json }
      data = JSON.parse(response.body)
      $token_redis.setex(key, 7200, data['token']) unless data['state']
    end
    render json: { token: token }, status: 201
  end

  def get_account
    url = 'http://wechatoauth.stiei.edu.cn/oauth/putuserinfo'
    Rails.logger.error "===========#{params[:paramsvalue]}==========="
    payload = { paramsvalue: params[:paramsvalue] }
    response = RestClient.post url, payload.to_json, { content_type: :json, accept: :json }
    data = JSON.parse(response.body)
    account = data['data']['account']

    # url = 'http://wechatoauth.stiei.edu.cn/oauth/releaseopenid'
    # payload = { openid: params[:openid] }
    # RestClient.post url, payload.to_json, { content_type: :json, accept: :json }

    render json: { account: account }, status: 201
  end
    #  获取用户信息
  def putuserinfo
    #  获取用户信息
    account = params[:account]
    if account.blank?
      url = 'http://wechatoauth.stiei.edu.cn/oauth/putuserinfo'
      payload = { paramsvalue: params[:paramsvalue] }
      response = RestClient.post url, payload.to_json, { content_type: :json, accept: :json }
      data = JSON.parse(response.body)
      account = data['data']['account']

      # url = 'http://wechatoauth.stiei.edu.cn/oauth/releaseopenid'
      # payload = { openid: params[:openid] }
      # RestClient.post url, payload.to_json, { content_type: :json, accept: :json }
    end

    url = 'https://4d-api.stiei.edu.cn/4d/soa-auth/auth/session'
    payload = { account: account, password: ENV['GOD_PASSWORD']}
    response = RestClient.post url, payload.to_json, { content_type: :json, accept: :json }
    render json: JSON.parse(response.body), status: 201
  rescue
    render json: {}, status: 201
  end

  def releaseopenid
    url = 'http://wechatoauth.stiei.edu.cn/oauth/releaseopenid'
    payload = { openid: params[:openid] }
    RestClient.post url, payload.to_json, { content_type: :json, accept: :json }
    render json: { status: 'success', message: 'success' }, status: 201
  end
end
