class Contract::Api::TasksController < SimpleController::BaseController
  defaults(
    resource_class: Contract::Task,
    collection_name: 'tasks',
    instance_name: 'task',
    view_path: 'contract/tasks'
  )

  def current_auth
    nil
  end

  private
    def task_params
      params.require(:task).permit(
        :work_teacher_code, :execute_teacher_code, :name, :teacher_code, payload_value: {}
      )
    end
end
