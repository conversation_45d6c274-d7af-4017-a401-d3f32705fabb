class Contract::Admin::TasksController < SimpleController::BaseController
  defaults(
    resource_class: Contract::Task,
    collection_name: 'tasks',
    instance_name: 'task',
    view_path: 'contract/tasks'
  )
  auth_action :teacher
  permit_action :contract_admin

  protected

  def	after_association_chain association
    association.where(school: current_teacher.school)
  end

  private
    def task_params
      params.require(:task).permit(
        :name, :execute_teacher_id, :work_teacher_id, :teacher_id, payload_value: {}
      )
    end
end
