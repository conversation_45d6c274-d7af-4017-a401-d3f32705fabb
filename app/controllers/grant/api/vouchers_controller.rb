class Grant::Api::VouchersController < Grant::Api::BaseController
  impressionist actions: [:create, :notify, :reject]

  def create
    obj = JSON.parse(decrypt(params['data']))
    instance = Bpm::Instance.find_by(seq: obj['orderNo'])
    if instance && instance.flowable && instance.flowable.is_a?(Finance::Voucher)
      if obj['status'].to_i == 1 && !instance.tokens.find_by(operator: Teacher.find(ENV['AUTO_ROBOT_ID']), state: 'processing')
        return render json: { code: 0, message: '未到收件流程' }
      end

      # 退件后不能投递
      if obj['status'].to_i == 1 && instance.flag.to_s == 'rejected'
        return render json: { code: 0, message: '退件后再次收件请走线下流程' }
      end

      # 用户投递后不能重复投递
      token = instance.tokens.find_by(operator: Teacher.find(ENV['AUTO_ROBOT_ID']), state: 'completed')
      if obj['status'].to_i != 2 && token && instance.flag.to_s == 'rejected'
        return render json: { code: 0, message: '单据不能重复投递' }
      end

      voucher = instance.flowable
      return render json: {
        code: 1,
        message: 'ok',
        data: {
          orderNo: obj['orderNo'],
          applicationNo: voucher.teacher&.code,
          applicationName: voucher.teacher&.name,
          orderDesc: voucher.remark,
          expenseType: voucher.type_zh,
          applyTime: voucher.created_at.strftime('%F %T'),
          applyStatus: voucher.state_zh
        }
      }
    else
      return render json: { code: 0, message: '单据不存在' }
    end
  rescue
    return render json: { code: 0, message: '解码失败' }
  end

  # 收件成功
  def notify
    obj = JSON.parse(decrypt(params['data']))
    instance = Bpm::Instance.find_by(seq: obj['orderNo'])
    if instance && instance.flowable && instance.flowable.is_a?(Finance::Voucher)
      # 如果是收过件退回后就不让收件了
      if instance.flag.to_s == 'rejected'
        return render json: { code: 0, message: '重复投递或者退件再次收件请走线下流程' }
      end

      token = instance.tokens.find_by(operator: Teacher.find(ENV['AUTO_ROBOT_ID']), state: 'processing')
      return render json: { code: 0, messgae: '未到收件流程或已经收件' } unless token
      token.fire! action: 'accept'
      # 收件后不能重复投递标识
      instance.update(flag: 'rejected')
      return render json: { code: 1, message: '收件成功' }
    else
      return render json: { code: 0, message: '单据不存在' }
    end
  rescue
    return render json: { code: 0, message: '解码失败' }
  end

  def reject
    obj = JSON.parse(decrypt(params['data']))
    instance = Bpm::Instance.find_by(seq: obj['orderNo'])
    if instance && (voucher = instance.flowable) && voucher.is_a?(Finance::Voucher) && voucher.rejected?
      creator = instance.creator
      Wechat::TemplateMessage.create(
        school: creator.school,
        app_id: 'wxb1daea392d68c3f5',
        receiver: creator,
        notifyable: instance,
        message: {
          template_id: 'YMFWHgdaTIr8grKr3KyMZVzP4FQPHEuiq57OkGeIOVo',
          url: "#{ENV['MOBILE_URL']}/bpm/instances/#{instance.id}",
          topcolor: '#FF0000',
          data: {
            keyword1: {
              value: "退件通知 取件码: #{obj['pickCode']}",
            },
            keyword2: {
              value: instance.workflow_name
            },
            keyword3: {
              value: instance.creator_name
            },
            keyword4: {
              value: instance.created_at.strftime('%Y-%m-%d %H:%M')
            },
          }
        }
      )
      # 获取上一次锁定
      token = instance.tokens.first
      if token
        comment = token.comment.to_s + "【取件码: #{obj['pickCode']},设备名称: #{obj['clientName']},设备安放位置: #{obj['placePosition']},箱号名称: #{obj['counterName']},日期: #{Time.now.strftime('%F')}】"
        token.update_columns(comment: comment)
      end
      return render json: { code: 1, message: 'success' }
    else
      return render json: { code: 0, message: '单据不存在或者不在退件状态' }
    end
  rescue
    return render json: { code: 0, message: '解码失败' }
  end

  def message
    obj = JSON.parse(decrypt(params['data']))
    instance = Bpm::Instance.find_by(seq: obj['orderNo'])
    if instance && instance.flowable && instance.flowable.is_a?(Finance::Voucher)
      creator = instance.creator

      case obj['status'].to_i
        when 1
          Wechat::TemplateMessage.create(
            school: creator.school,
            app_id: 'wxb1daea392d68c3f5',
            receiver: creator,
            notifyable: instance,
            message: {
              template_id: 'YMFWHgdaTIr8grKr3KyMZVzP4FQPHEuiq57OkGeIOVo',
              url: "#{ENV['MOBILE_URL']}/bpm/instances/#{instance.id}",
              topcolor: '#FF0000',
              data: {
                keyword1: {
                  value: "领取单据通知"
                },
                keyword2: {
                  value: instance.workflow_name
                },
                keyword3: {
                  value: instance.creator_name
                },
                keyword4: {
                  value: instance.created_at.strftime('%Y-%m-%d %H:%M')
                },
              }
            }
          )
        when 2
          Wechat::TemplateMessage.create(
            school: creator.school,
            app_id: 'wxb1daea392d68c3f5',
            receiver: creator,
            notifyable: instance,
            message: {
              template_id: 'YMFWHgdaTIr8grKr3KyMZVzP4FQPHEuiq57OkGeIOVo',
              url: "#{ENV['MOBILE_URL']}/bpm/instances/#{instance.id}",
              topcolor: '#FF0000',
              data: {
                keyword1: {
                  value: "代领通知"
                },
                keyword2: {
                  value: instance.workflow_name
                },
                keyword3: {
                  value: instance.creator_name
                },
                keyword4: {
                  value: instance.created_at.strftime('%Y-%m-%d %H:%M')
                }
              }
            }
          )
          token = instance.tokens.first
          if token
            comment = token.comment.to_s + "【您的报销单据已被财务人员代领,日期: #{Time.now.strftime('%F')}】"
            token.update_columns(comment: comment)
          end
        when 3
          Wechat::TemplateMessage.create(
            school: creator.school,
            app_id: 'wxb1daea392d68c3f5',
            receiver: creator,
            notifyable: instance,
            message: {
              template_id: 'YMFWHgdaTIr8grKr3KyMZVzP4FQPHEuiq57OkGeIOVo',
              url: "#{ENV['MOBILE_URL']}/bpm/instances/#{instance.id}",
              topcolor: '#FF0000',
              data: {
                keyword1: {
                  value: "催件通知 取件码: #{obj['pickCode']}"
                },
                keyword2: {
                  value: instance.workflow_name
                },
                keyword3: {
                  value: instance.creator_name
                },
                keyword4: {
                  value: instance.created_at.strftime('%Y-%m-%d %H:%M')
                },
              }
            }
          )
          token = instance.tokens.first
          if token
            comment = token.comment.to_s + "【取件码: #{obj['pickCode']},设备名称: #{obj['clientName']},设备安放位置: #{obj['placePosition']},箱号名称: #{obj['counterName']},日期: #{Time.now.strftime('%F')}】"
            token.update_columns(comment: comment)
          end
      end
      return render json: { code: 1, message: 'success' }
    else
      return render json: { code: 0, message: '单据不存在' }
    end
  rescue
    return render json: { code: 0, message: '解码失败' }
  end
end
