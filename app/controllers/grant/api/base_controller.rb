class Grant::Api::BaseController < SimpleController::BaseController
  before_action :verify!

  def verify!
    app_key = request.headers['HTTP_APPKEY'].to_s
    timestamps = request.headers['HTTP_TIMESTAMPS'].to_s
    random = request.headers['HTTP_RANDOM'].to_s
    sign = request.headers['HTTP_SIGN'].to_s

    app = Grant::App.find_by(app_key: app_key)
    return render json: { code: 0, message: 'app_key不正确' } unless app

    # time = Time.at(timestamps.to_i / 1000)
    # return render json: { code: 0, message: '' } if 1.minutes.ago > time

    status = sign == signature("#{app_key}#{app.app_secret}#{timestamps}#{random}")
    return render json: { code: 0, message: '签名不正确'} unless status

    @app_secret = app.app_secret
  end

  def decrypt(data)
    data = Base64.decode64(data)
    decipher = OpenSSL::Cipher.new('AES-256-CBC')
    decipher.decrypt
    decipher.key = @app_secret
    decipher.update(data) + decipher.final
  end

  def encrypt(password, data)
    cipher = OpenSSL::Cipher.new('AES-256-CBC')
    cipher.encrypt
    cipher.key = password
    encrypted = cipher.update(data) + cipher.final
    Base64.strict_encode64(encrypted)
  end

  private

  def signature(data)
    Digest::MD5.hexdigest(data)
  end
end
