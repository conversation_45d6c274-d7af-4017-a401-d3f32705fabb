class Grant::Api::TokensController < Grant::Api::BaseController
  # 定制工作流
  def create
    workflow = Notify::Workflow.last
    instance = workflow.instances.find_or_initialize_by(seq: params[:seq])

    # 如果instance不存在 则创建
    unless instance.persisted?
      instance.update(creator: Teacher.find(ENV['NOTIFY_WORKFLOW_ID'] || 1065))
      instance = instance.reload
    end

    # 通知发起的时候
    instance.tokens.where(state: 'processing').destroy_all
    params[:users].each do |user|
      user = user.with_indifferent_access
      operator = Teacher.find_by(code: code) || Student.find_by(code: code)
      next unless operator
      url = user[:url] || params[:url]
      instance.tokens.create!(
        type: 'Tokens::Approval',
        operator: operator,
        state: 'processing',
        token_payload: { url: url, info: user[:info] }
      )
    end
    render json: { code: 1, message: 'ok' }
  end
end
