class Exam::Student::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Exam::Activity,
    collection_name: 'activities',
    instance_name: 'activity',
    view_path: 'exam/activities/student'
  )

  auth_action :student

  def log
    # ip = request.remote_ip
    ip = activity_log_params[:ip]
    answer_set = resource.answer_sets.find_by!(
      creator: current_student,
    )
    answer_set.log!(
      ip: ip,
      log_at: Time.zone.now,
      address: activity_log_params[:address],
    )
    head 201
  end

  protected
    def begin_of_association_chain
      current_student
    end

    def method_for_association_chain
      :exam_activities
    end

    def after_association_chain association
      association.where(state: :doing)
    end

  private
    def activity_params
      params.require(:activity).permit(
        :creator_type, :creator_id, :title, :duration_in_min, :start_at, :state, :school_id
      )
    end

    def activity_log_params
      params.require(:activity).permit(
        :ip, :address
      )
    end
end
