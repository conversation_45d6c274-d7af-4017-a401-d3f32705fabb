class Exam::Teacher::AnswerSetsController < SimpleController::BaseController
  defaults(
    resource_class: Exam::AnswerSet,
    collection_name: 'answer_sets',
    instance_name: 'answer_set',
    view_path: 'answer_sets'
  )

  auth_action :teacher

  belongs_to :exam_activity, param: :activity_id

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def answer_set_params
      params.require(:answer_set).permit(
        :type, :state, :meta, :answerable_type, :answerable_id, :creator_type, :creator_id
      )
    end
end
