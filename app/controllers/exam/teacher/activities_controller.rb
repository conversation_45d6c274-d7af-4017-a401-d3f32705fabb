class Exam::Teacher::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Exam::Activity,
    collection_name: 'activities',
    instance_name: 'activity',
    view_path: 'exam/activities'
  )

  auth_action :teacher

  def show
    @activity = current_teacher.school.exam_activities.find(params[:id])
    respond_with @activity, template: "#{view_path}/show"
  end

  def approval
    @instance = resource.approval!
    respond_with @instance, template: "bpm/instances/show", status: 201
  end

  protected
    def begin_of_association_chain
      current_teacher
    end

    def method_for_association_chain
      :exam_activities
    end

  private
    def update_activity_params
      params.require(:activity).permit(
        :title, :duration_in_min, :start_at, :department_name, paste_student_ids: []
      )
    end

    def create_activity_params
      update_activity_params.merge(
        creator: current_teacher,
        school: current_teacher.school,
      )
    end
end
