class Exam::Admin::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Exam::Activity,
    collection_name: 'activities',
    instance_name: 'activity',
    view_path: 'exam/activities'
  )
  auth_action :teacher
  permit_action :exam_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

    def method_for_association_chain
      :exam_activities
    end

end
