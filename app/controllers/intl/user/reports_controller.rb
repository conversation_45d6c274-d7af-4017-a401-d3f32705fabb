class Intl::User::ReportsController < SimpleController::BaseController
  defaults(
    resource_class: Intl::Report,
    collection_name: 'reports',
    instance_name: 'report',
    view_path: 'intl/reports'
  )
  auth_action :teacher

  belongs_to :intl_activity, param: :activity_id

  protected

  def begin_of_association_chain
    current_teacher
  end

  private
    def update_report_params
      params.require(:report).permit(
       :title, :body, :flag, meta: {}, attachments: {}
      )
    end

    def create_report_params
      update_report_params.merge(
        user: current_teacher,
        school: current_teacher.school,
      )
    end
end
