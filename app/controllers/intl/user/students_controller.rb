class Intl::User::StudentsController < SimpleController::BaseController
  defaults(
    resource_class: Student,
    collection_name: 'students',
    instance_name: 'student',
    view_path: 'students'
  )

  auth_action [ :teacher, :student ]

  belongs_to :intl_activity, param: :activity_id

  protected

  def begin_of_association_chain
    current_auth
  end

  def method_for_association_chain
    :following_students
  end
end
