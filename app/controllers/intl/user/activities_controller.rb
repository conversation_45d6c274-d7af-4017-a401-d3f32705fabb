class Intl::User::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Intl::Activity,
    collection_name: 'activities',
    instance_name: 'activity',
    view_path: 'intl/activities'
  )

  auth_action [ :teacher, :student ]

  protected

  def begin_of_association_chain
    current_auth
  end

  def method_for_association_chain
    :intl_activities
  end

  private
    def activity_params
      params.require(:activity).permit(
        :name, :destination, :start_at, :end_at, :plan_num, :state, manage_teacher_ids: [], owned_teacher_ids: [], following_teacher_ids: [], following_student_ids: [], finance_budget_ids: []
      )
    end
end
