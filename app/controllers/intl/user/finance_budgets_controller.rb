class Intl::User::FinanceBudgetsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Budget,
    collection_name: 'budgets',
    instance_name: 'budget',
    view_path: 'finance/budgets'
  )
  auth_action :teacher

  belongs_to :intl_activity, param: :activity_id

  protected

  def begin_of_association_chain
    current_teacher
  end

  def method_for_association_chain
    :finance_budgets
  end
end
