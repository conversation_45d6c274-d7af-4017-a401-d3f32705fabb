class Stat::Admin::ComingAndGoingsController < SimpleController::BaseController
  defaults(
    resource_class: Stat::ComingAndGoing,
    collection_name: 'coming_and_goings',
    instance_name: 'coming_and_going',
    view_path: 'stat/coming_and_goings'
  )

  protected

  def after_association_chain association
    @stat = Stat::ComingAndGoing.stat(association.ransack(params[:q]).result)
    association
  end
end
