class Sms::CodesController < ApplicationController
  # 获取调用短信的
  def access_token
    token = $redis.get('sms_access_token')
    return token if token

    url = 'http://172.16.144.17:8080/msgcenter/msg_getToken.action'
    body = {
      appId: ENV['SMS_APP_ID'],
      appPassword: ENV['SMS_APP_PASSWORD']
    }
    response = Typhoeus.post(url, body: body)
    result = JSON.parse(response.body)
    $redis.setex('sms_access_token', 5400, result['access_token'])
    result['access_token']
  end

  #  发送验证码
  def create
    code = (0..9).to_a.sample(6).join
    data = {
      token: access_token,
      msgContent: "尊敬的用户，您的验证码为：#{code}，有效期5分钟。",
      sendChannel: 'SMS',
      sendMode: 'normal',
      receivers: [{ type: 2, phone: params[:phone] }],
    }
    url = "http://172.16.144.17:8080/msgcenter/msg_sendMessage.action"
    $redis.setex("sms_login_#{params[:account]}", 300, code)
    response = Typhoeus.post(url, body: {data: data.to_json})
    render json: JSON.parse(response.body)
  end

  def notify
    data = {
      token: access_token,
      msgContent: params[:content],
      sendChannel: 'SMS',
      sendMode: 'normal',
      receivers: [{ type: 2, phone: params[:phone] }],
    }
    url = "http://172.16.144.17:8080/msgcenter/msg_sendMessage.action"
    response = Typhoeus.post(url, body: {data: data.to_json})
    render json: JSON.parse(response.body)
  end
end