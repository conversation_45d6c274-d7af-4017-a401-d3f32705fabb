class Teaching::Admin::EvaluateScoresController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::EvaluateScore,
    collection_name: 'teaching_evaluate_scores',
    instance_name: 'teaching_evaluate_score',
    view_path: 'teaching/evaluate_scores'
  )
  belongs_to :question, shallow: true

  auth_action :teacher
  permit_action :teaching_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def teaching_evaluate_score_params
      params.require(:teaching_evaluate_score).permit(
        :score, :evaluate_item_id, :value
      )
    end
end
