class Teaching::Admin::EvaluateItemsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::EvaluateItem,
    collection_name: 'teaching_evaluate_items',
    instance_name: 'teaching_evaluate_item',
    view_path: 'teaching/evaluate_items'
  )
  auth_action :teacher
  permit_action :teaching_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def teaching_evaluate_item_params
      params.require(:teaching_evaluate_item).permit(
        :title, :content, :position
      )
    end
end
