class Teaching::Admin::CurrentSemester::CoursesController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::Course,
    collection_name: 'courses',
    instance_name: 'course',
    view_path: 'teaching/courses'
  )

  auth_action :teacher
  permit_action :teaching_admin

  include Teaching::<PERSON>mesterSelect

  def batch_update
    (batch_update_course_params[:course_ids] || []).each  do |course_id|
      Teaching::Course.find(course_id).inspect_teacher_ids = batch_update_course_params[:teacher_ids]
    end
    head 201
  end

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def batch_update_course_params
      params.require(:courses).permit(
        teacher_ids: [], course_ids: []
      )
    end
end
