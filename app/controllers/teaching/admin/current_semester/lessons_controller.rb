class Teaching::Admin::CurrentSemester::LessonsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::Lesson,
    collection_name: 'lessons',
    instance_name: 'lesson',
    view_path: 'teaching/lessons/admin'
  )

  auth_action :teacher
  permit_action :teaching_admin, :teaching_operate

  include Teaching::SemesterSelect

  def ana_info
    render json: Teaching::LessonAnalyst.new(lessons: collection).ana_info
  end

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def lesson_params
      params.require(:lesson).permit(
        :start_unit, :end_unit, :course_id, :teacher_id, :classroom_id, :date, :semester_id
      )
    end
end
