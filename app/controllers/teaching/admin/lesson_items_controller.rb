class Teaching::Admin::LessonItemsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::LessonItem,
    collection_name: 'lesson_items',
    instance_name: 'lesson_item',
    view_path: 'teaching/lesson_items'
  )

  auth_action :teacher
  permit_action :teaching_admin

  belongs_to :lesson_plan, shallow: true

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def lesson_item_params
      params.require(:lesson_item).permit(
        :item_type, :title, :position, :netdisk_item_id, attachments: {}
      )
    end
end
