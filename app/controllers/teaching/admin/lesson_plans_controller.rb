class Teaching::Admin::LessonPlansController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::LessonPlan::Share,
    collection_name: 'lesson_plans',
    instance_name: 'lesson_plan',
    view_path: 'teaching/lesson_plans'
  )

  auth_action :teacher
  permit_action :teaching_admin

  belongs_to :course_catalog, shallow: true

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def update_lesson_plan_params
      params.require(:lesson_plan).permit(
        :sections, :title, :body, :subject, :position
      )
    end

    def create_lesson_plan_params
      update_lesson_plan_params.merge(
        course_set: parent.course_set,
      )
    end
end
