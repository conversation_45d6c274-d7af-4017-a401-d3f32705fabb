class Teaching::Admin::CourseSetsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::CourseSet,
    collection_name: 'course_sets',
    instance_name: 'course_set',
    view_path: 'teaching/course_sets'
  )

  auth_action :teacher
  permit_action :teaching_admin

  protected
    def begin_of_association_chain
      current_teacher.school.current_semester
    end

  private
    def update_course_set_params
      params.require(:course_set).permit(
        :code, :credits, :enabled, :establish_on, :name, :project, :education, :category, :course_type, :department_id, :exam_mode, :period, meta: {}
      )
    end

    def create_course_set_params
      update_course_set_params.merge(
        school: current_teacher.school
      )
    end
end
