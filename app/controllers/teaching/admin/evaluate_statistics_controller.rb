class Teaching::Admin::EvaluateStatisticsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::EvaluateStatistic,
    collection_name: 'teaching_evaluate_statistics',
    instance_name: 'teaching_evaluate_statistic',
    view_path: 'teaching/evaluate_statistics'
  )

  auth_action :teacher
  permit_action :teaching_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def update_teaching_evaluate_statistic_params
      params.require(:teaching_evaluate_statistic).permit(
        :title, query_meta: {}
      )
    end

    def create_teaching_evaluate_statistic_params
      update_teaching_evaluate_statistic_params.merge(
        creator: current_teacher
      )
    end
end
