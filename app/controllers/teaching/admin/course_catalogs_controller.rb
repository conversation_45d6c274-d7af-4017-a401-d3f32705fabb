class Teaching::Admin::CourseCatalogsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::CourseCatalog,
    collection_name: 'course_catalogs',
    instance_name: 'course_catalog',
    view_path: 'teaching/course_catalogs',
    ransack_off: true,
    paginate_off: true,
  )

  auth_action :teacher
  permit_action :teaching_admin

  belongs_to :course_set, shallow: true

  def create
    if clone_course_catalog_params[:origin_course_catalog_id].present?
      origin_course_catalog = Teaching::CourseCatalog.find(clone_course_catalog_params[:origin_course_catalog_id])
      @course_catalog = origin_course_catalog.clone
      @course_catalog.update!(
        create_course_catalog_params.merge(
          course_set_id: params[:course_set_id]
        )
      )
      respond_with @course_catalog, template: "#{view_path}/show", status: 201
    else
      super
    end
  end

  protected
    def begin_of_association_chain
      current_teacher.school
    end

    def collection_of_association_chain
      Teaching::CourseCatalog.where(
        id: origin_end_of_association_chain.select do |course_catalog|
          course_catalog.can_view? current_teacher
        end.map(&:id)
      )
    end

  private
    def update_course_catalog_params
      params.require(:course_catalog).permit(
        :title, :view_permit, :edit_permit, view_teacher_ids: [], edit_teacher_ids: []
      )
    end

    def create_course_catalog_params
      update_course_catalog_params.merge(
        teacher: current_teacher
      )
    end

    def clone_course_catalog_params
      params.require(:course_catalog).permit(
        :origin_course_catalog_id
      )
    end
end
