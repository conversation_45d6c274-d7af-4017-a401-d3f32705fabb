class Teaching::Admin::PlansController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::Plan,
    collection_name: 'plans',
    instance_name: 'plan',
    view_path: 'teaching/plans',
    singleton: true,
  )

  auth_action :teacher
  permit_action :teaching_admin, :ems_admin

  belongs_to :program, shallow: true

  protected
    def begin_of_association_chain
      current_teacher.school
    end
end
