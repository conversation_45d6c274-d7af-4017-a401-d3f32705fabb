class Teaching::Admin::Question<PERSON>etsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::EvaluateQuestionSet,
    collection_name: 'question_sets',
    instance_name: 'question_set',
    view_path: 'question_sets'
  )

  auth_action :teacher
  permit_action :teaching_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

    def method_for_association_chain
      'teaching_evaluate_question_sets'
    end

  private
    def question_set_params
      params.require(:question_set).permit(
        :name, :category, meta: {}
      )
    end
end
