class Teaching::Admin::PlanCourseSetsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::PlanCourseSet,
    collection_name: 'plan_course_sets',
    instance_name: 'plan_course_set',
    view_path: 'teaching/plan_course_sets'
  )

  auth_action :teacher
  permit_action :teaching_admin, :ems_admin

  belongs_to :plan_course_set_group, shallow: true

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def plan_course_set_params
      params.require(:plan_course_set).permit(
        :compulsory, :remark, :terms, :course_set_id, :department_id, :exam_mode, :credits, :period, :course_dir_id, term_credits: {}
      )
    end
end
