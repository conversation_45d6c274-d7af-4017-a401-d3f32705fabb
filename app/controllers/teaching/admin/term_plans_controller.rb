class Teaching::Admin::TermPlansController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::TermPlan,
    collection_name: 'term_plans',
    instance_name: 'term_plan',
    view_path: 'teaching/term_plans',
    ransack_off: true,
    paginate_off: true,
  )

  auth_action :teacher
  permit_action :teaching_admin, :ems_admin

  belongs_to :program

  protected
    def begin_of_association_chain
      current_teacher.school
    end

    def resource
      @term_plan = Teaching::TermPlan.new(
        term: params[:id], program_id: params[:program_id], adminclass_name: params[:adminclass_name]
      )
      @adminclass_name = params[:adminclass_name]
    end
end
