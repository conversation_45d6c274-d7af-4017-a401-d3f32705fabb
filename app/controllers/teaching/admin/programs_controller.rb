class Teaching::Admin::ProgramsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::Program,
    collection_name: 'programs',
    instance_name: 'program',
    view_path: 'teaching/programs'
  )

  auth_action :teacher
  permit_action :teaching_admin, :ems_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def program_params
      params.require(:program).permit(
        :name, :grade, :department_id, :major_id, :duration, :study_type, :degree, :effective_on, :invalid_on, :remark, meta: {},
        plan_attributes: [:credits, :terms_count]
      )
    end
end
