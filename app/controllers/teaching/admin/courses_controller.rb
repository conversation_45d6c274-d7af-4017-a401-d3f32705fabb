class Teaching::Admin::CoursesController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::Course,
    collection_name: 'courses',
    instance_name: 'course',
    view_path: 'teaching/courses'
  )

  auth_action :teacher
  permit_action :teaching_admin

  belongs_to :program, optional: true

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def course_params
      params.require(:course).permit(
        :course_catalog_id, :course_set_id, :start_week, :end_week, :period, :status, :no, :grade, :name, :std_count, :semester_id, :department_id, meta: {}, inspect_teacher_ids: []
      )
    end
end
