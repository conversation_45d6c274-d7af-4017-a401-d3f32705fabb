class Teaching::Admin::Questions<PERSON><PERSON>roller < SimpleController::BaseController
  defaults(
    resource_class: Question,
    collection_name: 'questions',
    instance_name: 'question',
    view_path: 'questions'
  )

  auth_action :teacher
  permit_action :teaching_admin

  belongs_to :question_set, parent_class: Teaching::EvaluateQuestionSet, shallow: true

  def delete_evaluate_score
    value = evaluate_score_params[:value]
    resource.teaching_evaluate_scores.where(value: value).destroy_all
    head 201
  end

  private
    def question_params
      params.require(:question).permit(
        :type, :title, :position, choices: {}
      )
    end

    def evaluate_score_params
      params.require(:evaluate_score).permit(
        :value
      )
    end
end
