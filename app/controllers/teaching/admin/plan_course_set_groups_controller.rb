class Teaching::Admin::PlanCourseSetGroupsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::PlanCourseSetGroup,
    collection_name: 'plan_course_set_groups',
    instance_name: 'plan_course_set_group',
    view_path: 'teaching/plan_course_set_groups'
  )

  auth_action :teacher
  permit_action :teaching_admin, :ems_admin

  belongs_to :program, shallow: true

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def plan_course_set_group_params
      params.require(:plan_course_set_group).permit(
        :credits, :relation, :remark, :term_credits, :term_week_hours, :course_type, :position
      )
    end
end
