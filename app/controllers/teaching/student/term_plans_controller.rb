class Teaching::Student::TermPlansController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::TermPlan,
    collection_name: 'term_plans',
    instance_name: 'term_plan',
    view_path: 'teaching/term_plans',
    ransack_off: true,
    paginate_off: true,
  )

  auth_action :student

  belongs_to :program, singleton: true

  protected
    def begin_of_association_chain
      current_student
    end

    def resource
      @adminclass_name = current_student.adminclass&.name
      @term_plan = Teaching::TermPlan.new(
        term: params[:id], program_id: current_student.program&.id, adminclass_name: @adminclass_name
      )
    end
end
