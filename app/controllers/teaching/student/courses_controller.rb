class Teaching::Student::CoursesController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::Course,
    collection_name: 'courses',
    instance_name: 'course',
    view_path: 'teaching/courses'
  )

  auth_action :student

  protected
    def begin_of_association_chain
      current_student
    end

    def after_association_chain association
      association.where(semester: current_student.school.current_semester)
    end
end
