class Teaching::Student::<PERSON><PERSON><PERSON><PERSON>ontroller < SimpleController::BaseController
  defaults(
    resource_class: <PERSON><PERSON><PERSON>,
    collection_name: 'semesters',
    instance_name: 'semester',
    view_path: 'semesters'
  )
  auth_action :student

  def begin_of_association_chain
    current_student.school
  end

  def current
    @semester = current_student.school.current_semester
    respond_with @semester, template: "#{view_path}/show"
  end

end
