class Teaching::Student::EvaluationsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::Evaluation,
    collection_name: 'teaching_evaluations',
    instance_name: 'teaching_evaluation',
    view_path: 'teaching/evaluations'
  )

  auth_action :student

  belongs_to :lesson

  protected
    def begin_of_association_chain
      current_student
    end

    def resource
      @teaching_evaluation = current_student.lessons.find_by!(id: params[:lesson_id]).evaluations.find_by!(student: current_student)
    end

  private
    def update_teaching_evaluation_params
      params.require(:teaching_evaluation).permit(
        :state, answers_attributes: [:id, :value, meta: {}]
      )
    end

    def create_teaching_evaluation_params
      {
        question_set_id: current_student.school.teaching_evaluate_question_sets.last&.id,
        student: current_student,
      }
    end
end
