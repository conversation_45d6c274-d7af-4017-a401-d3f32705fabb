class Teaching::Student::CourseActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::CourseActivity,
    collection_name: 'course_activities',
    instance_name: 'course_activity',
    view_path: 'teaching/course_activities'
  )
  auth_action :student

  protected
    def begin_of_association_chain
      current_student
    end

    def after_association_chain association
      association.by_semester current_student.school.current_semester
    end
end
