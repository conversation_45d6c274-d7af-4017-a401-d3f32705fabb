class Teaching::Student::HomeworksController < SimpleController::BaseController
  defaults(
    resource_class: Report::Homework,
    collection_name: 'reports',
    instance_name: 'report',
    view_path: 'teaching/homeworks/student'
  )

  auth_action :student
  belongs_to  :source, collection_name: :lessons, param: :lesson_id, parent_class: Teaching::Lesson, shallow: true
  
  protected
    def begin_of_association_chain
      current_student
    end

    def method_for_association_chain
      'homeworks'
    end
end
