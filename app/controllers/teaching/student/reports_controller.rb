class Teaching::Student::ReportsController < SimpleController::BaseController
  defaults(
    resource_class: Report::Homework::Answer,
    collection_name: 'reports',
    instance_name: 'report',
    view_path: 'teaching/reports/student'
  )

  auth_action :student
  belongs_to  :source, collection_name: :courses, param: :course_id, parent_class: Teaching::Course, optional: true

  protected
    def begin_of_association_chain
      current_student
    end

    def method_for_association_chain
      'homeworks'
    end

    def resource
      @report = current_student.homeworks.find(params[:id])
    end

    def after_association_chain association
      reports = association.where(user: current_student)
      @info = Report.statistics(reports)
      reports
    end
end
