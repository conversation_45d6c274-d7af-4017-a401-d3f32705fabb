class Teaching::Student::RegistersController < SimpleController::BaseController
  defaults(
    resource_class: Register::LessonRegister,
    collection_name: 'registers',
    instance_name: 'register',
    view_path: 'registers'
  )

  auth_action :student

  belongs_to :lesson, polymorphic: true, optional: true

  def nonce
    render json: {
      nonce: Teaching::Lesson::Fast.find(lesson_id: params[:lesson_id]).first&.nonce,
      times: params[:times],
      register_start_time: [(resource.fast_lesson.register_time_range.first - Time.now).round, 0].max,
      register_end_time: [(resource.fast_lesson.end_datetime - Time.now).round, 0].max,
      evaluate_start_time: [(resource.fast_lesson.evaluate_time_range.first - Time.now).round, 0].max,
      evaluate_end_time: [(resource.fast_lesson.evaluate_time_range.last - Time.now).round, 0].max,
      # can_register: resource.first_register_time_correct?,
      # can_evaluate: resource.second_regitser_time_correct?,
    }
  rescue
     head 200
  end

  def update
    # Ohm 对象
    resource.times = register_params[:times]
    # nonce= 仅用于触发逻辑
    resource.process_state
    render json: { state: resource.state }, status: 200
  end

  protected

  def resource
    @register ||= Register::LessonRegister::Fast.find_by_lesson_id(
      lesson_id: params[:lesson_id],
      student_id: current_auth.id,
    )
    unless @register
      raise Error::BaseError.new(status: 404, message: '没有参加本次点名')
    end
    return @register
  end

  private

  def register_params
    params.require(:register).permit(
      # 使用 nonce= , times 要在 nonce 前
      :times, :nonce
    )
  end
end
