class Teaching::Student::<PERSON>ons<PERSON>ontroller < SimpleController::BaseController
  defaults(
    resource_class: Teaching::Lesson,
    collection_name: 'lessons',
    instance_name: 'lesson',
    view_path: 'teaching/lessons/student'
  )

  auth_action :student

  belongs_to :course, optional: true

  protected
    def begin_of_association_chain
      current_student
    end

    def after_association_chain association
      association.where(semester: current_student.school.current_semester)
    end
end
