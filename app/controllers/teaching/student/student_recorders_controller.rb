class Teaching::Student::StudentRecordersController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::StudentRecorder,
    collection_name: 'recorders',
    instance_name: 'recorder',
    view_path: 'teaching/recorders'
  )

  auth_action :student

  def show
    show! do
      @recorder.heartbeat
    end
  end

  protected
    def resource
      @recorder = Teaching::Recorder::Fast.load_recorder course_id: params[:course_id], user: current_student, source_type: params[:source_type], source_id: params[:source_id], lesson_id: params[:lesson_id]
    end

  private
    def recorder_params
      params.require(:recorder).permit(
       :state, meta: {}
      )
    end
end
