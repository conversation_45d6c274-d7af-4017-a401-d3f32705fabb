class Teaching::Student::Lessons::LessonPlansController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::LessonPlan,
    collection_name: 'lesson_plans',
    instance_name: 'lesson_plan',
    view_path: 'teaching/lesson_plans'
  )

  auth_action :student

  belongs_to :lesson

  protected
    def begin_of_association_chain
      @course = Teaching::Lesson.find(params[:lesson_id]).course
      current_student
    end
end
