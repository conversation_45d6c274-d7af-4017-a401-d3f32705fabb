class Teaching::User::CommentsController < SimpleController::BaseController
  defaults(
    resource_class: Comment,
    collection_name: 'comments',
    instance_name: 'comment',
    view_path: 'teaching/comments'
  )

  auth_action [:teacher, :student]

  include Teaching::CourseValidate
  before_action :validate_course

  belongs_to :course do
    belongs_to :topic
  end

  protected
    def end_of_association_chain
      parent
      @mode = params[:mode]
      if @mode == 'discuss'
        current_auth.class.to_s == 'Teacher' ?
          @topic.comments :
          @topic.comments_by_user(current_auth, mode: @mode)
      else
        current_auth.class.to_s == 'Teacher' ?
          @topic.comments.where(parent_id: nil) :
          @topic.comments_by_user(current_auth, mode: @mode).where(parent_id: nil)
      end
    end

    def resource
      parent
      if current_auth.class.to_s == 'Teacher'
        @comment = @topic.comments.find(params[:id])
      else
        @comment = @topic.comments.where(user: current_auth).find(params[:id])
      end
    end
end
