class Teaching::User::TopicsController < SimpleController::BaseController
  defaults(
    resource_class: Topic,
    collection_name: 'topics',
    instance_name: 'topic',
    view_path: 'topics'
  )

  auth_action [:teacher, :student]

  include Teaching::CourseValidate
  before_action :validate_course

  belongs_to :course

  after_action :update_topic_view_count!, only: :show

  def update
    @topic = resource
    @topic.assign_attributes topic_params
    @topic.save!(touch: false)
    head 204
  end

  protected
    def after_association_chain association
      current_auth.class.to_s == 'Teacher' ?
        association.published :
        association.published.view_permit_by_user(current_auth)
    end

  private
    def topic_params
      params.require(:topic).permit(
        :type, :title, :body, :state, :cover_image, :view_permit, :reply_permit, :suggest, :visible_conf, :comment_expire_at, meta: {}, view_student_ids: [], reply_student_ids: [], attachments: {}
      ).merge(
        school: current_auth.school,
        user: current_auth,
      )
    end

    def update_topic_view_count!
      @topic.increment!(:view_count)
    end
end
