class Teaching::User::CourseSetInstancesController < SimpleController::BaseController
  defaults(
    resource_class: Bpm::Instance,
    collection_name: 'instances',
    instance_name: 'instance',
    view_path: 'bpm/instances',
    singleton: true
  )

  auth_action :teacher

  belongs_to :course_set

  def resource
    @instance = parent.course_set_instance
  end

  protected
    def begin_of_association_chain
      current_auth.school
    end
end
