class Teaching::Cplan::User::CourseActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::CourseActivity,
    collection_name: 'course_activities',
    instance_name: 'course_activity',
    view_path: 'teaching/course_activities'
  )

  auth_action [:teacher, :student]

  belongs_to :course, optional: true
  belongs_to :program, optional: true

  protected
    def begin_of_association_chain
      current_auth.school
    end

end
