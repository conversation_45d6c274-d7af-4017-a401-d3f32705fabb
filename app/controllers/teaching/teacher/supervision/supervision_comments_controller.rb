class Teaching::Teacher::Supervision::SupervisionCommentsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::SupervisionComment,
    collection_name: 'supervision_comments',
    instance_name: 'supervision_comment',
    view_path: 'teaching/supervision_comments',
  )
  auth_action :teacher
  permit_action :teaching_admin, :teaching_operate

  belongs_to :lesson, collection_name: :supervision_lessons

  protected

  def	begin_of_association_chain
    current_teacher.has_any_role?(:teaching_admin) ?
      current_teacher.school : current_teacher
  end

  private

  def update_supervision_comment_params
    params.require(:supervision_comment).permit(
      :comment,
      :score,
      attachments: {},
      meta: {},
    )
  end

  def create_supervision_comment_params
    update_supervision_comment_params.merge(
      teacher: current_teacher
    )
  end
end
