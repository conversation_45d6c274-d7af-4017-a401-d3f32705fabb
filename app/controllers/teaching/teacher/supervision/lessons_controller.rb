class Teaching::Teacher::Supervision::Lessons<PERSON>ontroller < SimpleController::BaseController
  defaults(
    resource_class: Teaching::Lesson,
    collection_name: 'lessons',
    instance_name: 'lesson',
    view_path: 'teaching/lessons',
  )
  auth_action :teacher
  permit_action :teaching_admin, :teaching_operate

  include Teaching::SemesterSelect

  def create
    exist_lesson_ids = end_of_association_chain.where(
      semester_id: lesson_params[:semester_id],
      week: lesson_params[:week],
    ).pluck(:id)
    lesson_ids = lesson_params[:lesson_ids]
    delete_lesson_ids = exist_lesson_ids - lesson_ids
    add_lesson_ids = lesson_ids - exist_lesson_ids

    current_teacher.teaching_supervision_relations.where(lesson_id: delete_lesson_ids).destroy_all
    add_lesson_ids.each do |add_lesson_id|
      current_teacher.teaching_supervision_relations.where(
        lesson_id: add_lesson_id
      ).first_or_create!
    end

    @lessons = end_of_association_chain.where(semester_id: lesson_params[:semester_id], week: lesson_params[:week]).paginate(page: 1, per_page: 9999)
    respond_with @lessons, template: "#{view_path}/index", status: 201
  end

  def ana_info
    render json: Teaching::LessonAnalyst.new(lessons: collection).ana_info
  end

  protected

  def	begin_of_association_chain
    current_teacher.has_any_role?(:teaching_admin) ?
      current_teacher.school : current_teacher
  end

  def	method_for_association_chain
    :supervision_lessons
  end

  private

  def lesson_params
    params.require(:lesson).permit(
      :semester_id,
      :week,
      lesson_ids: [],
    )
  end
end
