class Teaching::Teacher::CurrentSemester::ScheduleCoursesController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::ScheduleCourse,
    collection_name: 'schedule_courses',
    instance_name: 'schedule_course',
    view_path: 'teaching/schedule_courses',
    singleton: true,
  )
  auth_action :teacher

  belongs_to :course

  def approval
    @instance = resource.approval!
    respond_with @instance, template: "bpm/instances/show", status: 201
  end

  protected
    def begin_of_association_chain
      current_teacher
    end

  private
    def create_schedule_course_params
      { teacher: current_teacher }
    end

    def update_schedule_course_params
      params.require(:schedule_course).permit(
        :state
      )
    end
end
