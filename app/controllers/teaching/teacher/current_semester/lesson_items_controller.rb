class Teaching::Teacher::CurrentSemester::LessonItemsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::LessonItem,
    collection_name: 'lesson_items',
    instance_name: 'lesson_item',
    view_path: 'teaching/lesson_items'
  )
  auth_action :teacher

  belongs_to :lesson_plan, shallow: true

  private
    def lesson_item_params
      params.require(:lesson_item).permit(
        :item_type, :title, :position, :netdisk_item_id, attachments: {}
      )
    end
end
