class Teaching::Teacher::CurrentSemester::ScheduleLessonsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::ScheduleLesson,
    collection_name: 'schedule_lessons',
    instance_name: 'schedule_lesson',
    view_path: 'teaching/schedule_lessons'
  )
  auth_action :teacher

  belongs_to :course do
    belongs_to :schedule_course, singleton: true
  end

  def import
    Netdisk::ScheduleCourseItem.find(params[:target_id]).execute(parent)
    head 204
  end

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def schedule_lesson_params
      params.require(:schedule_lesson).permit(
        payload: {}
      )
    end
end
