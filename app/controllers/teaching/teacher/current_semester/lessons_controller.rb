class Teaching::Teacher::CurrentSemester::LessonsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::Lesson,
    collection_name: 'lessons',
    instance_name: 'lesson',
    view_path: 'teaching/lessons/teacher'
  )

  auth_action :teacher

  include Teaching::SemesterSelect

  def ana_info
    render json: Teaching::LessonAnalyst.new(lessons: collection).ana_info
  end

  protected
    def begin_of_association_chain
      current_teacher
    end
end
