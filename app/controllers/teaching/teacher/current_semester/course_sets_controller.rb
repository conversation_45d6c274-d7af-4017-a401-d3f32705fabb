class Teaching::Teacher::CurrentSemester::CourseSetsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::CourseSet,
    collection_name: 'course_sets',
    instance_name: 'course_set',
    view_path: 'teaching/course_sets'
  )

  auth_action :teacher

  protected
    def begin_of_association_chain
      current_teacher
    end

    # def method_for_association_chain
    #   :current_semester_course_sets
    # end
    #
end
