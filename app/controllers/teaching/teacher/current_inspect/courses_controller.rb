class Teaching::Teacher::CurrentInspect::CoursesController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::Course,
    collection_name: 'courses',
    instance_name: 'course',
    view_path: 'teaching/courses'
  )

  auth_action :teacher

  include Teaching::SemesterSelect

  protected
    def begin_of_association_chain
      current_teacher
    end

    def method_for_association_chain
      :inspect_courses
    end
end
