class Teaching::Teacher::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> < SimpleController::BaseController
  defaults(
    resource_class: <PERSON><PERSON><PERSON>,
    collection_name: 'semesters',
    instance_name: 'semester',
    view_path: 'semesters'
  )
  auth_action :teacher

  def begin_of_association_chain
    current_teacher.school
  end

  def current
    @semester = current_teacher.school.current_semester
    respond_with @semester, template: "#{view_path}/show"
  end

end
