class Teaching::Teacher::Inspect::<PERSON>ons<PERSON>ontroller < SimpleController::BaseController
  defaults(
    resource_class: Teaching::Lesson,
    collection_name: 'lessons',
    instance_name: 'lesson',
    view_path: 'teaching/lessons/teacher'
  )
  auth_action :teacher

  before_action :resource
  include Teaching::LessonValidate
  before_action :validate_lesson

  def download
    url = ZipLessonService.new(id: resource.id, homework_id: params[:homework_id], q: params[:q]).execute
    render json: { url: url }, status: 201
  end
end
