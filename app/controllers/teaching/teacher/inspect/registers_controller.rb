class Teaching::Teacher::Inspect::RegistersController < SimpleController::BaseController
  defaults(
    resource_class: Register::Lesson<PERSON>eg<PERSON>,
    collection_name: 'registers',
    instance_name: 'register',
    view_path: 'registers',
    paginate_off: true,
    ransack_off: true,
  )

  auth_action :teacher

  include Teaching::LessonValidate
  before_action :validate_lesson

  belongs_to :lesson

  def nonce
    fast_object = parent.fast
    raise Error::BaseError.new(message: '签到未开启') unless fast_object
    fast_object.refresh_nonce
    render json: {
      nonce: fast_object.nonce,
      times: params[:times],
    }
  end

  def init
    # parent.register_init
    head 201
  end

  protected
    def collection
      @registers = parent.autoswitch_registers
    end

    def resource
      @register = parent.fast ?
        parent.autoswitch_registers.find(register_id: params[:id]).first :
        parent.autoswitch_registers.find(params[:id])
    end

  private

    def register_params
      params.require(:register).permit(
        :state
      )
    end
end
