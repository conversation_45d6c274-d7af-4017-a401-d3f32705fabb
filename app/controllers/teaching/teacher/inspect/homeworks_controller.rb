class Teaching::Teacher::Inspect::HomeworksController < SimpleController::BaseController
  defaults(
    resource_class: Report::Homework,
    collection_name: 'reports',
    instance_name: 'report',
    view_path: 'teaching/homeworks/teacher'
  )
  auth_action :teacher

  include Teaching::LessonValidate
  before_action :validate_lesson

  belongs_to :lesson

  def export
    url = ExportHomeworkService.new(id: resource.id, q: params[:q]).perform
    render json: { url: url }, status: 201
  end

  protected
    def method_for_association_chain
      'homeworks'
    end

  private
    def update_report_params
      params.require(:report).permit(
        :flag, :title, :body, :state, :end_at, meta: {}, attachments: {}, files: []
      )
    end

    def create_report_params
      update_report_params.merge(
        user: current_teacher,
        school: current_teacher.school,
      )
    end
end
