class Teaching::Teacher::Inspect::AnswersController < SimpleController::BaseController
  defaults(
    resource_class: Report::Homework::Answer,
    collection_name: 'reports',
    instance_name: 'report',
    view_path: 'teaching/answers/teacher'
  )
  auth_action :teacher

  include Teaching::LessonValidate
  before_action :validate_lesson

  belongs_to :lesson do
    belongs_to :homework, optional: true
  end

  protected
    def method_for_association_chain
      'homework_answers'
    end

  private
    def report_params
      params.require(:report).permit(
        :state, :score
      )
    end
end
