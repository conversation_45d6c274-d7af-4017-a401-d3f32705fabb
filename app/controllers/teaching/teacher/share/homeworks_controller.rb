class Teaching::Teacher::Share::HomeworksController < SimpleController::BaseController
  defaults(
    resource_class: Report::Homework,
    collection_name: 'reports',
    instance_name: 'report',
    view_path: 'teaching/homeworks/teacher'
  )

  auth_action :teacher

  include Teaching::CourseValidate
  before_action :validate_course

  belongs_to :course

  protected
    def method_for_association_chain
      'schoolworks'
    end
end
