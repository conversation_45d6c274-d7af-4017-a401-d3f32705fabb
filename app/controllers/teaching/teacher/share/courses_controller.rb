class Teaching::Teacher::Share::CoursesController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::Course,
    collection_name: 'courses',
    instance_name: 'course',
    view_path: 'teaching/courses'
  )

  auth_action :teacher

  before_action :resource
  include Teaching::CourseValidate
  before_action :validate_course

  private
    def course_params
      params.require(:course).permit(
        :course_catalog_id
      )
    end
end
