class Teaching::Teacher::Share::StudentsController < SimpleController::BaseController
  defaults(
    resource_class: Student,
    collection_name: 'students',
    instance_name: 'student',
    view_path: 'students/course'
  )

  auth_action :teacher

  include Teaching::CourseValidate
  before_action :validate_course

  belongs_to :course

  def	after_association_chain association
    association.order(adminclass_id: :asc, code: :asc)
  end
end
