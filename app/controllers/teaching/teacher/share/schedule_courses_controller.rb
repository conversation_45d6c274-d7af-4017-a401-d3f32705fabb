class Teaching::Teacher::Share::ScheduleCoursesController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::ScheduleCourse,
    collection_name: 'schedule_courses',
    instance_name: 'schedule_course',
    view_path: 'teaching/schedule_courses',
    singleton: true,

  )
  auth_action :teacher

  belongs_to :course

  protected

  def begin_of_association_chain
    current_teacher.school
  end
end
