class Teaching::Teacher::Share::StudentRecordersController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::StudentRecorder,
    collection_name: 'recorders',
    instance_name: 'recorder',
    view_path: 'teaching/recorders'
  )

  auth_action :teacher

  include Teaching::CourseValidate
  before_action :validate_course

  protected
    def begin_of_association_chain
      "#{params[:source_type]}".constantize.find(params[:source_id])
    end

    def method_for_association_chain
      :student_recorders
    end

    def after_association_chain association
      association.where(course: @course)
    end
end
