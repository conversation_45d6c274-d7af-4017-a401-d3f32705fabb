class Teaching::Teacher::CourseActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::CourseActivity,
    collection_name: 'course_activities',
    instance_name: 'course_activity',
    view_path: 'teaching/course_activities'
  )
  auth_action :teacher

  protected
    def begin_of_association_chain
      current_teacher
    end

    def after_association_chain association
      association.by_semester current_teacher.school.current_semester
    end
end
