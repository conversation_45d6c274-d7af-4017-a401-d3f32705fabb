class Teaching::Teacher::ProgramsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::Program,
    collection_name: 'programs',
    instance_name: 'program',
    view_path: 'teaching/programs'
  )

  auth_action :teacher

  protected
    def begin_of_association_chain
      current_teacher.school
    end

    def after_association_chain association
      association.activate
    end
end
