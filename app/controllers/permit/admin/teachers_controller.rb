class Permit::Admin::TeachersController < SimpleController::BaseController
  defaults(
    resource_class: Teacher,
    collection_name: 'teachers',
    instance_name: 'teacher',
    view_path: 'teachers'
  )
  auth_action :teacher
  permit_action :permit_admin

  def update
    @teacher = resource
    @teacher.set_role teacher_params[:mod], *teacher_params[:mod_roles]
    head 204
  end

  private
    def teacher_params
      params.require(:teacher).permit(
        :mod, mod_roles: []
      )
    end
end
