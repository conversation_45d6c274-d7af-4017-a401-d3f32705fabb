class Pt::Admin::ReportsController < SimpleController::BaseController
  defaults(
    resource_class: Report,
    collection_name: 'reports',
    instance_name: 'report',
    view_path: 'reports'
  )

  auth_action :teacher
  permit_action :pt_admin
  belongs_to :source, param: :practice_id, parent_class: Pt::Practice, collection_name: :pt_activity_practices, shallow: true

  protected
    def begin_of_association_chain
      current_teacher
    end

  private
    def report_params
      params.require(:report).permit(
        :score, :state
      )
    end
end
