class Pt::Admin::CommentsController < SimpleController::BaseController
  defaults(
    resource_class: Comment,
    collection_name: 'comments',
    instance_name: 'comment',
    view_path: 'pt/comments'
  )

  auth_action :teacher
  permit_action :pt_admin

  protected
    def begin_of_association_chain
      current_teacher.send("pt_practice_#{params[:commentable]}").find(params[:commentable_id])
    end

end
