class Pt::Admin::RegistersController < SimpleController::BaseController
  defaults(
    resource_class: Register,
    collection_name: 'registers',
    instance_name: 'register',
    view_path: 'pt/registers'
  )

  auth_action :teacher
  permit_action :pt_admin
  belongs_to :source, collection_name: :pt_activity_practices, parent_class: Pt::Practice, param: :practice_id, shallow: true

  protected
    def begin_of_association_chain
      current_teacher
    end
end
