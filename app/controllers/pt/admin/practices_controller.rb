class Pt::Admin::PracticesController < SimpleController::BaseController
  defaults(
    resource_class: Pt::Practice,
    collection_name: 'practices',
    instance_name: 'practice',
    view_path: 'pt/practices/admin'
  )

  auth_action :teacher
  permit_action :pt_admin
  belongs_to :activity, parent_class: Pt::Activity, collection_name: :pt_activities, param: :activity_id, shallow: true

  protected
    def begin_of_association_chain
      current_teacher
    end

  private
    def practice_params
      params.require(:practice).permit(
        :state, :remark, :start_at, :end_at, meta: {},
        guide_teachers_attributes: [:id, :_destroy, :teacher_id, :type, :state, :remark, :start_at, :end_at, attachments: {}, files: []],
        practice_infos_attributes: [:id, :score, :state],
        theses_attributes: [:id, :score, :state, meta: {}],
        defences_attributes: [:id, :_destroy, :title, :start_at, :end_at, :address, :remrak, :score, :state, files: [], attachments: {}, meta: {}]
      )
    end
end
