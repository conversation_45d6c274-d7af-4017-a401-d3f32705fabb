class Pt::Admin::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Pt::Activity,
    collection_name: 'activities',
    instance_name: 'activity',
    view_path: 'pt/activities'
  )

  auth_action :teacher
  permit_action :pt_admin

  protected
    def begin_of_association_chain
      current_teacher
    end

    def method_for_association_chain
      'pt_activities'
    end

  private
    def activity_params
      params.require(:activity).permit(
        :name, :start_at, :end_at, :state, files: [], attachments: {}, meta: {}
      ).merge(school: current_teacher.school)
    end
end
