class Pt::Admin::ThesesController < SimpleController::BaseController
  defaults(
    resource_class: Thesis,
    collection_name: 'theses',
    instance_name: 'thesis',
    view_path: 'theses'
  )

  auth_action :teacher
  permit_action :pt_admin
  belongs_to :source, param: :practice_id, parent_class: Pt::Practice, collection_name: :pt_activity_practices, shallow: true

  protected
    def begin_of_association_chain
      current_teacher
    end

  private
    def thesis_params
      params.require(:thesis).permit(
        :state, :score, meta: {},
      )
    end
end
