class ::Pt::Teacher::Reply::PracticesController < SimpleController::BaseController
  defaults(
    resource_class: Pt::Practice,
    collection_name: 'practices',
    instance_name: 'practice',
    view_path: 'pt/practices/teacher'
  )
  auth_action :teacher
  belongs_to :activity, parent_class: Pt::Activity, collection_name: :reply_pt_activities, param: :activity_id, shallow: true

  protected

    def begin_of_association_chain
      current_teacher
    end
    def resource
      @practice = current_teacher.reply_pt_practices.find(params[:id])
    end

    def after_association_chain assoication
      assoication.where(id: current_teacher.reply_pt_practice_ids)
    end
  private

    def practice_params
      params.require(:practice).permit(
        :activity_id, :student_id, :school_id, :college_id, :major_id, :college_code, :major_code, :adminclass_id, :title, :company, :city, :address, :job, :origin, :state, :remark, :attachments, :meta, :start_at, :end_at, :deleted_at, :score
      )
    end
end
