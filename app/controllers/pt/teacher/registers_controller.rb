class Pt::Teacher::RegistersController < SimpleController::BaseController
  defaults(
    resource_class: Register,
    collection_name: 'registers',
    instance_name: 'register',
    view_path: 'pt/registers'
  )

  auth_action :teacher
  belongs_to :source, collection_name: :associated_pt_practices, parent_class: Pt::Practice, param: :practice_id, shallow: true

  protected
    def begin_of_association_chain
      current_teacher
    end
end
