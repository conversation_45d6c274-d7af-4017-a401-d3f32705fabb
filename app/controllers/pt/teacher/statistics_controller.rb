class Pt::Teacher::StatisticsController < SimpleController::BaseController
  defaults(
    resource_class: Pt::Practice,
    collection_name: 'practices',
    instance_name: 'practice',
    view_path: 'pt/statistics'
  )

  auth_action :teacher
  belongs_to :activity, parent_class: Pt::Activity, collection_name: :associated_pt_activities, param: :activity_id, optional: true

  def index
    practices = parent.practices.where(id: current_teacher.guide_practices.pluck(:id))
    practice_ids = practices.pluck(:id)
    companies = Pt::PracticeInfo::Company.where(practice_id: practice_ids)
    reports = Report::Practice.where(source_id: practice_ids)
    theses = Thesis::Practice.where(source_id: practice_ids)
    registers = Register::Practice.where(source_id: practice_ids)
    replies = Pt::Reply.where(pt_practice_id: practice_ids)

    @info = {
      company_count: companies.submited.count,
      report_count: reports.count,
      thesis_count: theses.published.count,
      report: {
        total: reports.count,
        normal: reports.count - reports.pending.count - reports.late.count,
        late: reports.late.count,
        pending: reports.pending.count,
        published: reports.published.count,
        scored: reports.scored.count
      },
      register: {
        total: registers.count,
        undo: registers.undo.count,
        done: registers.count - registers.undo.count,
        today: {
          total: registers.by_day(Date.today).count,
          done: registers.by_day(Date.today).done.count
        }
      },
      company: {
        total: companies.count,
        pending: companies.pending.count,
        submited: companies.submited.count,
        scored: companies.scored.count,
        released: companies.count - companies.pending.count
      },
      thesis: {
        total: theses.count,
        pending: theses.pending.count,
        published: theses.published.count,
        scored: theses.scored.count,
        released: theses.count - theses.pending.count
      },
      replies: {
        total: replies.count,
        scored: replies.scored.count,
        teacher_ids: replies.map(&:teacher_ids).flatten.uniq
      },
      defence: {}
    }
    render json: { info: @info }
  end

  protected

    def begin_of_association_chain
      current_teacher
    end

end
