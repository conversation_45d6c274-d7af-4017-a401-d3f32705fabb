class Pt::Teacher::<PERSON>lies<PERSON>ontroller < SimpleController::BaseController
  defaults(
    resource_class: Pt::Reply,
    collection_name: 'replies',
    instance_name: 'reply',
    view_path: 'pt/replies'
  )

  auth_action :teacher

  private

    def reply_params
      params.require(:reply).permit(
        :pt_practice_id, :student_id, :score, :content, teacher_ids: [], json_attributes: {},
      )
    end
end
