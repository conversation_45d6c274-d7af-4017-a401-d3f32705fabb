class Pt::Teacher::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Pt::Activity,
    collection_name: 'activities',
    instance_name: 'activity',
    view_path: 'pt/activities'
  )

  auth_action :teacher

  protected
    def begin_of_association_chain
      current_teacher
    end

    def method_for_association_chain
      'associated_pt_activities'
    end
end
