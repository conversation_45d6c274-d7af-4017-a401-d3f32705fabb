class Pt::Teacher::Theses<PERSON>ontroller < SimpleController::BaseController
  defaults(
    resource_class: Thesis,
    collection_name: 'theses',
    instance_name: 'thesis',
    view_path: 'theses'
  )

  auth_action :teacher
  belongs_to :source, parent_class: Pt::Practice, collection_name: :guide_practices, param: :practice_id, shallow: true

  protected
    def begin_of_association_chain
      current_teacher
    end

  private
    def thesis_params
      params.require(:thesis).permit(
        :state, :score, meta: {}
      )
    end
end
