class Pt::Teacher::ReportsController < SimpleController::BaseController
  defaults(
    resource_class: Report,
    collection_name: 'reports',
    instance_name: 'report',
    view_path: 'reports'
  )

  auth_action :teacher
  belongs_to :source, parent_class: Pt::Practice, collection_name: :associated_pt_practices, param: :practice_id, shallow: true

  protected
    def begin_of_association_chain
      current_teacher
    end

  private
    def report_params
      params.require(:report).permit(
        :score, :state
      )
    end
end
