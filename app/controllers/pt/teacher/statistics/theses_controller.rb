class Pt::Teacher::Statistics::ThesesController < SimpleController::BaseController
  defaults(
    resource_class: Thesis,
    collection_name: 'theses',
    instance_name: 'thesis',
    view_path: 'pt/statistics/theses'
  )

  auth_action :teacher
  belongs_to :activity, parent_class: Pt::Activity, collection_name: :associated_pt_activities, param: :activity_id, optional: true

  protected
    def begin_of_association_chain
      current_teacher
    end

    def end_of_association_chain
      practices = parent.practices.where(id: current_teacher.guide_practices.pluck(:id))
      theses = Thesis::Practice.where(source_id: practices.pluck(:id))

      @info = {
        total: theses.count,
        pending: theses.pending.count,
        published: theses.count - theses.pending.count
      }
      theses
    end
  
end
