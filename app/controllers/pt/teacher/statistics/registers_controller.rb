class Pt::Teacher::Statistics::RegistersController < SimpleController::BaseController
  defaults(
    resource_class: Register,
    collection_name: 'registers',
    instance_name: 'register',
    view_path: 'pt/statistics/registers'
  )

  auth_action :teacher
  belongs_to :activity, parent_class: Pt::Activity, collection_name: :associated_pt_activities, param: :activity_id, optional: true

  protected
    def begin_of_association_chain
      current_teacher
    end

    def end_of_association_chain
      practices = parent.practices.where(id: current_teacher.guide_practices.pluck(:id))
      registers = Register::Practice.where(source_id: practices.pluck(:id))
      registers = registers.by_day(params[:date]) if params[:date].present?
      @info = {
        total: registers.count,
        undo: registers.undo.count,
        done: registers.count - registers.undo.count
      }
      registers
    end
end
