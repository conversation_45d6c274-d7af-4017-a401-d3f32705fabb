class Pt::Teacher::Statistics::ReportsController < SimpleController::BaseController
  defaults(
    resource_class: Report,
    collection_name: 'reports',
    instance_name: 'report',
    view_path: 'pt/statistics/reports'
  )

  auth_action :teacher
  belongs_to :activity, parent_class: Pt::Activity, collection_name: :associated_pt_activities, param: :activity_id, optional: true

  protected
    def begin_of_association_chain
      current_teacher
    end

    def end_of_association_chain
      practices = parent.practices.where(id: current_teacher.guide_practices.pluck(:id))
      reports = Report::Practice.where(source_id: practices.pluck(:id))

      @info = {
        total: reports.count,
        normal: reports.count - reports.pending.count - reports.late.count,
        late: reports.late.count,
        pending: reports.pending.count,
        published: reports.published.count,
        scored:    reports.scored.count
      }
      reports
    end
end
