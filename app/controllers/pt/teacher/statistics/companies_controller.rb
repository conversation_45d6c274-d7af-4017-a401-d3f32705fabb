class Pt::Teacher::Statistics::CompaniesController < SimpleController::BaseController
  defaults(
    resource_class: Pt::PracticeInfo,
    collection_name: 'practice_infos',
    instance_name: 'info',
    view_path: 'pt/statistics/companies'
  )

  auth_action :teacher
  belongs_to :activity, parent_class: Pt::Activity, collection_name: :associated_pt_activities, param: :activity_id, optional: true

  protected
    def begin_of_association_chain
      current_teacher
    end

    def end_of_association_chain
      practices = parent.practices.where(id: current_teacher.guide_practices.pluck(:id))
      practice_infos = Pt::PracticeInfo::Company.where(practice_id: practices.pluck(:id))

      @info = {
        total: practice_infos.count,
        pending: practice_infos.pending.count,
        submited: practice_infos.count - practice_infos.pending.count
      }
      practice_infos
    end
end
