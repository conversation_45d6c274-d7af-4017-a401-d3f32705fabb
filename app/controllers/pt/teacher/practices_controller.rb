class Pt::Teacher::PracticesController < SimpleController::BaseController
  defaults(
    resource_class: Pt::Practice,
    collection_name: 'practices',
    instance_name: 'practice',
    view_path: 'pt/practices/teacher'
  )

  auth_action :teacher
  belongs_to :activity, parent_class: Pt::Activity, collection_name: :associated_pt_activities, param: :activity_id, shallow: true

  protected

    def begin_of_association_chain
      current_teacher
    end

    def resource
      @practice = current_teacher.associated_pt_practices.find(params[:id])
    end

    def after_association_chain assoication
      assoication.where(id: current_teacher.guide_practices.pluck(:id))
    end

  private

    def practice_params
      params.require(:practice).permit(
        :state, :remark, :start_at, :end_at, meta: {},
        practice_infos_attributes: [:id, :score, meta: {}, files: []],
        theses_attributes: [:id, :score, :state, meta: {}],
        defences_attributes: [:id, :_destroy, :title, :start_at, :end_at, :address, :remark, :score, :state, files: [], attachments: {}, meta: {}]
      )
    end
end
