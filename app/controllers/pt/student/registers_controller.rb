class Pt::Student::RegistersController < SimpleController::BaseController
  defaults(
    resource_class: Register,
    collection_name: 'registers',
    instance_name: 'register',
    view_path: 'pt/registers'
  )

  auth_action :student
  belongs_to :source, collection_name: :practices, parent_class: Pt::Practice, param: :practice_id, shallow: true

  # def create
  #   @register = parent.registers.by_day(Date.today).first || parent.registers.build
  #   @register.update(create_register_params)
  #   render json: @register.as_json, status: 201
  # end

  protected
    def begin_of_association_chain
      current_student
    end

  private
    def update_register_params
      params.require(:register).permit(
        :lon, :lat, :address, meta: {}
      ).merge(state: 'done')
    end

    def create_register_params
      params.require(:register).permit(
        :lon, :lat, :address, meta: {}
      ).merge(type: 'Register::Practice', user: current_student, state: 'done', created_at: Time.now)
    end
end
