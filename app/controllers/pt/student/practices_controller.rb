class Pt::Student::PracticesController < SimpleController::BaseController
  defaults(
    resource_class: Pt::Practice,
    collection_name: 'practices',
    instance_name: 'practice',
    view_path: 'pt/practices'
  )

  auth_action :student

  protected
    def begin_of_association_chain
      current_student
    end

    def after_association_chain association
      association
    end

  private
    def practice_params
      params.require(:practice).permit(
        practice_infos_attributes: [:id, files: [], attachments: {}, meta: {}]
      )
    end
end
