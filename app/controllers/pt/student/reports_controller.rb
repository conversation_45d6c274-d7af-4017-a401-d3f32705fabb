class Pt::Student::ReportsController < SimpleController::BaseController
  defaults(
    resource_class: Report,
    collection_name: 'reports',
    instance_name: 'report',
    view_path: 'reports'
  )

  auth_action :student
  belongs_to :source, param: :practice_id, parent_class: Pt::Practice, collection_name: :practices, shallow: true

  protected
    def begin_of_association_chain
      current_student
    end

    def report_params
      params.require(:report).permit(
       :title, :body, :state, meta: {}, attachments: {}, files: [],
      )
    end

end
