class Pt::Student::ThesesController < SimpleController::BaseController
  defaults(
    resource_class: Thesis,
    collection_name: 'theses',
    instance_name: 'thesis',
    view_path: 'theses'
  )

  auth_action :student
  belongs_to :source, param: :practice_id, parent_class: Pt::Practice, collection_name: :practices, shallow: true

  protected
    def begin_of_association_chain
      current_student
    end
end
