class Assessment::User::Entried::EntriesController < SimpleController::BaseController
  defaults(
    resource_class: Assessment::Entry,
    collection_name: 'entries',
    instance_name: 'entry',
    view_path: 'assessment/entries'
  )

  auth_action [:teacher, :student]

  protected

  def begin_of_association_chain
    current_auth
  end

  def method_for_association_chain
    :assessment_entries
  end

  def after_association_chain association
    association.where(activity_id: params[:activity_id])
  end
end
