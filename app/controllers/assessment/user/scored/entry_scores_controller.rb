class Assessment::User::Scored::EntryScoresController < SimpleController::BaseController
  defaults(
    resource_class: Assessment::Score,
    collection_name: 'scores',
    instance_name: 'score',
    view_path: 'assessment/scores'
  )
  auth_action [:teacher, :student]

  belongs_to :entry, collection_name: :score_assessment_entries, shallow: true

  protected

  def begin_of_association_chain
    current_auth
  end
end
