class Assessment::User::Scored::ScoresController < SimpleController::BaseController
  defaults(
    resource_class: Assessment::Score,
    collection_name: 'scores',
    instance_name: 'score',
    view_path: 'assessment/scores'
  )
  auth_action [:teacher, :student]

  protected

  def begin_of_association_chain
    current_auth
  end

  def method_for_association_chain
    :assessment_scores
  end

  def after_association_chain association
    association.where(activity_id: params[:activity_id])
  end

  private
    def score_params
      params.require(:score).permit(
       :score, :state, catalog_payload: {}, item_payload: {}
      )
    end
end
