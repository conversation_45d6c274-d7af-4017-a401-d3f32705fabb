class Assessment::User::Scored::EntriesController < SimpleController::BaseController
  defaults(
    resource_class: Assessment::Entry,
    collection_name: 'entries',
    instance_name: 'entry',
    view_path: 'assessment/entries'
  )

  auth_action [:teacher, :student]

  # belongs_to :activity, collection_name: :score_assessment_activities, shallow: true

  protected

  def begin_of_association_chain
    current_auth
  end

  def method_for_association_chain
    :score_assessment_entries
  end

  def after_association_chain association
    association.where(activity_id: params[:activity_id])
  end

  def entry_params
    if resource.activity.manage_teachers.where(id: current_auth.id).exists?
      params.require(:entry).permit(:department_level)
    else
      {}
    end
  end
end
