class Assessment::User::Related::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Assessment::Activity,
    collection_name: 'activities',
    instance_name: 'activity',
    view_path: 'assessment/activities'
  )

  auth_action [:teacher, :student]

  protected

  def begin_of_association_chain
    current_auth
  end

  def method_for_association_chain
    :relate_assessment_activities
  end
end
