class Assessment::Admin::GroupsController < SimpleController::BaseController
  defaults(
    resource_class: Assessment::Group,
    collection_name: 'groups',
    instance_name: 'group',
    view_path: 'assessment/groups'
  )

  auth_action :teacher
  permit_action :assessment_admin

  belongs_to :activity, collection_name: :assessment_activities, shallow: true

  protected

  def begin_of_association_chain
    current_auth.school
  end

  private
    def group_params
      params.require(:group).permit(
        :name, meta: {}, catalog_ids: [], paste_department_ids: []
      )
    end
end
