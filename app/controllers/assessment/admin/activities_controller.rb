class Assessment::Admin::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Assessment::Activity,
    collection_name: 'activities',
    instance_name: 'activity',
    view_path: 'assessment/activities'
  )
  auth_action :teacher
  permit_action :assessment_admin

  def create
    @activity = end_of_association_chain.create!(create_activity_params)
    respond_with @activity, template: "#{view_path}/show", status: 201
  end

  def export_entries
    score_users = Succ::AssessmentEntryExport::ScoreUser.init_by_activity resource
    path = Succ::AssessmentEntryExport.export_xlsx score_users
    send_file path
  end

  def export_compete_entries
    entries = resource.entries.order(score: :desc)
    path = Succ::AssessmentCompeteEntryExport.export_xlsx entries, headers: resource.succ_compete_entry_export_headers
    send_file path
  end

  def init_scores
    resource.init_scores
    head 201
  end

  def sync_entry_level
    resource.sync_entry_level
    head 201
  end

  protected

  def begin_of_association_chain
    current_auth
  end

  def method_for_association_chain
    :admin_assessment_activities
  end

  private
    def update_activity_params
      params.require(:activity).permit(
        :name, :content, :start_at, :end_at, :state,
        attachments: {}, stages: {}, meta: {},
        paste_manage_teacher_ids: [],
      )
    end

    def create_activity_params
      update_activity_params.merge(
        school: current_auth.school
      )
    end
end
