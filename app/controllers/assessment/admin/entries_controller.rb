class Assessment::Admin::EntriesController < SimpleController::BaseController
  defaults(
    resource_class: Assessment::Entry,
    collection_name: 'entries',
    instance_name: 'entry',
    view_path: 'assessment/entries'
  )

  auth_action :teacher
  permit_action :assessment_admin

  belongs_to :activity, collection_name: :assessment_activities, shallow: true

  def index
    index! do
      @score_dimension_id = params[:score_dimension_id]
    end
  end

  protected

  def begin_of_association_chain
    current_auth.school
  end

  private
    def entry_params
      params.require(:entry).permit(
        :sub_group_id, :user_type, :user_id,
        :code_level, :department_level,
        scores_attributes: [:id, :user_id, :user_type, :dimension_id, :_destroy],
        # scores_replace_attributes: [:user_id, :user_type, :dimension_id],
      )
    end
end
