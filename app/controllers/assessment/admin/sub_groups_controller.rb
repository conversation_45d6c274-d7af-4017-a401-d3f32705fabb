class Assessment::Admin::SubGroupsController < SimpleController::BaseController
  defaults(
    resource_class: Assessment::SubGroup,
    collection_name: 'sub_groups',
    instance_name: 'sub_group',
    view_path: 'assessment/sub_groups'
  )
  auth_action :teacher
  permit_action :assessment_admin

  belongs_to :activity, collection_name: :assessment_activities, shallow: true

  protected

  def begin_of_association_chain
    current_auth.school
  end

  private
    def sub_group_params
      params.require(:sub_group).permit(
        :group_id, :catalog_id, entries_attributes: [:user_type, :user_id, :_destroy]
      )
    end
end
