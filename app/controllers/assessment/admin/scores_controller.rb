class Assessment::Admin::ScoresController < SimpleController::BaseController
  defaults(
    resource_class: Assessment::Score,
    collection_name: 'scores',
    instance_name: 'score',
    view_path: 'assessment/scores'
  )

  auth_action :teacher
  permit_action :assessment_admin

  belongs_to :activity, collection_name: :assessment_activities, shallow: true do
    belongs_to :entry
  end

  protected

  def begin_of_association_chain
    current_auth.school
  end

  private
    def score_params
      params.require(:score).permit(
       :entry_id, :user_type, :user_id, :dimension_id, :score, :state, catalog_payload: {}, item_payload: {}
      )
    end
end
