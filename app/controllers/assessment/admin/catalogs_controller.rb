class Assessment::Admin::CatalogsController < SimpleController::BaseController
  defaults(
    resource_class: Assessment::Catalog,
    collection_name: 'catalogs',
    instance_name: 'catalog',
    view_path: 'assessment/catalogs'
  )
  auth_action :teacher
  permit_action :assessment_admin

  belongs_to :activity, collection_name: :assessment_activities, shallow: true

  protected

  def begin_of_association_chain
    current_auth.school
  end

  private
    def catalog_params
      params.require(:catalog).permit(
        :score_template_id, :name, :submit_workflow_id, :confirm_workflow_id
      )
    end
end
