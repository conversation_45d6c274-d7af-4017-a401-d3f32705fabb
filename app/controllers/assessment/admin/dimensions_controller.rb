class Assessment::Admin::DimensionsController < SimpleController::BaseController
  defaults(
    resource_class: Assessment::Dimension,
    collection_name: 'dimensions',
    instance_name: 'dimension',
    view_path: 'assessment/dimensions'
  )

  auth_action :teacher
  permit_action :assessment_admin

  belongs_to :activity, collection_name: :assessment_activities, shallow: true do
    belongs_to :catalog
  end

  def import
    xlsx_file = params[:file] || importable_class.import_excel_klass.new(params[:uid])
    dimension_id = params[:dimension_id]
    dimension = Assessment::Dimension.find(dimension_id)
    association = dimension.scores
    response = importable_class.import_xlsx(xlsx_file, association, **params.to_unsafe_h.symbolize_keys)
    render json: response, status: 201
  end

  protected

  def begin_of_association_chain
    current_auth.school
  end

  private
    def dimension_params
      params.require(:dimension).permit(
        :weight, :type, :score_template_id, :name, :stage, paste_assessment_dimension_ids: [], options: {}
      )
    end
end
