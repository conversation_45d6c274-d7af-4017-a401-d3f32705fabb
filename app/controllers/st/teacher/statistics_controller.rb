class St::Teacher::StatisticsController < SimpleController::BaseController
  auth_action :teacher

  def index
    options = {
      current_user: current_user,
      role:         'teacher',
      namespace:    params[:namespace],
      source_type:  params[:source_type],
      source_id:    params[:source_id],
      targets:      params[:targets]
    }

    obj = ::Statistics.new(options).load!

    targets = obj[:targets].ransack(params[:q])
                            .result
                            .paginate(page: params[:page], per_page: params[:per_page])

    @infos = {
      info: obj[:info] || {},
      source: obj[:source],
      targets: targets
    }
    respond_with  @infos, template: obj[:render_path]
  end
end
