class Ex::Teacher::ExportsController < SimpleController::BaseController
  auth_action :teacher

  def create
    options = export_params.merge(current_user: current_teacher)
    url = Excel.new(options).load!
    render json: { url: url }, status: 201
  end

  def titles
    datas = ::Translate.new(type: params[:type], action_type: 'export').load!
    render json: { datas: datas }, status: 201
  end

  private
    def export_params
      options = params.permit!.to_h
      options = options.slice!('controller', 'action', 'export')
      options.with_indifferent_access
    end
end
