class Ex::Admin::ImportsController < SimpleController::BaseController
  auth_action :teacher

  def create
    options = import_params
    ImportJob.perform_later(options)
    render json: { status: 'successed' }, status: 201
  end

  # 读取excel数据
  def read
    options = {
      file: params[:file]
    }
    response = Import.new(options).async!
    render json: response, status: 201
  end

  def async
    datas = Import.async!(params)
    render json: { status: 'successed', data: datas }, status: 201
  end

  def titles
    datas = ::Translate.new(type: params[:type]).load!
    render json: { status: 'successed', data: datas }, status: 201
  end

  def notify
    data = Import.notify!(uid: params[:uid])
    render json: { status: 'successed', data: data }, status: 201
  end

  def valid
    options = import_params.merge(action: 'valid')
    response = ImportJob.perform_later(options)
    render json: { status: 'successed' }, status: 201
  end

  def zip
    options = params.permit!.to_h
    options = { uid: options['uid'], attrs: options['attrs'] }
    response = Import.zip!(options)
    render json: response, status: 201
  end

  private
    def import_params
      options = params.permit!.to_h.merge(user_id: current_teacher.id)
      options = options.slice!('controller', 'action', 'import')
      options.with_indifferent_access
    end
end
