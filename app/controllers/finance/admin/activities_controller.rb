class Finance::Admin::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Activity,
    collection_name: 'activities',
    instance_name: 'activity',
    view_path: 'finance/activities'
  )

  auth_action :teacher
  permit_action :finance_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

    def method_for_association_chain
      :finance_activities
    end

  private
    def update_activity_params
      params.require(:activity).permit(
        :year, :school_name, :state
      )
    end

    def create_activity_params
      params.require(:activity).permit(
        :year, :school_name
      ).merge(state: 'todo')
    end
end
