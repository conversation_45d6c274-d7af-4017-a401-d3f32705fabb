class Finance::Admin::ProjectRolesController < SimpleController::BaseController
  defaults(
    resource_class: Finance::ProjectRole,
    collection_name: 'project_roles',
    instance_name: 'project_role',
    view_path: 'finance/project_roles'
  )

  auth_action :teacher
  permit_action :finance_admin

  belongs_to :finance_project, param: :project_id, shallow: true

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def project_role_params
      params.require(:project_role).permit(
        :approval_role_id, :teacher_id, :position,
      )
    end
end
