class Finance::Admin::CatalogsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Catalog,
    collection_name: 'catalogs',
    instance_name: 'catalog',
    view_path: 'finance/catalogs'
  )

  belongs_to :finance_project, param: :project_id, shallow: true

  auth_action :teacher
  permit_action :finance_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def catalog_params
      params.require(:catalog).permit(
        :name, :position
      )
    end
end
