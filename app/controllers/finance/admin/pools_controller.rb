class Finance::Admin::PoolsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Pool,
    collection_name: 'pools',
    instance_name: 'pool',
    view_path: 'finance/pools'
  )

  auth_action :teacher
  permit_action :finance_admin

  def create
    @activity = current_teacher.school.finance_activities.find(params[:activity_id])
    @pool = Finance::Pool.new @activity
    @pool.init!
    render json: @pool.as_plan_json, status: 201
  end

  def show
    @activity = current_teacher.school.finance_activities.find(params[:activity_id])
    @pool = Finance::Pool.new @activity
    render json: @pool.as_plan_json
  end
end
