class Finance::Admin::BudgetLogsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::BudgetLog,
    collection_name: 'budget_logs',
    instance_name: 'budget_log',
    view_path: 'finance/budget_logs'
  )

  auth_action :teacher
  permit_action :finance_admin

  belongs_to :activity, collection_name: :finance_activities, shallow: true

  def index
    index! do
      @avaliable_amount = Finance::BudgetLog.avaliable_amount(activity: @activity)
      @amount_info = Finance::BudgetLog.amount_info(activity: @activity)
    end
  end

  protected

  def begin_of_association_chain
    current_teacher.school
  end

  def	after_association_chain association
    association.order(id: :desc)
  end

  private

  def budget_log_params
    params.require(:budget_log).permit(
      :amount, :year, :project_id
    ).merge(
      creator: current_auth
    )
  end
end
