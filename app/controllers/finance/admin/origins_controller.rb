class Finance::Admin::OriginsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Origin,
    collection_name: 'origins',
    instance_name: 'origin',
    view_path: 'finance/origins'
  )

  auth_action :teacher
  permit_action :finance_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

    def method_for_association_chain
      :finance_origins
    end

  private
    def origin_params
      params.require(:origin).permit(
        :name, :code
      )
    end
end
