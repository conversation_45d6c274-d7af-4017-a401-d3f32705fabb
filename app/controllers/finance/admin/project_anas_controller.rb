class Finance::Admin::ProjectAnasController < SimpleController::BaseController
  defaults(
    resource_class: Finance::ProjectAna,
    collection_name: 'project_anas',
    instance_name: 'project_ana',
    view_path: 'finance/project_anas'
  )

  auth_action :teacher
  permit_action :finance_admin

  belongs_to :finance_project, param: :project_id, shallow: true

  protected
    def begin_of_association_chain
      current_teacher.school
    end

end
