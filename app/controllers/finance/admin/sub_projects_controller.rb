class Finance::Admin::SubProjectsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::SubProject,
    collection_name: 'projects',
    instance_name: 'project',
    view_path: 'finance/projects',
  )

  auth_action :teacher
  permit_action :finance_admin

  belongs_to :activity, collection_name: :finance_activities, shallow: true

  def import
    xlsx_file = params[:file] || importable_class.import_excel_klass.new(params[:uid])
    association = Finance::SubBudget.all
    response = importable_class.import_xlsx(xlsx_file, association, **params.to_unsafe_h.symbolize_keys)
    render json: response, status: 201
  end

  protected

  def begin_of_association_chain
    current_teacher.school
  end
end
