class Finance::Admin::VouchersController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Voucher,
    collection_name: 'vouchers',
    instance_name: 'voucher',
    view_path: 'finance/vouchers'
  )

  auth_action :teacher
  permit_action :finance_admin

  belongs_to :project, collection_name: :finance_projects, optional: true
  belongs_to :activity, collection_name: :finance_activities, optional: true
  belongs_to :budget, collection_name: :finance_budgets, optional: true
  belongs_to :budget_lock_grp, collection_name: :finance_budget_lock_grps, optional: true

  def export
    render json: { url: Finance::Voucher.export(collection) }, status: 201
  end

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def voucher_params
      params.require(:voucher).permit(
        :state, :amount, :remark, payee_meta: {}, meta: {}
      )
    end
end
