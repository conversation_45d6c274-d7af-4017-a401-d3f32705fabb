class Finance::Admin::ProjectCategoriesController < SimpleController::BaseController
  defaults(
    resource_class: Finance::ProjectCategory,
    collection_name: 'project_categories',
    instance_name: 'project_category',
    view_path: 'finance/project_categories'
  )

  auth_action :teacher
  permit_action :finance_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

    def method_for_association_chain
      :finance_project_categories
    end

  private
    def project_category_params
      params.require(:project_category).permit(
        :name, :position
      )
    end
end
