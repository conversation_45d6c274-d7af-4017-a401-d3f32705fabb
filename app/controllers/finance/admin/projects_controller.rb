class Finance::Admin::ProjectsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Project,
    collection_name: 'projects',
    instance_name: 'project',
    view_path: 'finance/projects'
  )

  auth_action :teacher
  permit_action :finance_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

    def method_for_association_chain
      :finance_projects
    end

    def after_association_chain association
      params[:activity_id].present? ?
        association.where(activity_id: params[:activity_id]) :
        association
    end

  private
    def update_project_params
      params.require(:project).permit(
        :uid, :name, :start_at, :end_at, :state, :remark, :department_code, :major_code, :owner_id, :project_category_id,
        :catalog, :type,:parent_project_id,
        project_roles_attributes: [:id, :approval_role_id, :teacher_id, :position],
      )
    end

    def create_project_params
      update_project_params.merge(
        school: current_teacher.school,
        activity_id: params[:activity_id],
      )
    end
end
