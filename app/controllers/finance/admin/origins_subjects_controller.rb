class Finance::Admin::OriginsSubjectsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::OriginsSubject,
    collection_name: 'origins_subjects',
    instance_name: 'origins_subject',
    view_path: 'finance/origins_subjects'
  )

  auth_action :teacher
  permit_action :finance_admin

  belongs_to :finance_activity, param: :activity_id, shallow: true

  def batch_update
    Finance::OriginsSubject.transaction do
      batch_origins_subjects_params[:origins_subjects]&.each do |os_params|
        current_teacher.school.origins_subjects.find(os_params[:id]).update plan_amount: os_params[:plan_amount]
      end
    end
    head 201
  end

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def origins_subject_params
      params.require(:origins_subject).permit(
        :plan_amount
      )
    end

    def batch_origins_subjects_params
      params.permit(
        origins_subjects: [
          :id, :plan_amount,
        ]
      )
    end
end
