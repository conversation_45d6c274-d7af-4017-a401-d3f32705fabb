class Finance::Admin::SubjectsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Subject,
    collection_name: 'subjects',
    instance_name: 'subject',
    view_path: 'finance/subjects'
  )
  auth_action :teacher
  permit_action :finance_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

    def method_for_association_chain
      :finance_subjects
    end

  private
    def subject_params
      params.require(:subject).permit(
        :name, :code, :remark, :catalog
      )
    end
end
