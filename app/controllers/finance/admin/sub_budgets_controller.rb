class Finance::Admin::SubBudgetsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Budget,
    collection_name: 'budgets',
    instance_name: 'budget',
    view_path: 'finance/budgets',
  )
  auth_action :teacher
  permit_action :finance_admin

  belongs_to :budget, collection_name: :finance_budgets

  protected

  def	begin_of_association_chain
    current_teacher.school
  end

  def	method_for_association_chain
    :sub_budgets
  end
end
