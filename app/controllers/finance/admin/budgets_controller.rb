class Finance::Admin::BudgetsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Budget,
    collection_name: 'budgets',
    instance_name: 'budget',
    view_path: 'finance/budgets'
  )

  belongs_to :finance_project, param: :project_id, shallow: true

  auth_action :teacher
  permit_action :finance_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def budget_params
      params.require(:budget).permit(
        :name, :catalog_id, :amount, :number, :unit_price, :unit, :remark, :origin_id, :subject_id, :purchase, :type, :parent_budget_id,
        executor_ids: []
      )
    end
end
