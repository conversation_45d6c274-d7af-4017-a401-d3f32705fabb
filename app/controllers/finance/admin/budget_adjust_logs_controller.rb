class Finance::Admin::BudgetAdjustLogsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::BudgetAdjustLog,
    collection_name: 'budget_adjust_logs',
    instance_name: 'budget_adjust_log',
    view_path: 'finance/budget_adjust_logs'
  )
  auth_action :teacher
  permit_action :finance_admin

  belongs_to :activity, collection_name: :finance_activities, shallow: true

  protected

  def	begin_of_association_chain
    current_teacher.school
  end

  private
    def update_budget_adjust_log_params
      params.require(:budget_adjust_log).permit(
        :amount, :catalog, :origin_id, :remark, :project_id
      )
    end

    def create_budget_adjust_log_params
      update_budget_adjust_log_params.merge(
        year: Time.zone.now.year,
        creator: current_teacher,
        school: current_teacher.school,
      )
    end
end
