class Finance::Admin::ApprovalRolesController < SimpleController::BaseController
  defaults(
    resource_class: Finance::ApprovalRole,
    collection_name: 'approval_roles',
    instance_name: 'approval_role',
    view_path: 'finance/approval_roles'
  )

  auth_action :teacher
  permit_action :finance_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

    def method_for_association_chain
      :finance_approval_roles
    end

  private
    def approval_role_params
      params.require(:approval_role).permit(
        :name, :remark
      )
    end
end
