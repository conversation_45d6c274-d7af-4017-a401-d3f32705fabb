class Finance::Admin::BudgetLockGrpsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::BudgetLockGrp,
    collection_name: 'budget_lock_grps',
    instance_name: 'budget_lock_grp',
    view_path: 'finance/budget_lock_grps',
  )

  auth_action :teacher
  permit_action :finance_admin

  belongs_to :project, collection_name: :finance_projects, optional: true
  belongs_to :budget, collection_name: :finance_budgets, optional: true

  protected

  def begin_of_association_chain
    current_teacher.school
  end
end
