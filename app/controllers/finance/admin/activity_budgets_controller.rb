class Finance::Admin::ActivityBudgetsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Budget,
    collection_name: 'budgets',
    instance_name: 'budget',
    view_path: 'finance/budgets',
  )

  auth_action :teacher
  permit_action :finance_admin

  belongs_to :activity, collection_name: :finance_activities

  protected
    def begin_of_association_chain
      current_teacher.school
    end
end
