class Finance::Asset::BudgetLockGrpsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::BudgetLockGrp,
    collection_name: 'budget_lock_grps',
    instance_name: 'budget_lock_grp',
    view_path: 'finance/budget_lock_grps',
  )

  protected

  def	after_association_chain association
    teacher = ::Teacher.find_by! code: params[:teacher_code]
    association.where(owner: teacher).doing
  end


  private
    def budget_lock_grp_params
      params.require(:budget_lock_grp).permit(
        :state
      )
    end
end
