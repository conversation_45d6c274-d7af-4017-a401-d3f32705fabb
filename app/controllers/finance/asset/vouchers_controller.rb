class Finance::Asset::VouchersController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Voucher,
    collection_name: 'vouchers',
    instance_name: 'voucher',
    view_path: 'finance/vouchers'
  )

  auth_action :teacher
  permit_action :asset_admin, :asset_operate

  belongs_to :budget, collection_name: :finance_asset_budgets, shallow: true

  protected

  def begin_of_association_chain
    current_teacher.school
  end
end
