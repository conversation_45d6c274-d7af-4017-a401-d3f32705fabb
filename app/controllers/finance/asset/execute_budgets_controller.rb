class Finance::Asset::ExecuteBudgetsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Budget,
    collection_name: 'budgets',
    instance_name: 'budget',
    view_path: 'finance/budgets'
  )

  protected

  def	begin_of_association_chain
    ::Teacher.find_by! code: params[:teacher_code]
  end

  def	method_for_association_chain
    :finance_execute_budgets
  end
end
