class Finance::Asset::BudgetLocksController < SimpleController::BaseController
  defaults(
    resource_class: Finance::BudgetLock,
    collection_name: 'budget_locks',
    instance_name: 'budget_lock',
    view_path: 'finance/budget_locks'
  )
  protected

  def	after_association_chain association
    teacher = ::Teacher.find_by! code: params[:teacher_code]
    association.where(owner: teacher).doing
  end

  private
    def create_budget_lock_params
      params.require(:budget_lock).permit(
        :budget_id, :amount, :name
      ).merge(
        owner: ::Teacher.find_by!(code: params[:teacher_code])
      )
    end

    def update_budget_lock_params
      params.require(:budget_lock).permit(
        :state
      )
    end
end
