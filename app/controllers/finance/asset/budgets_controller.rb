class Finance::Asset::BudgetsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Budget,
    collection_name: 'budgets',
    instance_name: 'budget',
    view_path: 'finance/budgets/asset'
  )
  auth_action :teacher
  permit_action :asset_admin, :asset_operate

  protected

  def begin_of_association_chain
    current_teacher.school
  end

  def method_for_association_chain
    :finance_asset_budgets
  end

  private
    def budget_params
      params.require(:budget).permit(
        :payment_way
      )
    end
end
