class Finance::Teacher::OriginsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Origin,
    collection_name: 'origins',
    instance_name: 'origin',
    view_path: 'finance/origins'
  )

  auth_action :teacher

  protected
    def begin_of_association_chain
      current_teacher.school
    end

    def method_for_association_chain
      :finance_origins
    end
end
