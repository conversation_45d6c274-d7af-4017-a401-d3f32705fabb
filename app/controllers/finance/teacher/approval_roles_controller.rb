class Finance::Teacher::A<PERSON><PERSON>alRolesController < SimpleController::BaseController
  defaults(
    resource_class: Finance::ApprovalRole,
    collection_name: 'approval_roles',
    instance_name: 'approval_role',
    view_path: 'finance/approval_roles'
  )
  auth_action :teacher

  protected
    def begin_of_association_chain
      current_teacher.school
    end

    def method_for_association_chain
      :finance_approval_roles
    end
end
