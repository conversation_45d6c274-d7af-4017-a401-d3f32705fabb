class Finance::Teacher::SubjectsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Subject,
    collection_name: 'subjects',
    instance_name: 'subject',
    view_path: 'finance/subjects'
  )
  auth_action :teacher

  protected
    def begin_of_association_chain
      current_teacher.school
    end

    def method_for_association_chain
      :finance_subjects
    end

end
