class Finance::Teacher::Own::ProjectAnasController < SimpleController::BaseController
  defaults(
    resource_class: Finance::ProjectAna,
    collection_name: 'project_anas',
    instance_name: 'project_ana',
    view_path: 'finance/project_anas'
  )

  auth_action :teacher

  belongs_to :project, collection_name: :finance_own_projects, shallow: true

  protected
    def begin_of_association_chain
      current_teacher
    end

  # private
    # def project_ana_params
    #   # params.require(:project_ana).permit(
    #   #   :day, :completed_ratio, :doing_ratio, :normal_ratio, :project_id
    #   # )
    # end
end
