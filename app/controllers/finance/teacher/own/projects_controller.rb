class Finance::Teacher::Own::ProjectsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Project,
    collection_name: 'projects',
    instance_name: 'project',
    view_path: 'finance/projects'
  )

  auth_action :teacher

  # belongs_to :activity, shallow: true

  def approval
    @instance = resource.approval!
    respond_with @instance, template: "bpm/instances/show", status: 201
  end

  protected
    def begin_of_association_chain
      current_teacher
    end

    def after_association_chain association
      params[:activity_id].present? ?
        association.where(activity_id: params[:activity_id]) :
        association
    end

    def method_for_association_chain
      :finance_own_projects
    end
end
