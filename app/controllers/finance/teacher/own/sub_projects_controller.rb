class Finance::Teacher::Own::SubProjectsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::SubProject,
    collection_name: 'projects',
    instance_name: 'project',
    view_path: 'finance/projects',
    importable_class: Finance::Admin::SubProjectsExcel
  )

  auth_action :teacher

  belongs_to :project, collection_name: :finance_own_projects

  def import
    xlsx_file = params[:file] || importable_class.import_excel_klass.new(params[:uid])
    association = Finance::SubBudget.all
    response = importable_class.import_xlsx(xlsx_file, association, **params.to_unsafe_h.symbolize_keys)
    render json: response, status: 201
  end

  protected

  def begin_of_association_chain
    current_teacher
  end

  def method_for_association_chain
    :sub_projects
  end
end
