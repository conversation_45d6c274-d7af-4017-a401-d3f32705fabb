class Finance::Teacher::Own::Sub<PERSON>udgetsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::SubBudget,
    collection_name: 'budgets',
    instance_name: 'budgets',
    view_path: 'finance/budgets',
  )
  auth_action :teacher

  belongs_to :budget, collection_name: :finance_own_budgets, optional: true

  protected

  def begin_of_association_chain
    current_teacher
  end

  def	method_for_association_chain
    :sub_budgets
  end
end
