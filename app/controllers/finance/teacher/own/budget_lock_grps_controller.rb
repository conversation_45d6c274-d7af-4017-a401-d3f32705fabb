class Finance::Teacher::Own::BudgetLockGrpsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::BudgetLockGrp,
    collection_name: 'budget_lock_grps',
    instance_name: 'budget_lock_grp',
    view_path: 'finance/budget_lock_grps',
  )
  auth_action :teacher

  belongs_to :project, collection_name: :finance_own_projects, optional: true
  belongs_to :budget, collection_name: :finance_own_budgets, optional: true

  protected

  def begin_of_association_chain
    current_teacher
  end
end
