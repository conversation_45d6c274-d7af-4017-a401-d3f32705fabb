class Finance::Teacher::Own::ExecuteBudgetsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::ExecuteBudget,
    collection_name: 'execute_budgets',
    instance_name: 'execute_budget',
    view_path: 'finance/execute_budgets'
  )

  auth_action :teacher

  belongs_to :project, collection_name: :finance_own_projects, shallow: true

  def executors
    executors_hash = collection.group(:teacher).count.transform_keys { |teacher| { teacher_id: teacher.id, code: teacher.code, name: teacher.name} }
    render json: executors_hash
  end

  def batch_update
    collection
    batch_update_budget_params[:teacher_ids]&.each do |teacher_id|
      batch_update_budget_params[:budget_ids]&.each do |budget_id|
        @project.execute_budgets.find_or_create_by teacher_id: teacher_id, budget_id: budget_id
      end
    end
    head 201
  end

  protected
    def begin_of_association_chain
      current_teacher
    end

  private
    def execute_budget_params
      params.require(:execute_budget).permit(
        :teacher_id, :budget_id
      )
    end

    def batch_update_budget_params
      params.require(:execute_budgets).permit(
        teacher_ids: [], budget_ids: []
      )
    end
end
