class Finance::Teacher::Own::ExecutorsController < SimpleController::BaseController
  defaults(
    resource_class: Teacher,
    collection_name: 'executors',
    instance_name: 'executor',
    view_path: 'finance/executors'
  )

  auth_action :teacher

  belongs_to :project, collection_name: :finance_own_projects

  def create
    @project = current_teacher.finance_own_projects.find(params[:project_id])
    if @project.executor_ids.include?(create_executor_params[:executor_id]) # 直接全部新建
      create_executor_params[:budget_ids].each do |budget_id|
        @project.execute_budgets.find_or_create_by!(
          teacher_id: create_executor_params[:executor_id],
          budget_id: budget_id,
        )
      end
    else
      @project.execute_budgets.where(teacher: create_executor_params[:executor_id])&.destroy_all
      create_executor_params[:budget_ids].each do |budget_id|
        @project.execute_budgets.find_or_create_by!(
          teacher_id: create_executor_params[:executor_id],
          budget_id: budget_id,
        )
      end
    end
    @executor = @project.executors.find(create_executor_params[:executor_id])
    respond_with @executor, template: "#{view_path}/show", status: 201
  end

  def update
    @project = current_teacher.finance_own_projects.find(params[:project_id])
    @project.execute_budgets.where(teacher: params[:id])&.destroy_all
    create_executor_params[:budget_ids].each do |budget_id|
      @project.execute_budgets.find_or_create_by!(
        teacher_id: params[:id],
        budget_id: budget_id,
      )
    end
    head 204
  end

  def destroy
    @project = current_teacher.finance_own_projects.find(params[:project_id])
    @project.execute_budgets.where(teacher: params[:id])&.destroy_all
    head 204
  end

  protected
    def begin_of_association_chain
      current_teacher
    end


  private
    def update_executor_params
      params.require(:executor).permit(
        budget_ids: []
      )
    end

    def create_executor_params
      params.require(:executor).permit(
        :executor_id, budget_ids: []
      )
    end
end
