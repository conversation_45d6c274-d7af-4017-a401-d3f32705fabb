class Finance::Teacher::Own::BudgetsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Budget,
    collection_name: 'budgets',
    instance_name: 'budget',
    view_path: 'finance/budgets'
  )

  auth_action :teacher

  belongs_to :project, collection_name: :finance_own_projects, shallow: true

  protected
    def begin_of_association_chain
      current_teacher
    end

  private
    def budget_params
      params.require(:budget).permit(
        executor_ids: [],
        execute_budgets_attributes: [:id, :amount]
      )
    end
end
