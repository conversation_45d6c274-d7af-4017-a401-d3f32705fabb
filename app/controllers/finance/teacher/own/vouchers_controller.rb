class Finance::Teacher::Own::VouchersController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Voucher,
    collection_name: 'vouchers',
    instance_name: 'voucher',
    view_path: 'finance/vouchers'
  )

  auth_action :teacher

  belongs_to :budget, collection_name: :finance_own_budgets, optional: true
  belongs_to :project, collection_name: :finance_own_projects, optional: true
  belongs_to :budget_lock_grp, collection_name: :finance_own_budget_lock_grps, optional: true

  def export
    render json: { url: Finance::Voucher.export(collection) }, status: 201
  end

  protected
    def begin_of_association_chain
      current_teacher
    end

    def resource
      @voucher = Finance::Voucher.find params[:id]
    end

end
