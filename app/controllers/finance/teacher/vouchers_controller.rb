class Finance::Teacher::VouchersController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Voucher,
    collection_name: 'vouchers',
    instance_name: 'voucher',
    view_path: 'finance/vouchers'
  )

  auth_action :teacher

  belongs_to :budget_lock_grp, collection_name: :finance_budget_lock_grps, optional: true

  def show
    @voucher = Finance::Voucher.find(params[:id])
    super
  end

  def export
    render json: { url: Finance::Voucher.export(collection) }, status: 201
  end

  protected
    def begin_of_association_chain
      current_teacher
    end

    def method_for_association_chain
      :finance_vouchers
    end

    def after_association_chain association
      params[:activity_id].present? ?
        association.where(activity_id: params[:activity_id]) :
        association
    end

  private
    def update_voucher_params
      voucher_params = params.require(:voucher).permit(
        :type, :state, :amount, :remark, :tax, payee_meta: {}, meta: {},
        payments_attributes: [:id, :budget_id, :amount, :_destroy],
        receipts_attributes: [:id, :type, :name, :_destroy, attachment: {}, meta: {}],
      )
      voucher_params[:receipts_attributes]&.each do |receipts_attribute|
        receipts_attribute.merge!(
          creator: current_teacher,
        )
      end
      voucher_params
    end

    def create_voucher_params
      voucher_params = params.require(:voucher).permit(
        :type, :state, :amount, :remark, :tax, payee_meta: {}, meta: {},
        payments_attributes: [:id, :budget_id, :amount],
        receipts_attributes: [:id, :type, :name, attachment: {}, meta: {}],
      ).merge(
        teacher_id: current_teacher.id,
        school_id: current_teacher.school&.id,
        activity_id: params[:activity_id],
      )
      voucher_params[:receipts_attributes]&.each do |receipts_attribute|
        receipts_attribute.merge!(
          creator: current_teacher,
        )
      end
      voucher_params
    end
end
