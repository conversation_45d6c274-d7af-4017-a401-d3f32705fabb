class Finance::Teacher::PaymentsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Payment,
    collection_name: 'payments',
    instance_name: 'payment',
    view_path: 'finance/payments'
  )

  auth_action :teacher

  belongs_to :voucher, collection_name: :finance_vouchers, shallow: true

  protected
    def begin_of_association_chain
      current_teacher
    end

  private
    def payment_params
      params.require(:payment).permit(
        :budget_id, :origin_id, :subject_id, :amount, :budget_lock_id
      )
    end
end
