class Finance::Teacher::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Activity,
    collection_name: 'activities',
    instance_name: 'activity',
    view_path: 'finance/activities'
  )

  auth_action :teacher

  protected
    def begin_of_association_chain
      current_teacher.school
    end

    def method_for_association_chain
      :finance_activities
    end
end
