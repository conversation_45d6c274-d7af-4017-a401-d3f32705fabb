class Finance::Teacher::Execute::BudgetLocksController < SimpleController::BaseController
  defaults(
    resource_class: Finance::BudgetLock,
    collection_name: 'budget_locks',
    instance_name: 'budget_lock',
    view_path: 'finance/budget_locks'
  )
  auth_action :teacher

  belongs_to :project, collection_name: :finance_execute_projects, shallow: true

  protected

  def	begin_of_association_chain
    current_teacher
  end

  def	after_association_chain association
    association.where(owner: current_teacher, state: 'doing')
  end

  private
    def budget_lock_params
      params.require(:budget_lock).permit(
        :state
      )
    end
end
