class Finance::Teacher::Execute::VouchersController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Voucher,
    collection_name: 'vouchers',
    instance_name: 'voucher',
    view_path: 'finance/vouchers'
  )
  auth_action :teacher

  belongs_to :budget, collection_name: :finance_execute_budgets, optional: true

  def export
    render json: { url: Finance::Voucher.export(collection) }, status: 201
  end

  protected
    def begin_of_association_chain
      current_teacher
    end

    def after_association_chain association
      association.where(teacher_id: current_teacher.id)
    end

    def resource
      @voucher = Finance::Voucher.find params[:id]
    end
end
