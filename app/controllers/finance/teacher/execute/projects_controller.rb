class Finance::Teacher::Execute::ProjectsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Project,
    collection_name: 'projects',
    instance_name: 'project',
    view_path: 'finance/projects'
  )

  auth_action :teacher

  protected
    def begin_of_association_chain
      current_teacher
    end

    def method_for_association_chain
      :finance_execute_projects
    end

    def after_association_chain association
      params[:activity_id].present? ?
        association.where(activity_id: params[:activity_id]) :
        association
    end
end
