class Finance::Teacher::Execute::BudgetsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Budget,
    collection_name: 'budgets',
    instance_name: 'budget',
    view_path: 'finance/budgets'
  )
  auth_action :teacher

  protected
    def begin_of_association_chain
      current_teacher
    end

    def method_for_association_chain
      :finance_execute_budgets
    end

    def after_association_chain association
      association.where(project_id: params[:project_id])
    end

end
