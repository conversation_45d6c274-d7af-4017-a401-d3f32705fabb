class Finance::Teacher::ReceiptsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::Receipt,
    collection_name: 'receipts',
    instance_name: 'receipt',
    view_path: 'finance/receipts'
  )
  auth_action :teacher

  belongs_to :voucher, collection_name: :finance_vouchers, optional: true

  protected
    def begin_of_association_chain
      current_teacher
    end

    def method_for_association_chain
      :finance_receipts
    end

  private
    def update_receipt_params
      params.require(:receipt).permit(
        :type, :name, attachment: {}, meta: {}
      )
    end

    def create_receipt_params
      update_receipt_params.merge(
        creator: current_teacher
      )
    end
end
