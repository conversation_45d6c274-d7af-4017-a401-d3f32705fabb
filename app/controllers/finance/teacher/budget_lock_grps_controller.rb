class Finance::Teacher::BudgetLockGrpsController < SimpleController::BaseController
  defaults(
    resource_class: Finance::BudgetLockGrp,
    collection_name: 'budget_lock_grps',
    instance_name: 'budget_lock_grp',
    view_path: 'finance/budget_lock_grps',
  )
  auth_action :teacher

  def done
    resource.update!(state: 'done') if resource.state == 'doing'
    respond_resource
  end

  def statistics
    render json: end_of_association_chain.group(:state).count, status: 201
  end

  protected

  def	begin_of_association_chain
    current_teacher
  end

  def	method_for_association_chain
    :finance_budget_lock_grps
  end
end
