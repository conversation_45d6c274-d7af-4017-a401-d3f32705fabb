class Ep::Inspect::StatisticsController < SimpleController::BaseController
  defaults(
    resource_class: Ep::Activity,
    collection_name: 'activities',
    instance_name: 'activity',
    view_path: 'ep/statistics'
  )

  auth_action :teacher

  def index
    activities = current_teacher.ep_inspect_activities
    @activity = activities.last
    date = params['date'].present? ? params[:date] : Date.today.strftime('%F')
    return render json: { activity: {}, info: {} } if activities.count < 1
    @info = EpActivityStatisticService.new(activities: activities, date: date).perform
    super
  end

  def registers
    @registers = Register::Ep.ransack(params[:q])
                             .result
                             .paginate(page: params[:page], per_page: params[:per_page])
    respond_with @registers, template: "#{view_path}/registers", status: 201
  end
end
