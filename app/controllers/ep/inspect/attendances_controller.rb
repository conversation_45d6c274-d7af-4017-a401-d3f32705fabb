class Ep::Inspect::AttendancesController < SimpleController::BaseController
  defaults(
    resource_class: Ep::Attendance,
    collection_name: 'attendances',
    instance_name: 'attendance',
    view_path: 'ep/attendances'
  )

  auth_action :teacher
  belongs_to :activity, parent_class: Pt::Activity, collection_name: :ep_inspect_activities, param: :activity_id, shallow: true

  protected
    def begin_of_association_chain
      current_teacher
    end
end
