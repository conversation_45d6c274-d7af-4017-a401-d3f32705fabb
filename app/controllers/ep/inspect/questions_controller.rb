class Ep::Inspect::QuestionsController < SimpleController::BaseController
  defaults(
    resource_class: Register,
    collection_name: 'registers',
    instance_name: 'register',
    view_path: 'ep/questions'
  )

  auth_action :teacher
  belongs_to :activity, collection_name: :ep_inspect_activities, parent_class: Ep::Activity, param: :activity_id, shallow: true

  protected
    def begin_of_association_chain
      current_teacher
    end

    def after_association_chain association
      association.where(state: 'done')
    end
end
