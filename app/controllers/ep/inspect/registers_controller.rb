class Ep::Inspect::RegistersController < SimpleController::BaseController
  defaults(
    resource_class: Register,
    collection_name: 'registers',
    instance_name: 'register',
    view_path: 'ep/registers'
  )

  auth_action :teacher
  belongs_to :source, collection_name: :ep_inspect_attendances, parent_class: Ep::Attendance, param: :attendance_id, shallow: true

  protected
    def begin_of_association_chain
      current_teacher
    end

    def register_params
      params.require(:register).permit(
        :state,  meta: {}
      )
    end
end
