class Ep::Inspect::Statistics::RegistersController < SimpleController::BaseController
  defaults(
    resource_class: Register,
    collection_name: 'registers',
    instance_name: 'register',
    view_path: 'ep/registers/admin'
  )

  auth_action :teacher
  belongs_to :activity, parent_class: Ep::Activity, collection_name: :ep_inspect_activities, param: :activity_id, optional: true

  protected
    def begin_of_association_chain
      current_teacher
    end
end
