class Ep::User::AttendancesController < SimpleController::BaseController
  defaults(
    resource_class: Ep::Attendance,
    collection_name: 'attendances',
    instance_name: 'attendance',
    view_path: 'ep/attendances'
  )

  auth_action [:teacher, :student]

  protected
    def begin_of_association_chain
      current_auth
    end

    def method_for_association_chain
      'ep_attendances'
    end
end
