class Ep::User::RegistersController < SimpleController::BaseController
  defaults(
    resource_class: Register,
    collection_name: 'registers',
    instance_name: 'register',
    view_path: 'ep/registers/user'
  )

  auth_action [:teacher, :student]
  belongs_to :source, collection_name: :ep_attendances, parent_class: Ep::Attendance, param: :attendance_id, shallow: true

  protected
    def begin_of_association_chain
      current_auth
    end

  private
    def register_params
      params.require(:register).permit(
        :state, :lon, :lat, :address, :province, :city, :district, :date, :created_at, meta: {}
      )
    end
end
