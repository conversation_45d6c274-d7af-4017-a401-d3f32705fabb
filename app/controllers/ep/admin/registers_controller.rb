class Ep::Admin::RegistersController < SimpleController::BaseController
  defaults(
    resource_class: Register::Ep,
    collection_name: 'registers',
    instance_name: 'register',
    view_path: 'ep/registers'
  )

  auth_action :teacher
  permit_action :ep_admin
  belongs_to :source, collection_name: :ep_activity_attendances, parent_class: Ep::Attendance, param: :attendance_id, optional: true

  protected
    def begin_of_association_chain
      current_teacher.school
    end

    def method_for_association_chain
      :ep_registers
    end

    def register_params
      register = params.require(:register).permit(
        :state, :date,  meta: {}
      )
    end
end
