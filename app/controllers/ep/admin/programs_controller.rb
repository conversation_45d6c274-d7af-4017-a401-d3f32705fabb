class Ep::Admin::ProgramsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::Program,
    collection_name: 'programs',
    instance_name: 'program',
    view_path: 'ep/programs'
  )

  auth_action :teacher
  belongs_to :activity, collection_name: :ep_activities, parent_class: Ep::Activity, param: :activity_id, shallow: true

  protected
    def begin_of_association_chain
      current_teacher.school
    end

end
