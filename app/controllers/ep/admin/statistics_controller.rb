class Ep::Admin::StatisticsController < SimpleController::BaseController
  defaults(
    resource_class: Ep::Activity,
    collection_name: 'activities',
    instance_name: 'activity',
    view_path: 'ep/statistics'
  )

  auth_action :teacher
  permit_action :ep_admin, only: [:index, :registers, :activities]

  def index
    activities = current_teacher.manage_ep_activities
    @activity = activities.last
    date = params['date'].present? ? params[:date] : Date.today.strftime('%F')
    return render json: { activity: {}, info: {} } if activities.count < 1
    @info = EpActivityStatisticService.new(activities: activities, date: date).perform
    super
  end

  def registers
    @registers = Register::Ep.ransack(params[:q])
                             .result
                             .paginate(page: params[:page], per_page: params[:per_page])
    respond_with @registers, template: "#{view_path}/registers"
  end

  def activities
    @activities = Ep::Activity.ransack(params[:q])
                              .result
                              .paginate(page: params[:page], per_page: params[:per_page])
    respond_with @activities, template: "#{view_path}/activities"
  end

  def export
    options = {
      activities: params[:id].present? ? Ep::Activity.where(id: params[:id]) : current_teacher.manage_ep_activities,
      date: params[:date] || Date.today.strftime('%F'),
      type: params[:type]&.downcase || 'teacher'
    }

    url = ExportEpRegisterService.new(options).perform
    render json: { url: url }, status: 201
  end
end
