class Ep::Admin::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Ep::Activity,
    collection_name: 'activities',
    instance_name: 'activity',
    view_path: 'ep/activities/admin'
  )

  auth_action :teacher
  permit_action :ep_admin

  def batch_update
    (inspect_activity_params[:activity_ids] || []).each do |activity_id|
      activity = Ep::Activity.find(activity_id)
      (inspect_activity_params[:ep_inspect_teacher_ids] || []).each do |teacher_id|
        activity.ep_activity_inspectors.find_or_create_by(user_id: teacher_id, user_type: 'Teacher')
      end
    end
    head 201
  end

  def batch_assign_manage
    manage_activity_params[:activity_ids].each do |activity_id|
      activity = Ep::Activity.find(activity_id)
      activity.manage_teacher_ids = manage_activity_params[:manage_teacher_ids]
    end
  end

  protected
    def begin_of_association_chain
      current_teacher
    end

    def method_for_association_chain
      :manage_ep_activities
    end

  private
    def update_activity_params
      params.require(:activity).permit(
        :name, :state, :start_at, :end_at, :add_student, meta: {},
        teacher_ids: [], program_ids: [], ep_inspect_teacher_ids: [], manage_teacher_ids: []
      )
    end

    def create_activity_params
      update_activity_params.merge(
        teacher_id: current_teacher.id,
        school: current_teacher.school,
        paste_manageteacher_ids: [current_teacher.id],
      )
    end

    def inspect_activity_params
      params.require(:activity).permit(
        activity_ids: [], ep_inspect_teacher_ids: []
      )
    end

    def manage_activity_params
      params.require(:activity).permit(
        activity_ids: [], manage_teacher_ids: []
      )
    end
end
