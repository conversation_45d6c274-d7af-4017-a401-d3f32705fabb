class Ep::Admin::QuestionsController < SimpleController::BaseController
  defaults(
    resource_class: Register::Ep,
    collection_name: 'registers',
    instance_name: 'register',
    view_path: 'ep/questions'
  )

  auth_action :teacher
  belongs_to :activity, collection_name: :ep_activities, parent_class: Ep::Activity, param: :activity_id, optional: true

  protected
    def begin_of_association_chain
      params[:activity_id].present? ? current_teacher.school : current_teacher
    end

    def method_for_association_chain
      :ep_registers
    end

    def after_association_chain association
      association.where(state: 'done')
    end
end


