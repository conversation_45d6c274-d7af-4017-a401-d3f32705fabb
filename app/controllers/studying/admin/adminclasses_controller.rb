class Studying::Admin::AdminclassesController < SimpleController::BaseController
  defaults(
    resource_class: Adminclass,
    collection_name: 'adminclasses',
    instance_name: 'adminclass',
    view_path: 'adminclasses'
  )

  auth_action :teacher
  permit_action :studying_admin

  protected

  def begin_of_association_chain
    current_teacher.school
  end

  private
    def adminclass_params
      params.require(:adminclass).permit(
        :code, :name, :short_name, :grade, :major_id, :std_type, :plan_count, :department_id, :program_id, teacher_ids: []
      )
    end
end
