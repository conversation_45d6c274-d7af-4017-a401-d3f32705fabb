class Studying::Admin::Welcome::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Studying::Welcome::Activity,
    collection_name: 'activities',
    instance_name: 'activity',
    view_path: 'studying/welcome/activities'
  )
  auth_action :teacher
  permit_action :studying_admin

  def import
    head 201
  end

  def clone
    @activity = resource.clone
    respond_with @activity, template: "#{view_path}/show", status: 201
  end

  def statistics
    render json: resource.statistics(mode: statistics_params[:mode]), status: 201
  end

  def export_statistics
    path = resource.export_xlsx(mode: statistics_params[:mode])
    send_file path
  end

  private
    def activity_params
      params.require(:activity).permit(
        :name, :start_at, :end_at, :body, :state, fee: {}, student_ids: []
      ).merge(
        school: current_auth.school,
        creator: current_auth,
      )
    end

    def statistics_params
      params.require(:activity).permit(
        :mode
      )
    end
end
