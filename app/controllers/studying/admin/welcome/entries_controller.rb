class Studying::Admin::Welcome::EntriesController < SimpleController::BaseController
  defaults(
    resource_class: Studying::Welcome::Entry,
    collection_name: 'entries',
    instance_name: 'entry',
    view_path: 'studying/welcome/entries',
    exportable_class: Studying::Welcome::Excel::EntryHandle,
  )

  auth_action :teacher
  permit_action :studying_admin

  belongs_to :studying_welcome_activity, param: :activity_id, shallow: true

  def append
    student_ids = parent.student_ids
    student_ids = (student_ids + append_entry_params[:student_ids]).uniq
    parent.update student_ids: student_ids
    head 201
  end

  protected
    def begin_of_association_chain
      current_auth.school
    end
    # def method_for_association_chain
    #   :studying_welcome_activities
    # end

  private
    def entry_params
      params.require(:entry).permit(
        :student_id
      )
    end

    def append_entry_params
      params.require(:entry).permit(
        student_ids: []
      )
    end
end
