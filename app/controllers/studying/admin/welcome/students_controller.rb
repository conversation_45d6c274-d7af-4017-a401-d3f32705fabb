class Studying::Admin::Welcome::StudentsController < SimpleController::BaseController
  defaults(
    resource_class: ::Student,
    collection_name: 'students',
    instance_name: 'student',
    view_path: 'students',
    importable_class: Studying::Welcome::Excel::Student,
  )
  auth_action :teacher
  permit_action :studying_admin

  belongs_to :activity, collection_name: 'studying_welcome_activities'

  def index
    index! do
      @statistics = end_of_association_chain.group(:catalog).count.merge(count: end_of_association_chain.count)
    end
  end

  protected

  def	begin_of_association_chain
    current_teacher.school
  end

  private

  def student_params
    params.require(:student).permit(
      :building_name, :floor, :room, :bed
    )
  end
end
