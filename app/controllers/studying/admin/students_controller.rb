class Studying::Admin::StudentsController < SimpleController::BaseController
  defaults(
    resource_class: ::Student,
    collection_name: 'students',
    instance_name: 'student',
    view_path: 'students',
    exportable_class: ::Student,
    importable_class: ::Student,
  )
  auth_action :teacher
  permit_action :studying_admin

  protected

  def begin_of_association_chain
    current_teacher.school
  end

  private

  def student_params
    params.require(:student).permit(
      :name, :code, :identity_id, :sex, :nation, :birth, :native_place, :home_address,
      :tel, :phone, :postcode, :tel2, :study_stat, :password_raw,
      column1_key_value: {}, column2_key_value: {}
    ).merge(
      school: current_teacher.school
    )
  end
end
