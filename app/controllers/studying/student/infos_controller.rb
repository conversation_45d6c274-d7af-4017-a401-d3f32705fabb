class Studying::Student::InfosController < SimpleController::BaseController
  defaults(
    resource_class: Student,
    collection_name: 'students',
    instance_name: 'student',
    view_path: 'students'
  )
  auth_action :student

  def resource
    @student = current_student
  end

  private
    def student_params
      params.require(:student).permit(
        :name, :code, :identity_id, :sex, :nation, :birth, :native_place, :home_address, :tel, :phone, :postcode, :tel2, :high_school, :specialty, :entrance_records, :reward_punish, :high_school_job, :height, :weight, resume: {}, family: {}, avatar: {}
      )
    end
end
