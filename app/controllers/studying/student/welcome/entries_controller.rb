class Studying::Student::Welcome::EntriesController < SimpleController::BaseController
  defaults(
    resource_class: Studying::Welcome::Entry,
    collection_name: 'entries',
    instance_name: 'entry',
    view_path: 'studying/welcome/entries',
    singleton: true,
  )

  auth_action :student

  def resource
    @entry = current_student.studying_welcome_entries.joins(:activity).where(studying_welcome_activities: { state: 'doing' }).first || raise(ActiveRecord::RecordNotFound.new('未找到相关的迎新活动'))
  end
end
