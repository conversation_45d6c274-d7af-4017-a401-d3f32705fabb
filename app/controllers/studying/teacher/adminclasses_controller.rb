class Studying::Teacher::<PERSON><PERSON><PERSON><PERSON><PERSON>Controll<PERSON> < SimpleController::BaseController
  defaults(
    resource_class: Adminclass,
    collection_name: 'adminclasses',
    instance_name: 'adminclass',
    view_path: 'adminclasses'
  )
  auth_action :teacher

  protected

  def begin_of_association_chain
    current_auth
  end

  def method_for_association_chain
    :associate_adminclasses
  end
end
