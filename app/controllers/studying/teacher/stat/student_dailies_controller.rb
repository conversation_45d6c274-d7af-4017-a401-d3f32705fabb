class Studying::Teacher::Stat::StudentDailiesController < SimpleController::BaseController
  defaults(
    resource_class: Stat::StudentDaily,
    collection_name: 'student_dailies',
    instance_name: 'student_daily',
    view_path: 'studying/student_dailies'
  )
  auth_action :teacher

  def summary
    render json: {
      coming_count: end_of_association_chain.ransack(params[:q]).result.sum(:coming_count),
      going_count: end_of_association_chain.ransack(params[:q]).result.sum(:going_count),
      register_count: end_of_association_chain.ransack(params[:q]).result.sum(:register_count),
    }
  end

  protected

  def begin_of_association_chain
    current_auth
  end
end
