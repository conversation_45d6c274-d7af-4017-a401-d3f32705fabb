class Studying::Api::InstancesController < SimpleController::BaseController
  defaults(
    resource_class: Instance,
    collection_name: 'instances',
    instance_name: 'instance',
    view_path: 'bpm/instances',
  )

  def create
    workflow = Workflow.find(params[:workflow_id])
    # 发起人，默认为学生或者公有的
    teacher = ::Teacher.find_by code: instance_params[:teacher_code]
    if instance_params[:student_code].present? && instance_params[:sfzh].present?
      student = ::Student.find_by(code: instance_params[:student_code]) ||
      ::Student.find_by(identity_id: instance_params[:sfzh]) || teacher
    else
      student = teacher
    end
    payload = instance_params[:payload].to_h
    @instance = workflow.instances.create!(
      type: 'Bpm::Instance',
      payload: convert_payload(payload.with_indifferent_access),
      creator: student,
    )
    @instance.tokens.first.fire!(action: 'submit')
    @instance.tokens.reload.last.update(state: 'processing', operator: teacher)
    @current_auth = student
    respond_with @instance, template: "#{view_path}/show", status: 201
  end

  def self.sms_by_student payload_value
    url = 'http://172.16.144.80:3067/sendmess'
    instance = payload_value[:instance]
    payload = instance.payload
    body = {
      cellphone: payload[:brsj],
      lqtzsh: payload[:lqtzsh],
      cgf: instance.storage[:approval_result] == '通过',
      jzf: false,
      sfzh: payload[:sfzh],
    }
    Typhoeus.post(url, body: body)
  end

  def self.sms_by_parent payload_value
    url = 'http://172.16.144.80:3067/sendmess'
    instance = payload_value[:instance]
    payload = instance.payload
    body = {
      cellphone: payload[:brsj],
      lqtzsh: payload[:lqtzsh],
      cgf: instance.storage[:approval_result] == '通过',
      sfzh: payload[:sfzh],
      jzf: true,
    }
    Typhoeus.post(url, body: body)
  end

  def self.sms_by_off_campus payload_value
    url = 'http://172.16.144.80:3071/sendmess'
    instance = payload_value[:instance]
    payload = instance.payload
    body = {
      cellphone: payload[:brsj],
      cgf: instance.storage[:approval_result] == '通过',
      lch: instance.seq,
      lxrq: payload[:lxrq],
      dxrq: payload[:dxrq],
      bfry: payload[:bfry],
      xm: payload[:xm],
    }
    Typhoeus.post(url, body: body)
  end

  def self.sms_by_student_newcome payload_value
    url = 'http://172.16.144.80:3088/sendmess'
    instance = payload_value[:instance]
    payload = instance.payload
    payload['cgf'] = instance.storage[:cgf]
    body = {
      cellphone: payload[:brsj],
      cgf: instance.storage[:cgf],
      lch: instance.seq,
      xm: payload[:xm],
    }
    instance.update! payload: payload
    Typhoeus.post(url, body: body)
  end

  def self.return_to_campus_before payload_value
    url = 'http://172.16.144.80:3133/bpm/before'
    instance = payload_value[:instance]
    creator = instance.creator
    body = payload_value.except(:creator).merge(
      seq: instance.seq,
      code: creator.code,
      name: creator.name,
    )
    Typhoeus.post(url, body: body)
  end

  def self.return_to_campus_after payload_value
    url = 'http://172.16.144.80:3133/bpm/after'
    instance = payload_value[:instance]
    creator = instance.creator
    body = payload_value.except(:creator).merge(
      seq: instance.seq,
      code: creator.code,
      name: creator.name,
    )
    Typhoeus.post(url, body: body)
  end

  private
    def instance_params
      params.require(:instance).permit(
        :student_code, :teacher_code, payload: {}
      )
    end

    def convert_payload payload
      payload[:jtgj] =
        case payload[:jtgj].to_i
        when 1
          '飞机'
        when 2
          '火车'
        when 3
          '汽车'
        when 4
          '其他'
        end
      payload[:frf] = payload[:frf].to_i == 1 ? '是' : '否'
      payload[:jcf] = payload[:jcf].to_i == 1 ? '是' : '否'
      payload[:jwf] = payload[:jwf].to_i == 1 ? '是' : '否'
      payload[:jcggf] = payload[:jcggf].to_i == 1 ? '是' : '否'
      payload[:smf] = '我已阅读并承诺以上内容'

      # url开头的参数，进行url的格式转换
      payload.keys.each do |key|
        if key.to_s.start_with?('url')
          payload[key] = payload[key].map do |item|
            item.present? ? convert_url(item) : nil
          end.compact
        end
      end
      payload
    end

    def convert_url url
      filename = File.basename url
      mime_type = MIME::Types.type_for(filename).first.content_type
      file_type = mime_type.split('/').last
      {url: url, fileName: filename, status: 'done', mimeType: mime_type, fileType: file_type}
    end
end
