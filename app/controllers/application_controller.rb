class ApplicationController < ActionController::API
  include AbstractController::Helpers
  include ActionController::Caching
  include AuthAction
  include PermitAction
  include SimpleController::Importable
  include SimpleController::Exportable

  helper_method :current_auth
  prepend_before_action :decode_iv_encrypted_parameters
  # prepend_before_action :decode_query_parameters
  before_action :limit_user_operation!, only: [:create, :destroy], if: :should_limit_user_operation
  impressionist actions: [:create, :update]

  def request_token
    request.env['HTTP_AUTHORIZATION'].to_s.sub(/^Token /, '')
  end

  def authenticated_auth
    if respond_to?(:current_auth) && current_auth
      current_auth
    else
      'Unknown'
    end
  end

  def should_limit_user_operation
    respond_to?(:current_auth) && current_auth
  end

  def limit_user_operation!
    key = [params[:controller], params[:action], params[:sequence], current_auth&.id] * '_'
    value = Redis::Value.new(key, expiration: 1)
    raise Error::BaseError.new(message: 'operation frequently', status: 430) if value.value.present?
    value.value = 1
  end

  def export_headers
    render json: { headers: exportable_class.export_instance.export_headers(**params.to_unsafe_h.symbolize_keys) }
  end

  rescue_from ActiveRecord::RecordNotFound  do |e|
    render json: { code: -1, message: e.message }, status: 404
  end

  rescue_from ActiveRecord::RecordInvalid do |e|
    render json: { code: -1, message: e.message }, status: 422
  end

  rescue_from Error::BaseError do |e|
    render json: { code: e.code, message: e.message, errors: e.errors }, status: e.status
  end

  def render(*args, &block)
    super

    return unless request.headers['HTTP_IV_ENCRYPT'] || request.headers['HTTP_IV_DECODE'] || ENV['FORCE_IV_ENCRYPT']
    return if request.headers['HTTP_DISABLE_IV_ENCRYPT'] && request.headers['HTTP_IV_DECODE'].blank? # 支持可以强制关闭
    return unless response.headers['Content-Type'].to_s.include?('application/json')

    cipher = OpenSSL::Cipher.new('AES-256-CBC')
    iv = cipher.random_iv
    iv64 = Base64.strict_encode64(iv)
    password = Digest::SHA256.digest(ENV.fetch('IV_KEY', 'tallty.com'))
    iv_encrypted = encrypt(password, iv, response.body)

    response.body = { iv_encrypted: iv_encrypted, iv64: iv64 }.to_json
    response.headers.merge!('iv-encrypt' => true)
  end

  def decode_iv_encrypted_parameters
    if request.headers['HTTP_IV_DECODE'].present?
      iv = Base64.decode64 request.headers['HTTP_IV_DECODE']
      password = Digest::SHA256.digest(ENV.fetch('IV_KEY', 'tallty.com'))
      iv_encrypted = params.delete(:iv_encrypted)
      params.merge!(
        JSON.parse(
          decrypt(password, iv, iv_encrypted),
        ),
      )
    end
  rescue Exception => e
    raise Error::EncryptError
  end

  def decode_query_parameters
    return unless request.headers['HTTP_RANSACK_DECODE']

    request.query_parameters.each do |key, value|
      params[key] = JSON.parse(value) if value.is_a?(String)
    rescue Exception => e
      next
    end
  end

  def check_iv_encrypted
    raise Error::EncryptError unless request.headers['HTTP_IV_DECODE'].present?
  end

  def disable_iv_encrypted(*args)
    request.headers['HTTP_DISABLE_IV_ENCRYPT'] = 1
  end

  def decrypt(password, iv, secretdata)
    secretdata = Base64.decode64(secretdata)
    decipher = OpenSSL::Cipher.new('AES-256-CBC')
    decipher.decrypt
    decipher.key = password
    decipher.iv = iv unless iv.nil?
    decipher.update(secretdata) + decipher.final
  end

  def encrypt(password, iv, cleardata)
    cipher = OpenSSL::Cipher.new('AES-256-CBC')
    cipher.encrypt  # set cipher to be encryption mode
    cipher.key = password
    cipher.iv  = iv unless iv.nil?

    encrypted = ''
    encrypted << cipher.update(cleardata)
    encrypted << cipher.final
    Base64.strict_encode64(encrypted)
  end
end
