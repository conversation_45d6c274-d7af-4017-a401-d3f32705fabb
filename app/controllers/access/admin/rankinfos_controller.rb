class Access::Admin::RankinfosController < SimpleController::BaseController
  defaults(
    resource_class: Access::Entry,
    collection_name: 'entries',
    instance_name: 'entry',
    view_path: 'access/rankinfos'
  )

  auth_action :teacher
  permit_action :access_admin
  belongs_to :access_activity, parent_class: Access::Activity, param: :activity_id, optional: true

  protected
    def after_association_chain association
      @access_activity.get_rank(params[:type], params[:id])
    end
end
