class Access::Admin::TeachersController < SimpleController::BaseController
  defaults(
    resource_class: Access::ScopeTeacher,
    collection_name: 'access_scope_teachers',
    instance_name: 'access_scope_teacher',
    view_path: 'access/scope_teachers'
  )

  auth_action :teacher
  permit_action :access_admin
  belongs_to :access_scope, parent_class: Access::Scope, param: :scope_id, optional: true

  private
    def access_scope_teacher_params
      params.require(:access_scope_teacher).permit(
        :teacher_id
      )
    end
end
