class Access::Admin::ScopesController < SimpleController::BaseController
  defaults(
    resource_class: Access::Scope,
    collection_name: 'access_scopes',
    instance_name: 'access_scope',
    view_path: 'access/scopes'
  )

  auth_action :teacher
  permit_action :access_admin
  belongs_to :access_activity, parent_class: Access::Activity, param: :activity_id, optional: true

  private
    def access_scope_params
      params.require(:access_scope).permit(
        :weight, :name, :position, :score_config, :type, attachments: {}, teacher_ids: []
      )
    end
end
