class Access::Admin::QuestionSetsController < SimpleController::BaseController
  defaults(
    resource_class: Access::QuestionSet,
    collection_name: 'access_question_sets',
    instance_name: 'access_question_set',
    view_path: 'access/question_sets'
  )

  auth_action :teacher
  permit_action :access_admin
  belongs_to :access_activity, parent_class: Access::Activity, param: :activity_id, optional: true

  private
    def access_question_set_params
      params.require(:access_question_set).permit(
        :title, :body, :state, :type, :start_at, :end_at, :position, meta: {}
      )
    end
end
