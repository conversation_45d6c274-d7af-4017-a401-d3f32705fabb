class Access::Admin::ScoresController < SimpleController::BaseController
  defaults(
    resource_class: Access::Score,
    collection_name: 'access_scores',
    instance_name: 'access_score',
    view_path: 'access/scores/admin'
  )

  auth_action :teacher
  permit_action :access_admin

  belongs_to :access_entry, parent_class: Access::Entry, param: :entry_id, optional: true

  protected
    def after_association_chain association
      @scopes = @access_entry.access_activity.access_scopes.pluck(:id, :name).to_h
      association
    end

  private
    def access_score_params
      params.require(:access_score).permit(
        :access_activity_id, :teacher_id, :user_id, :access_entry_id, :access_scope_id, :score, :meta, :deleted_at
      )
    end
end
