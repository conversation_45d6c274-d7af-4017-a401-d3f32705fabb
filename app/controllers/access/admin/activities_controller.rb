class Access::Admin::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Access::Activity,
    collection_name: 'access_activities',
    instance_name: 'access_activity',
    view_path: 'access/activities/admin'
  )

  auth_action :teacher
  permit_action :access_admin

  def export
    render json: { url: AccessActivityService.new(id: resource.id).export }
  end

  protected
    def begin_of_association_chain
      current_teacher
    end

    def method_for_association_chain
      params[:action] == 'create' ? :create_access_activities : :admin_access_activities
    end

  private
    def access_activity_params
      params.require(:access_activity).permit(
        :name, :body, :state, :start_at, :end_at, :type, meta: {}, attachments: {}, teacher_ids: []
      ).merge(school: current_teacher.school)
    end
end
