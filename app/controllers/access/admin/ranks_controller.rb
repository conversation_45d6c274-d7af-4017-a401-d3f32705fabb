class Access::Admin::RanksController < SimpleController::BaseController
  defaults(
    resource_class: Access::Entry,
    collection_name: 'entries',
    instance_name: 'entry',
    view_path: 'access/ranks'
  )

  auth_action :teacher
  permit_action :access_admin
  belongs_to :access_activity, parent_class: Access::Activity, param: :activity_id, optional: true

  protected
    def after_association_chain association
      @scopes, @qsts = {}, {}
      @access_activity.access_scopes.each{ |scope|
        @scopes[scope.id] = @access_activity.get_rank('scope', scope.id)
                                         .paginate(page: params[:page], per_page: params[:per_page])
      }
      @access_activity.access_question_sets.each{ |qst|
        @qsts[qst.id] = @access_activity.get_rank('qst', qst.id)
                            .paginate(page: params[:page], per_page: params[:per_page])
      }
      association
    end
end
