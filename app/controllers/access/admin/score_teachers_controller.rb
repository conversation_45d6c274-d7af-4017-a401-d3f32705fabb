class Access::Admin::ScoreTeachersController < SimpleController::BaseController
  defaults(
    resource_class: Access::ScopeTeacher,
    collection_name: 'teachers',
    instance_name: 'teacher',
    view_path: 'access/score_teachers'
  )

  auth_action :teacher
  permit_action :access_admin
  belongs_to :access_activity, parent_class: Access::Activity, param: :activity_id

  protected
    def method_for_association_chain
      'access_scope_teachers'
    end

    def after_association_chain association
      @scope = @access_activity.access_scopes.pluck(:id, :name).to_h
      association = @access_activity.queue_statistic_scope_teacher
    end
end
