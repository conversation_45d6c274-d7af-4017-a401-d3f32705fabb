class Access::Admin::QuestionsController < SimpleController::BaseController
  defaults(
    resource_class: Access::Question,
    collection_name: 'access_questions',
    instance_name: 'access_question',
    view_path: 'access/questions'
  )

  auth_action :teacher
  permit_action :access_admin
  belongs_to :access_question_set, parent_class: Access::QuestionSet, param: :question_set_id, optional: true

  protected
    def after_association_chain association
      association.system
    end

  private
    def access_question_params
      params.require(:access_question).permit(
        :title, :score, :position, meta: {}
      )
    end
end
