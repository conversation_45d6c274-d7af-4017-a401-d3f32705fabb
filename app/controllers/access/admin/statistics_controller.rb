class Access::Admin::StatisticsController < SimpleController::BaseController
  defaults(
    resource_class: Access::Entry,
    collection_name: 'entries',
    instance_name: 'entry',
    view_path: 'access/statistics'
  )

  auth_action :teacher
  permit_action :access_admin
  belongs_to :access_activity, parent_class: Access::Activity, param: :activity_id

  protected
    def after_association_chain association
      @activity = @access_activity.queue_statistic
      association
    end
end
