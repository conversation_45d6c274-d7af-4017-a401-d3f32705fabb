class Access::Admin::EntriesController < SimpleController::BaseController
  defaults(
    resource_class: Access::Entry,
    collection_name: 'access_entries',
    instance_name: 'access_entry',
    view_path: 'access/entries/admin'
  )

  auth_action :teacher
  permit_action :access_admin
  belongs_to :access_activity, parent_class: Access::Activity, param: :activity_id, optional: true

  def batch_update_scores
    teacher_ids = params[:teacher_ids] || []
    teacher_ids.each do |teacher_id|
      # resource.access_activity.access_scope_teachers.find_or_create_by(
      #   teacher_id: teacher_id,
      #   access_scope_id: params[:access_scope_id],
      # )

      resource.access_scores.find_or_create_by(
        teacher_id: teacher_id,
        access_scope_id: params[:access_scope_id],
        user_id: resource.teacher_id,
        access_activity_id: resource.access_activity_id,
      )
    end
    resource.reload.access_scores.where(access_scope_id: params[:access_scope_id]).where.not(teacher_id: teacher_ids).destroy_all
    respond_with @access_entry, template: "#{view_path}/show", status: 201
  end

  protected
    def end_of_association_chain
      Access::Activity.find(params[:activity_id]).get_statistic(query: params[:q])
    end

    def resource
      @access_entry = Access::Entry.find(params[:id])
      if params[:action] == 'show'
        @access_scores = @access_entry.get_detail_access_score
        @personal_access_scores = @access_entry.get_personal_score
        @access_entry = @access_entry.get_detail_score
      end
      @access_entry
    end

  private
    def access_entry_params
      params.require(:access_entry).permit(
        :teacher_id, :total_score, :state, :level
      )
    end
end
