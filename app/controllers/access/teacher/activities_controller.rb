class Access::Teacher::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Access::Activity,
    collection_name: 'access_activities',
    instance_name: 'access_activity',
    view_path: 'access/activities'
  )

  auth_action :teacher

  protected
    def begin_of_association_chain
      current_teacher
    end

    def after_association_chain association
      @entry_activity_ids = current_teacher.entry_access_activities.pluck(:id)
      @score_activity_ids = current_teacher.score_access_activities.pluck(:id)
      super
    end
end
