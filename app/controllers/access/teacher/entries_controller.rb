class Access::Teacher::En<PERSON><PERSON><PERSON>roller < SimpleController::BaseController
  defaults(
    resource_class: Access::Entry,
    collection_name: 'access_entries',
    instance_name: 'access_entry',
    view_path: 'access/entries',
  )

  auth_action :teacher

  protected
    def resource
      @access_entry = current_teacher.access_entries.find_by!(access_activity_id: params[:entry_activity_id])
    end

  private
    def access_entry_params
      params.require(:access_entry).permit(
        :state, entry_meta: {}
      )
    end
end
