class Access::Teacher::Questions<PERSON><PERSON>roller < SimpleController::BaseController
  defaults(
    resource_class: Access::Question,
    collection_name: 'access_questions',
    instance_name: 'access_question',
    view_path: 'access/questions'
  )

  auth_action :teacher
  belongs_to :access_question_set, parent_class: Access::QuestionSet, param: :question_set_id, optional: true

  private
    def access_question_params
      params.require(:access_question).permit(
        :title, :score, :position, meta: {}
      ).merge(teacher: current_teacher)
    end
end
