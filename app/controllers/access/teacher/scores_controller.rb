class Access::Teacher::ScoresController < SimpleController::BaseController
  defaults(
    resource_class: Access::Score,
    collection_name: 'access_scores',
    instance_name: 'access_score',
    view_path: 'access/scores'
  )

  auth_action :teacher

  def resource
    @access_score = current_teacher.score_access_entries.find_by!(id: params[:score_entry_id]).access_scores.find_or_create_by!(teacher: current_teacher)
  end

  private
    def access_score_params
      params.require(:access_score).permit(
        :score, score_meta: {}
      ).merge(state: 'done')
    end
end
