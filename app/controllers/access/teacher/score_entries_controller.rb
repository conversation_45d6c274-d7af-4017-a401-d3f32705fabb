class Access::Teacher::ScoreEntriesController < SimpleController::BaseController
  defaults(
    resource_class: Access::Entry,
    collection_name: 'access_entries',
    instance_name: 'access_entry',
    view_path: 'access/entries/score'
  )

  auth_action :teacher

  protected
    def method_for_association_chain
      'score_access_entries'
    end

    def begin_of_association_chain
      current_teacher
    end

    def after_association_chain association
      activity, teacher_id = Access::Activity.find(params[:score_activity_id]), current_teacher.id
      # @scope = activity.access_scope_teachers.find_by(teacher_id: teacher_id)&.access_scope
      @scope = activity.access_scores.find_by(teacher: current_teacher)&.access_scope
      @score_count = activity.access_scores.where(teacher_id: teacher_id).count
      association.search_by_score_type(current_teacher.id, params[:score_activity_id], params[:type])
    end

    def resource
      super
      @score = @access_entry.access_scores.find_by(teacher_id: current_teacher.id)
      ### 下一个版本删除 基本数据应当在列表页返回 ###
      activity, teacher_id = @access_entry.access_activity, current_teacher.id
      # @score_config = activity.access_scope_teachers.find_by(teacher_id: teacher_id)&.access_scope&.score_config
      @score_config = @score&.access_scope&.score_config
      # @total_entry_count = activity.access_entries.where.not(teacher_id: teacher_id).count
      # @score_count = activity.access_scores.where(teacher_id: teacher_id).count

      @total_entry_count = activity.access_scores.where(teacher_id: teacher_id).count
      @score_count = activity.access_scores.where(teacher_id: teacher_id).done.count
      ### ################################ ###
    end
end
