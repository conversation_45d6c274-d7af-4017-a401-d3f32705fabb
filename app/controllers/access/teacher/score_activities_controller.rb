class Access::Teacher::ScoreActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Access::Activity,
    collection_name: 'access_activities',
    instance_name: 'access_activity',
    view_path: 'access/activities'
  )

  auth_action :teacher

  protected
    def begin_of_association_chain
      current_teacher
    end

    def method_for_association_chain
      'score_access_activities'
    end

end
