module PermitAction
  extend ActiveSupport::Concern

  included do
    private :permit_perform
  end

  def permit_perform
    # 如果是admin，直接通过权限校验
    if current_auth.class.name == 'Admin'
      return true
    end

    unless current_auth&.respond_to?(:has_any_role?) && current_auth&.has_any_role?(*self.class.permit_roles)
      raise Error::PermitError.new
    end
  end

  def permit_perform_with_creator
    # 如果是admin，直接通过权限校验
    if current_auth.class.name == 'Admin'
      return true
    end

    has_role = current_auth&.respond_to?(:has_any_role?) && current_auth&.has_any_role?(*self.class.permit_roles)
    is_creator = (resource&.try(:creator) == current_auth rescue false)
    unless has_role || is_creator
      raise Error::PermitError.new
    end
  end

  module ClassMethods
    def permit_action *permit_roles, only: nil
      if only
        before_action :permit_perform, only: only
      else
        before_action :permit_perform
      end
      @permit_roles = permit_roles
    end

    def permit_action_with_creator *permit_roles, only: nil
      if only
        before_action :permit_perform_with_creator, only: only
      else
        before_action :permit_perform_with_creator
      end
      @permit_roles = permit_roles
    end

    def permit_roles
      @permit_roles || []
    end
  end
end
