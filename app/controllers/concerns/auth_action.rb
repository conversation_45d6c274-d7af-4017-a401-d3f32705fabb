module AuthAction
  extend ActiveSupport::Concern

  included do
    private :auth_perform
  end

  def auth_perform
    klasses = self.class.action_classes
    klasses.each do |klass|
      @user_info = klass.auth!(request).with_indifferent_access rescue next
      break
    end
    raise Error::AuthError.new if @user_info.nil?
  end

  def load_auth user_info
    type = user_info['type']
    code = user_info['code']
    case type
    when 'Teacher'
      Teacher.find_by(code: code) ||
        Stiei::Teacher.sync_from_stiei(code: code, user: user_info)
    when 'Student'
      Student.find_by(code: code) ||
        StieiTeaching::Student.sync_from_stiei(code: code)
    when 'Expert'
      Expert.find_by(code: code)
    when 'Admin'
      Admin.find_by(app_id: code)
    end
  end

  module ClassMethods
    def auth_action klass_sym, options={}
      prepend_before_action :auth_perform, options
      define_method("current_auth") {
        @current_auth ||= load_auth(@user_info)
      }

      define_method("current_user") {
        @current_auth ||= load_auth(@user_info)
        @current_user = @current_auth
      }

      @klasses = (Array(klass_sym) << :admin).map do |sym|
        define_method("current_#{sym}") {
          current_auth
          instance_variable_set("@current_#{sym}", @current_auth)
        }
        sym.to_s.camelize.constantize
      end
    end

    def action_classes
      @klasses || superclass.action_classes
    end

  end
end
