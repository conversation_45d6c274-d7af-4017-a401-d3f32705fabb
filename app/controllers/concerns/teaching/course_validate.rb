module Teaching::CourseValidate
  extend ActiveSupport::Concern

  included do
    private :validate_course
  end

  def validate_course
    @teach_permit = nil
    @course ||= Teaching::Course.find(params[:course_id])
    if current_auth.class.to_s == 'Teacher' &&
      current_auth.courses.include?(@course)
      @teach_permit = 'teaching'
    elsif current_auth.respond_to?(:has_role?) &&
        current_auth.has_role?('teaching_admin') &&
        current_auth.school.courses.include?(@course)
      @teach_permit = 'admin'
    elsif current_auth.class.to_s == 'Student' &&
      current_auth.courses.include?(@course)
      @teach_permit = 'studying'
    elsif current_auth.respond_to?(:has_role?) &&
        current_auth.has_role?('teaching_supervistion') &&
        current_auth.school.courses.include?(@course)
      @teach_permit = 'supervision'
    elsif current_auth.class.to_s == 'Teacher' &&
      current_auth.inspect_courses.include?(@course)
      @teach_permit = 'inspect'
    end

    unless @teach_permit.present?
      raise Error::CoursePermitError.new
    end
  end

  module ClassMethods
  end
end
