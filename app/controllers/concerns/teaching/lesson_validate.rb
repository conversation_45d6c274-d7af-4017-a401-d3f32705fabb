module Teaching::LessonValidate
  extend ActiveSupport::Concern

  included do
    private :validate_lesson
  end

  def validate_lesson
    @teach_permit = nil
    @lesson ||= Teaching::Lesson.find(params[:lesson_id])
    if current_teacher.lessons.include?(@lesson)
      @teach_permit = 'teaching'
    elsif current_teacher.has_role?('teaching_admin') && current_teacher.school.lessons.include?(@lesson)
      @teach_permit = 'admin'
    elsif current_teacher.lessons.include?(@lesson)
      @teach_permit = 'teaching'
    elsif current_teacher.has_role?('teaching_operate') && current_teacher.school.lessons.include?(@lesson)
      @teach_permit = 'supervision'
    elsif current_teacher.inspect_lessons.include?(@lesson)
      @teach_permit = 'inspect'
    end

    unless @teach_permit.present?
      raise Error::LessonPermitError.new
    end
  end

  module ClassMethods
  end
end
