module Teaching::SemesterSelect
  extend ActiveSupport::Concern

  included do
    protected :after_association_chain
  end

  def after_association_chain association
    semester_id = params[:semester_id]
    if semester_id == 'all'
      association
    elsif semester_id.present?
      association.where(semester_id: semester_id)
    else
      association.where(semester: current_auth.school.current_semester)
    end
  end

  module ClassMethods
  end
end
