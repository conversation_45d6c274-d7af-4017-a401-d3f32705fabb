class Bpm::User::WorkflowsController < SimpleController::BaseController
  defaults(
    resource_class: Bpm::Workflow,
    collection_name: 'workflows',
    instance_name: 'workflow',
    view_path: 'workflows'
  )

  auth_action [:teacher, :student, :expert]

  protected
    def begin_of_association_chain
      params[:action] == 'show' ? current_auth.school : current_auth
    end

    def after_association_chain association
      params[:action] == 'show' ? association : association.where(state: :done)
    end
end
