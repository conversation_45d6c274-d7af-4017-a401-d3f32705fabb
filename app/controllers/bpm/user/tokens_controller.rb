class Bpm::User::TokensController < SimpleController::BaseController
  defaults(
    resource_class: Token,
    collection_name: 'tokens',
    instance_name: 'token',
    view_path: 'tokens'
  )

  auth_action [:teacher, :student, :expert]
  before_action :limit_user_operation!, only: [:fire, :assign], if: :current_auth

  belongs_to :bpm_instance, parent_class: Bpm::Instance, param: :instance_id, optional: true
  impressionist actions: [:fire, :assign, :forward]

  def fire
    @token = resource
    raise Error::BaseError.new(message: '不能操作该流程', status: 422) unless current_auth.bpm_instances.find_by(id: @token.instance_id)
    mode = fire_token_params[:action].to_sym
    if mode.to_s.in?(['accept'])
      raise Error::TokenError.new unless current_auth == @token.operator
    end
    @token.transaction do
      @token.update! comment_token_params if comment_token_params.present?
      @token.update! token_params if token_params.present?
      @token.fire! action: mode, params: fire_token_params
    end
    @instance = @token.instance.reload
    respond_with @instance, template: 'bpm/instances/show', status: 201
  end

  def assign
    @token = resource
    raise Error::BaseError.new(message: '不能操作该流程', status: 422) unless current_auth.bpm_instances.find_by(id: @token.instance_id)
    ::Token.transaction do
      @token.update! token_params if token_params.present?
      @token.fire! action: :assign, params: assign_token_params
    end
    @instance = @token.instance.reload
    respond_with @instance, template: 'bpm/instances/show', status: 201
  end

  def forward
    raise Error::BaseError.new(message: '不能操作该流程', status: 422) unless current_auth.bpm_instances.find_by(id: @token.instance_id)
    @token = resource
    ::Token.transaction do
      @token.update! token_params if token_params.present?
      @token.fire! action: :forward, params: assign_token_params
    end
    @instance = @token.instance.reload
    respond_with @instance, template: 'bpm/instances/show', status: 201
  end

  def show
    if !current_auth.has_role?(:bpm_admin) && !current_auth.has_role?(:bpm_view) && !ENV['PUBLISHED_WORKFLOW_ID'].to_s.split(',').include?(resource.instance&.workflow_id.to_s)
      raise Error::BaseError.new(message: '不能操作该流程', status: 422) unless current_auth.bpm_instances.find_by(id: resource.instance_id)
    end
    super
  end

  private
    def fire_token_params
      params.require(:token).permit(
        :action, :next_place_id, :operator_type, :operator_id
      )
    end

    def assign_token_params
      params.require(:token).permit(
        :operator_type, :operator_id
      )
    end

    def comment_token_params
      params.require(:token).permit(
        :comment
      )
    end

    def token_params
      params.require(:token).permit(
        options: {}, token_payload: {}
      )
    end
end
