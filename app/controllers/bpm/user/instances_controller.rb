class Bpm::User::InstancesController < SimpleController::BaseController
  defaults(
    resource_class: Bpm::Instance,
    collection_name: 'instances',
    instance_name: 'instance',
    view_path: 'bpm/instances'
  )

  auth_action [:teacher, :student, :expert]
  before_action :current_auth
  before_action :check_params!, only: [:index]

  belongs_to :workflow, collection_name: :bpm_workflows, parent_class: Bpm::Workflow, optional: true

  # def export
  #   url = Bpm::Instance.export_xlsx collection
  #   render json: { url: url }
  # end
  def statistic
    render json: {}
    # association = params[:type].present? ? Instance.where(type: params[:type]) : Instance.all
    # association = params[:workflow_id].present? ? association.where(workflow_id: params[:workflow_id]) : association
    # association = association.where.not(state: 'completed')
    # types = association.group(:type).count.keys
    # s_result = types.reduce({}) do |hash, type|
    #   hash[type] = {
    #     type: type,
    #     todo_count: association.where(type: type).todo(current_auth.id, current_auth.class.to_s).count,
    #     approving_count: association.where(type: type).approving(current_auth.id, current_auth.class.to_s).count,
    #   }
    #   hash
    # end
    # render json: s_result
  end

  def three_level_stat
    association = after_association_chain(origin_end_of_association_chain.where.not(state: 'completed')).unscope(:order)

    result_association = association.ransack(params[:q]&.except(:workflow_modul_eq, :workflow_modul_null)).result
    workflow_modul_association = result_association.unscope(where: :workflow_id).joins(:workflow)
    workflow_id_association = association.ransack(params[:q]&.except(:workflow_id_eq)).result.joins(:workflow)
    workflow_id_count = workflow_id_association.group(:workflow_id).count

    instance_stat_association  = association.ransack(
      params[:q]&.except(
        :todo,
        :approving,
        :created,
        :approved,
        :notified,
        :workflow_modul_eq,
        :workflow_modul_null,
      )
    ).result.unscope(where: :workflow_id)

    render json: {
      instance_stat_count: {
        todo: instance_stat_association.ransack(todo: [current_user.id, current_user.class.base_class.to_s]).result.count,
        approving: instance_stat_association.ransack(approving: [current_user.id, current_user.class.base_class.to_s]).result.count,
        created: instance_stat_association.ransack(created: [current_user.id, current_user.class.base_class.to_s]).result.count,
        # approved: instance_stat_association.ransack(approved: [current_user.id, current_user.class.base_class.to_s]).result.count,
        notified: instance_stat_association.ransack(notified: [current_user.id, current_user.class.base_class.to_s]).result.count,
      },
      workflow_modul_count: workflow_modul_association.group('workflows.modul').count.merge(count: workflow_modul_association.count),
      workflow_id_count: workflow_id_count,
      workflow_id_name: Workflow.where(id: workflow_id_count.keys).pluck(:id, :name).to_h,
    }
  end

  protected
    def begin_of_associaition_chain
      current_auth.has_role?(:bpm_admin) ? current_auth.school : current_auth
    end

    def method_for_association_chain
      current_auth.has_role?(:bpm_admin) ? :instances : :bpm_instances
    end

    def	after_association_chain association
      association
    end

    # def resource
    #   @instance = current_auth.has_role?(:bpm_admin) ? super : current_auth.bpm_instances.find_by(id: params[:id])
    #   raise Error::BaseError.new(message: '流程节点已被审批或无法查看该流程', status: 422) unless @instance
    #   @instance
    # end

    def exportable_class
      params[:finance] ? Finance::VoucherInstance : Bpm::Instance
    end

    def check_params!
      unless current_auth.has_role?(:bpm_admin)
        if params[:q].present?
          q = params[:q].to_unsafe_h
          operator = q[:todo] || q[:approving] || q[:created] || q[:notified] || q[:approved]
          operator_type = current_auth.class.base_class.name
          result = [current_auth.id, operator_type] == operator || [current_auth.id.to_s, operator_type] == operator
          raise Error::BaseError.new(message: '参数错误', status: 422) unless result
        end
      end
    end

  private
    def create_instance_params
      params.require(:instance).permit(
        :flowable_type, :flowable_id, payload: {}, meta: {}
      ).merge(creator: current_auth, type: @workflow.instance_type)
    end

    def update_instance_params
      params.require(:instance).permit(
        payload: {}, meta: {}
      )
    end
end
