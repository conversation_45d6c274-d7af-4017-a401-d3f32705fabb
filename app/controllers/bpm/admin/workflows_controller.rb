class Bpm::Admin::WorkflowsController < SimpleController::BaseController
  defaults(
    resource_class: Bpm::Workflow,
    collection_name: 'workflows',
    instance_name: 'workflow',
    view_path: 'workflows'
  )

  auth_action :teacher
  permit_action_with_creator :bpm_admin

  def clone
    @workflow = resource.clone
    respond_with @workflow, template: "#{view_path}/show", status: 201
  end

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def workflow_params
      images_params = {}
      image = params.require(:workflow).fetch(:image, [])&.map(&:permit!)
      images_params.merge!(image: image) if params.require(:workflow)[:image]

      params.require(:workflow).permit(
        :permit_type, :type, :name, :desc, :state, :catalog, :modul, :print_component, core: {}, form: {}, map_keys: {}, permit_options: {}, paste_teacher_ids: [], paste_student_ids: [], cm_model_setting_attributes: [ :id, :model_define_id, :flag, :setable_type, :setable_id ]
      ).merge(
        creator: current_auth,
      ).merge(images_params)
    end
end
