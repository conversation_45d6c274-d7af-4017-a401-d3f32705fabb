class Bpm::Admin::InstancesController < SimpleController::BaseController
  defaults(
    resource_class: Bpm::Instance,
    collection_name: 'instances',
    instance_name: 'instance',
    view_path: 'bpm/instances'
  )

  auth_action :teacher
  permit_action :bpm_admin

  belongs_to :workflow, collection_name: :bpm_workflows, parent_class: Bpm::Workflow, optional: true

  protected
    def begin_of_associaition_chain
      current_teacher.school
    end
end
