class Bpm::Flowable::InstancesController < SimpleController::BaseController
  defaults(
    resource_class: Bpm::Instance,
    collection_name: 'instances',
    instance_name: 'instance',
    view_path: 'bpm/instances'
  )
  auth_action [:student, :teacher]

  def create
    flag = create_instance_params[:flag]
    generate_instance_method = ['generate', flag.to_s, 'instance', 'by_user'].reject(&:blank?).join('_')
    @instance = begin_of_association_chain.send(generate_instance_method, current_auth)
    @instance.update! payload: create_instance_params[:payload]
    respond_with @instance, template: "#{view_path}/show", status: 201
  end

  protected
    def begin_of_association_chain
      params[:flowable_type].present? && params[:flowable_id] ?
        Cm::ModelDefine.class_by_singular_name(params[:flowable_type]).find(params[:flowable_id]) :
        current_auth
    end

    def method_for_association_chain
      :flowable_instances
    end

  private
    def create_instance_params
      params.require(:instance).permit(
        :flag, payload: {}
      ).merge(creator: current_auth)
    end
end
