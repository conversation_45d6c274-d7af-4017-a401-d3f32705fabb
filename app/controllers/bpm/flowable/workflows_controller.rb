class Bpm::Flowable::WorkflowsController < SimpleController::BaseController
  defaults(
    resource_class: Bpm::Workflow,
    collection_name: 'workflows',
    instance_name: 'workflow',
    view_path: 'workflows'
  )
  auth_action [:student, :teacher]

  protected
    def begin_of_association_chain
      params[:flowable_type].present? && params[:flowable_id] ?
        Cm::ModelDefine.class_by_singular_name(params[:flowable_type]).find(params[:flowable_id]) :
        current_auth
    end

end
