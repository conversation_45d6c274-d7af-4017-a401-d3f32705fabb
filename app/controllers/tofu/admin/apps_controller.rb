class Tofu::Admin::AppsController < SimpleController::BaseController
  defaults(
    resource_class: Tofu::App,
    collection_name: 'apps',
    instance_name: 'app',
    view_path: 'tofu/apps'
  )

  auth_action :teacher
  permit_action :inform_admin

  private

  def after_association_chain association
    association.by_position
  end

  def app_params
    params.require(:app).permit(
      :name, :url, :mobile_url, :mobilable, :icon, :image, :type, :mod,
      :position, :super_tofu_id, :workflow_modul, instance_types: [], auths: [],
    )
  end
end
