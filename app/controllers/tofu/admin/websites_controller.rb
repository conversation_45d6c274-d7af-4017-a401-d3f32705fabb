class Tofu::Admin::WebsitesController < SimpleController::BaseController
  defaults(
    resource_class: Tofu::Website,
    collection_name: 'websites',
    instance_name: 'website',
    view_path: 'tofu/websites'
  )

  auth_action :teacher
  permit_action :inform_admin

  private

  def website_params
    params.require(:website).permit(
      :name, :url, :mobile_url, :icon, :image, :position,
    )
  end
end
