class Tofu::User::CustomsController < SimpleController::BaseController
  defaults(
    resource_class: Tofu::Custom,
    collection_name: 'customs',
    instance_name: 'custom',
    view_path: 'tofu/customs'
  )

  auth_action [:teacher, :student]

  private

  def end_of_association_chain
    current_auth.custom_tofu_objs.by_position
  end

  def custom_params
    params.require(:custom).permit(
      :name, :url, :icon, :image, :position
    )
  end
end
