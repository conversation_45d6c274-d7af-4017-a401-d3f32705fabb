class Tofu::User::StarsController < SimpleController::BaseController
  defaults(
    resource_class: Tofu::Star,
    collection_name: 'stars',
    instance_name: 'star',
    view_path: 'tofu/stars'
  )

  auth_action [:teacher, :student]

  def index
    render json: { stars: current_auth.liked_tofus }
  end

  def show
    tofu = current_auth.liked_tofu_objs.find(params[:id])
    top_tofu = tofu.super_tofu_id ? tofu.top_tofu : nil

    render json: {
      id: tofu.id,
      name: tofu.name,
      url: tofu.url,
      image: tofu.image,
      top_tofu_name: top_tofu && top_tofu.name,
      top_tofu_image: top_tofu && top_tofu.image,
      instance_count: tofu.try(:instance_count, current_auth)
    }
  end

  def batch_update
    current_auth.liked_tofu_ids = params[:tofu_ids]
    head 204
  end

  def update
    # 路由的 :id 传 tofu.id
    Tofu::Star.find_by!(auth: current_auth, tofu_id: params[:id]).update!(
      position: star_params[:position],
    )
    head 204
  end

  def create
    Tofu::Star.create!(auth: current_auth, tofu_id: star_params[:tofu_id])
    head 201
  end

  def destroy
    # 路由的 :id 传 tofu.id
    Tofu::Star.where(auth: current_auth, tofu_id: params[:id]).destroy_all
    head 204
  end

  private

  def star_params
    params.require(:star).permit(
      :tofu_id, :position
    )
  end
end
