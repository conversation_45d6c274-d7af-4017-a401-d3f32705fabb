class Data::Api::InstancesController < SimpleController::BaseController
  def execute
    user = Teacher.find_by(code: params[:code]) || Student.find_by(code: params[:code])
    return render json: { code: 0, message: '用户不存在' } unless user
    @instances = School.first.instances.ransack(approving: [user.id, user.class.name])
                  .result
                  .paginate(page: params[:page], per_page: params[:per_page])
    respond_with @instances, template: "bpm/instances/index", status: 201
  end

  def show
    user = Teacher.find_by(code: params[:code]) || Student.find_by(code: params[:code])
    return render json: { code: 0, message: '用户不存在' } unless user

    @instance = user.bpm_instances.find_by(id: params[:id])
    return render json: { code: 0, message: '用户没有该流程' } unless @instance
    respond_with @instance, template: "bpm/instances/show"
  end
end