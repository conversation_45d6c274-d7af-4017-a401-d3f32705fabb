class Data::Api::ComingAndGoingsController < SimpleController::BaseController
  defaults(
    resource_class: Stat::ComingAndGoing,
    collection_name: 'coming_and_goings',
    instance_name: 'coming_and_going',
    view_path: 'stat/coming_and_goings'
  )

  def stat
    render json: Stat::ComingAndGoing.stat(end_of_association_chain.ransack(params[:q]).result)
  end

  def line_chart
    start_at, end_at = Time.zone.now - 24.hours, Time.zone.now
    relation = end_of_association_chain.where('created_at BETWEEN ? AND ?', start_at, end_at).ransack(params[:q]).result

    coming_info = []
    going_info = []

    24.times do |i|
      a, b = start_at + i.hours, start_at + (i + 1).hours
      coming_count = relation.where('created_at BETWEEN ? AND ?', a, b).sum(:coming_count)
      going_count = relation.where('created_at BETWEEN ? AND ?', a, b).sum(:going_count)
      coming_info.push({ time: a.strftime('%H:%M'), count: coming_count })
      going_info.push({ time: a.strftime('%H:%M'), count: going_count })
    end

    render json: {
      coming: coming_info,
      going: going_info,
    }
  end
end
