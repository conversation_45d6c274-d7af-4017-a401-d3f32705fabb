class Data::Api::DivisionsController < SimpleController::BaseController
  defaults(
    resource_class: Department::Division,
    collection_name: 'divisions',
    instance_name: 'division',
    view_path: 'data/divisions'
  )

  belongs_to :school

  def statistic
    relation = end_of_association_chain.ransack(params[:q]).result
    render json: {
      department_count: relation.count,
      teacher_count: relation.map { |d| d.teachers.count }.sum,
    }
  end


  private

  def end_of_association_chain
    parent.departments.where(type: 'Department::Division', depth: 1)
  end
end
