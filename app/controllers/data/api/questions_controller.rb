class Data::Api::EpRegistersController < SimpleController::BaseController
  defaults(
    resource_class: Register::Ep,
    collection_name: 'registers',
    instance_name: 'register',
    view_path: 'data/ep_registers'
  )

  protected

  def begin_of_association_chain
    School.first
  end

  def method_for_association_chain
    :ep_registers
  end

  def after_association_chain association
    association.where(state: 'done')
  end
end
