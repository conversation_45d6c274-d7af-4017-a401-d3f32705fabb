class Data::Api::EntriesController < ApplicationController
  def create
    student = Student.find_by(code: params[:code]) || Student.find_by(code1: params[:code])
    entry = Studying::Welcome::Entry.find_by(student: student)
    if student && entry
      return render json: { code: 0, message: '迎新状态不正确' } unless entry.state.to_s.in?(['线上报到成功', '线上报到中'])
      token = entry.instance.tokens.filter { |token| token.name =~ /人证|人脸/ }&.first
      return render json: { code: 0, message: '未到该流程' } unless token
      token.fire! action: 'accept'
      # 生成新的学生
      if params[:uid].present?
        s = Student.find_or_initialize_by(
          school: student.school,
          name: student.name,
          identity_id: student.identity_id,
          code: params[:uid],
        )

        password_raw = ['Xs@', student.identity_id.to_s.last(6).upcase] * ''
        s.update(password_raw: password_raw) unless s.persisted?
      end

      Wechat::TemplateMessage.find_or_create_by(
        school: student.school,
        app_id: 'wxb1daea392d68c3f5',
        receiver: student,
        notifyable: entry,
        message: {
          template_id: 'YMFWHgdaTIr8grKr3KyMZVzP4FQPHEuiq57OkGeIOVo',
          url: "#{ENV['MOBILE_URL']}/studying/welcome",
          topcolor: '#FF0000',
          data: {
            keyword1: {
              value: '您已经完成了线下人证对比',
            },
            keyword2: {
              value: '请与老师确认',
            },
            keyword3: {
              value: student.name,
            },
            keyword4: {
              value: Time.zone.now.strftime('%Y-%m-%d %H:%M'),
            },
          }
        }
      )
      return render json: { code: 1, message: 'ok' }
    else
      return render json: { code: 0, message: '学生不存在或者不在迎新中' }
    end
  end
end
