class Data::Api::FacesController < ApplicationController
  def create
    person_id = params['personId']
    api = HikApiService.new
    response = api.post('/api/cflms/v2/person/personList/search', { personIds: person_id, pageNo: 1, pageSize: 100 })
    infos = JSON.parse response.body
    user_info = infos['data']['list'].first
    identity_id = user_info['certificateNo']
    code = user_info['jobNo']
    name = user_info['name']

    FaceApiRecord.create!(
      person_id: person_id,
      identity_id: identity_id,
      code: code,
      name: name,
      meta: params,
    )

    # 查找到对应迎新活动的entry
    # 找到对应instance，找到人脸对应节点
    # 直接trigger
    student = Student.find_by(code: code)
    entry = Studying::Welcome::Entry.find_by(
      state: ['线上报到成功', '线上报到中'],
      student: student
    )
    if entry.present?
      token = entry.instance.tokens.filter { |token| token.name =~ /人证|人脸/ }&.first
      if token&.state == 'processing'
        token.fire! action: 'accept'

        Wechat::TemplateMessage.find_or_create_by(
          school: student.school,
          app_id: 'wxb1daea392d68c3f5',
          receiver: student,
          notifyable: entry,
        ) do |notification|
          notification.message = {
            template_id: 'YMFWHgdaTIr8grKr3KyMZVzP4FQPHEuiq57OkGeIOVo',
            url: "#{ENV['MOBILE_URL']}/studying/welcome",
            topcolor: '#FF0000',
            data: {
              first: {
                color: '#0033FF',
                value: "你已经完成了线下人证对比",
              },
              keyword1: {
                value: '你已经完成了线下人证对比',
              },
              keyword2: {
                value: "你的宿舍是9-1091",
              },
              keyword3: {
                value: "请与老师确认",
              },
              keyword4: {
                value: Time.zone.now.strftime('%Y-%m-%d %H:%M'),
              },
              remark: {
                color: '#0ABB0A',
                value: "请于老师确认",
              },
            }
          }
        end
      end
    end


    head 201
  end
end
