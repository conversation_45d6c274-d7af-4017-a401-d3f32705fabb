class Data::Api::CourseDirsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::CourseDir,
    collection_name: 'course_dirs',
    instance_name: 'course_dir',
    view_path: 'teaching/course_dirs'
  )

  belongs_to :school

  def end_of_association_chain
    if (semester_code = params[:semester]).present?
      Teaching::CourseDir.where(id: parent.semesters.find_by!(code: semester_code).course_sets.joins(:course_dir).select(:course_dir_id))
    else
      parent.course_dirs
    end
  end
end
