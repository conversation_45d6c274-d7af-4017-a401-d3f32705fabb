class Data::Api::EpActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Ep::Activity,
    collection_name: 'activities',
    instance_name: 'activity',
    view_path: 'ep/statistics'
  )

  def index
    @activity = Ep::Activity.starting.last
    date = params['date'].present? ? params[:date] : Date.today.strftime('%F')
    @info = EpActivityStatisticService.new(activities: Ep::Activity.starting, date: date).perform
    super
  end
end
