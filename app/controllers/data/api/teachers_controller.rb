class Data::Api::TeachersController < SimpleController::BaseController
  defaults(
    resource_class: Teacher,
    collection_name: 'teachers',
    instance_name: 'teacher',
    view_path: 'teachers'
  )

  def department
    t = Teacher.find_by code: params[:code]
    return render json: {} unless t
    department = t.departments_by_level(2).first
    render json: {
      department_name: department&.name,
      department_id: department&.id,
      department_code: department&.code,
      level: t.level || '普通'
    }
  end

  private
    def teacher_params
      params.require(:teacher).permit(
        :name, :name_pinyin, :identity_id, :identity_type, :code, :birthday, :sex, :phone, :tel, :email, :meta, :dynamic_attrs, :deleted_at, :school_id, :department_id, :college_id, :college_code, :major_id, :major_code, :to_school_at, :leave_school_at, :work_way, :degree, :education, :state, :office, :address, :type, :category, :politics, :place, :wage
      )
    end
end
