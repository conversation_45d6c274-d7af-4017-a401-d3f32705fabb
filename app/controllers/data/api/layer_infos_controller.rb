class Data::Api::LayerInfosController < ApplicationController
  def create
    h = layer_info_params.select { |k, v| v.present? }.to_h.symbolize_keys
    info = BusinessPoint.layer_info **h
    render json: info, status: 201
  end

  private

  def layer_info_params
    params.permit(
      :layer, :key, :key_layer, :date, :schedule, :student, :colledge, :course, :teacher
    )
  end

  def current_auth
    nil
  end
end
