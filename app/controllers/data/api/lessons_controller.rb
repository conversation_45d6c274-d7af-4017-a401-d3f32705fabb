class Data::Api::LessonsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::Lesson,
    collection_name: 'lessons',
    instance_name: 'lesson',
    view_path: 'teaching/lessons/admin'
  )

  belongs_to :school

  def ana_info
    render json: Teaching::LessonAnalyst.new(lessons: collection).ana_info
  end

  protected

  def after_association_chain association
    semester_id = params[:semester_id]
    if semester_id == 'all'
      association
    elsif semester_id.present?
      association.where(semester_id: semester_id)
    else
      association.where(semester: parent.current_semester)
    end
  end
end
