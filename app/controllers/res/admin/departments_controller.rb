class Res::Admin::DepartmentsController < SimpleController::BaseController
  defaults(
    resource_class: Department,
    collection_name: 'departments',
    instance_name: 'department',
    view_path: 'departments'
  )

  auth_action :teacher
  permit_action :res_admin

  private
    def department_params
      params.require(:department).permit(
        :name, :code, :short_name, :type, :parent_id, meta: {}, direct_teacher_ids: []
      ).merge(
        school_id: current_teacher.school_id
      )
    end
end
