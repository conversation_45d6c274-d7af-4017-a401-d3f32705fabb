class Res::Admin::TitleCategoriesController < SimpleController::BaseController
  defaults(
    resource_class: TitleCategory,
    collection_name: 'title_categories',
    instance_name: 'title_category',
    view_path: 'title_categories'
  )

  auth_action :teacher
  permit_action :res_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def title_category_params
      params.require(:title_category).permit(
        :name, meta: {},
        titles_attributes: [:id, :level, :name, :_destroy, :position]
      )
    end
end
