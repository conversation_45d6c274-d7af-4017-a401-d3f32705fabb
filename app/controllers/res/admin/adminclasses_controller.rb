class Res::Admin::AdminclassesController < SimpleController::BaseController
  defaults(
    resource_class: Adminclass,
    collection_name: 'adminclasses',
    instance_name: 'adminclass',
    view_path: 'adminclasses'
  )

  auth_action :teacher
  permit_action :res_admin, :ems_admin

  belongs_to :major, optional: true

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def adminclass_params
      params.require(:adminclass).permit(
        :code, :name, :short_name, :grade, :std_type, :plan_count, :department_id, :program_id, :major_id
      )
    end
end
