class Res::Admin::TeachersController < SimpleController::BaseController
  defaults(
    resource_class: Teacher,
    collection_name: 'teachers',
    instance_name: 'teacher',
    view_path: 'teachers'
  )

  auth_action :teacher
  # permit_action :res_admin
  belongs_to :title, param: :title_id, optional: true
  belongs_to :duty,  param: :duty_id, optional: true
  belongs_to :label,  param: :label_id, optional: true
  belongs_to :major,  param: :major_id, optional: true
  belongs_to :department, param: :department_id, optional: true
end
