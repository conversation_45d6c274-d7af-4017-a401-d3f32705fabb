class Res::Admin::TitlesController < SimpleController::BaseController
  defaults(
    resource_class: Title,
    collection_name: 'titles',
    instance_name: 'title',
    view_path: 'titles'
  )

  auth_action :teacher
  permit_action :res_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def title_params
      params.require(:title).permit(
        :title_category_id, :level, :name, :series
      )
    end
end
