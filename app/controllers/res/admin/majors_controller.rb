class Res::Admin::MajorsController < SimpleController::BaseController
  defaults(
    resource_class: Major,
    collection_name: 'majors',
    instance_name: 'major',
    view_path: 'majors'
  )

  auth_action :teacher
  permit_action :res_admin, :ems_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def major_params
      params.require(:major).permit(
        :code, :name, :short_name, :eng_name, :duration, :project, :department_id, meta: {}
      )
    end
end
