class Res::Admin::TeacherTitlesController < SimpleController::BaseController
  defaults(
    resource_class: TeacherTitle,
    collection_name: 'teacher_titles',
    instance_name: 'teacher_title',
    view_path: 'teacher_titles'
  )

  auth_action :teacher
  permit_action :res_admin

  belongs_to :title_category, optional: true

  private
    def teacher_title_params
      params.require(:teacher_title).permit(
        :teacher_id, :title_id, :state, :country_title_id, :country_level, :country_auth_at, :school_title_id, :school_level, :school_auth_at, :teacher_title_id, :teacher_level, :teacher_auth_at, :title_category_id, :meta
      )
    end
end
