class Res::Admin::CollegesController < SimpleController::BaseController
  defaults(
    resource_class: College,
    collection_name: 'colleges',
    instance_name: 'college',
    view_path: 'colleges'
  )

  auth_action :teacher
  permit_action :res_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def college_params
      params.require(:college).permit(
        :department_id, :name, :code, :short_name, :eng_name, meta: {}
      )
    end
end
