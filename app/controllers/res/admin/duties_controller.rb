class Res::Admin::DutiesController < SimpleController::BaseController
  defaults(
    resource_class: Duty,
    collection_name: 'duties',
    instance_name: 'duty',
    view_path: 'duties'
  )

  auth_action :teacher
  permit_action :res_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def duty_params
      params.require(:duty).permit(
        :department_id, :is_manager, :name, teacher_ids: []
      ).merge(school_id: current_teacher.school_id)
    end
end
