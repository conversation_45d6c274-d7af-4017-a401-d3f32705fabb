class Res::Admin::ProgramsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::Program,
    collection_name: 'programs',
    instance_name: 'program',
    view_path: 'teaching/programs'
  )

  auth_action :teacher
  permit_action :res_admin, :ems_admin

  belongs_to :major, optional: true

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def update_program_params
      params.require(:program).permit(
        :major_id, :name, :grade, :department_id, :duration, :study_type, :degree, :effective_on, :invalid_on, :remark, meta: {}
      )
    end

    def create_program_params
      update_program_params.merge(
        school: current_teacher.school
      )
    end
end
