class Res::Teacher::StudentsController < SimpleController::BaseController
  defaults(
    resource_class: ::Student,
    collection_name: 'students',
    instance_name: 'student',
    view_path: 'students/teacher',
    importable_class: ::Student,
  )

  auth_action :teacher

  def find_by_file
    xlsx_file = params[:file] || importable_class.import_excel_klass.new(params[:uid])
    resource_ids = importable_class.exchange_to_ids(xlsx_file, collection, **params.to_unsafe_h.symbolize_keys)
    @students = collection.where(id: resource_ids).paginate(page: 1, per_page: 999999)
    respond_with @students, template: "#{view_path}/index", status: 200
  end

  protected
    def begin_of_association_chain
      current_teacher.school
    end
end
