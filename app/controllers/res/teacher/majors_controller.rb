class Res::Teacher::MajorsController < SimpleController::BaseController
  defaults(
    resource_class: Major,
    collection_name: 'majors',
    instance_name: 'major',
    view_path: 'majors'
  )

  auth_action :teacher

  private
    def major_params
      params.require(:major).permit(
        :school_id, :code, :name, :short_name, :eng_name, :duration, :project, :meta, :department_id
      )
    end
end
