class Ems::Admin::CourseDirsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::CourseDir,
    collection_name: 'course_dirs',
    instance_name: 'course_dir',
    view_path: 'teaching/course_dirs'
  )

  auth_action :teacher
  permit_action :ems_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def course_dir_params
      params.require(:course_dir).permit(
        :name, :code, :teacher_id, :department_id, course_set_ids: []
      )
    end
end
