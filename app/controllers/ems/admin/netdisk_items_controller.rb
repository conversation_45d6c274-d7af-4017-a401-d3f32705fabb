class Ems::Admin::NetdiskItemsController < SimpleController::BaseController
  defaults(
    resource_class: Netdisk::Item,
    collection_name: 'items',
    instance_name: 'item',
    view_path: 'netdisk/items'
  )

  auth_action :teacher
  permit_action :ems_admin

  belongs_to :course_dir

  protected

  def begin_of_association_chain
    current_teacher.school
  end

  def method_for_association_chain
    :netdisk_items
  end
end
