class Ems::Admin::CourseSetsController < SimpleController::BaseController
  defaults(
    resource_class: Teaching::CourseSet,
    collection_name: 'course_sets',
    instance_name: 'course_set',
    view_path: 'teaching/course_sets'
  )

  auth_action :teacher
  permit_action :ems_admin

  belongs_to :course_dir, optional: true

  protected
    def begin_of_association_chain
      current_auth.school
    end

  private
    def update_course_set_params
      params.require(:course_set).permit(
        :code, :credits, :enabled, :establish_on, :name, :project, :education, :category, :course_type, :department_id, :exam_mode, :period, :meta, :course_dir_id, course_hours: {}, paste_major_ids: []
      )
    end

    def create_course_set_params
      update_course_set_params.merge(
        school: current_auth.school
      )
    end
end
