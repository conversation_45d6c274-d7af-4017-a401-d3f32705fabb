class Compete::Admin::EntriesController < SimpleController::BaseController
  defaults(
    resource_class: Compete::Entry,
    collection_name: 'entries',
    instance_name: 'entry',
    view_path: 'compete/entries'
  )
  auth_action :teacher
  permit_action :compete_admin

  belongs_to :activity, collection_name: :compete_activities, shallow: true

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def entry_params
      params.require(:entry).permit(
        :user_type, :user_id, :state, payload: {}
      )
    end
end
