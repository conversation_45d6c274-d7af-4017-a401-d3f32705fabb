class Compete::Admin::ProjectsController < SimpleController::BaseController
  defaults(
    resource_class: Compete::Project,
    collection_name: 'projects',
    instance_name: 'project',
    view_path: 'compete/projects'
  )
  auth_action :teacher
  permit_action :compete_admin

  belongs_to :activity, collection_name: :compete_activities, shallow: true

  protected
    def begin_of_association_chain
      current_teacher.school
    end

  private
    def project_params
      params.require(:project).permit(
        :name, :content
      )
    end
end
