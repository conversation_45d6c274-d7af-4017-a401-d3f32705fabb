class Compete::Admin::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Compete::Activity,
    collection_name: 'activities',
    instance_name: 'activity',
    view_path: 'compete/activities'
  )

  auth_action :teacher
  permit_action :compete_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

    def method_for_association_chain
      :compete_activities
    end

  private
    def update_activity_params
      params.require(:activity).permit(
        :name, :content, :start_at, :end_at, :state, attachments: {}, meta: {}
      )
    end

    def create_activity_params
      update_activity_params.merge(
        teacher: current_teacher,
        school: current_teacher.school,
      )
    end
end
