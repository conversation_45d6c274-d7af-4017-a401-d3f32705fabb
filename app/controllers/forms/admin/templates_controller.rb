class Forms::Admin::TemplatesController < SimpleController::BaseController
  defaults(
    resource_class: Forms::Template,
    collection_name: 'templates',
    instance_name: 'template',
    view_path: 'forms/templates'
  )

  auth_action :teacher
  permit_action :forms_admin

  protected
    def begin_of_association_chain
      current_teacher.school
    end

    def method_for_association_chain
      :forms_templates
    end

  private
    def update_template_params
      params.require(:template).permit(
        :name, :type, form: {}
      )
    end

    def create_template_params
      update_template_params.merge(
        school: current_teacher.school,
        creator: current_teacher
      )
    end
end
