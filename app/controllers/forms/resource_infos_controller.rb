class Forms::ResourceInfosController < SimpleController::BaseController
  auth_action [:teacher, :student]

  # {only: [:id, :amount], methods: [:state_before_type_cast], include: { budget: { only: [:id, :name], root: 'budget_info' }, project: { only: [:name, :uid]} } }
  def find_by_ids
    # _controller = "#{Rails.application.routes.recognize_path(resource_info_params[:path])[:controller].camelize}Controller".constantize
    _controller = "#{(resource_info_params[:path]).camelize}Controller".constantize
    _resource_class = _controller.resource_class
    records = _resource_class.where(id: resource_info_params[:ids])
    render json: { records: records.as_json(resource_info_params[:config].to_h) }
  end

  private
    def resource_info_params
      params.require(:resource_info).permit(
        :path, ids: [], config: {}
      )
    end
end
