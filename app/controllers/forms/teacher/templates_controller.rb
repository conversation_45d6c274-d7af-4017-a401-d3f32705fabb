class Forms::Teacher::TemplatesController < SimpleController::BaseController
  defaults(
    resource_class: Forms::Template,
    collection_name: 'templates',
    instance_name: 'template',
    view_path: 'forms/templates'
  )

  auth_action :teacher

  protected
    def begin_of_association_chain
      current_teacher.school
    end

    def method_for_association_chain
      :forms_templates
    end
end
