# == Schema Information
#
# Table name: cm_model_defines
#
#  id                                                                                                       :bigint           not null, primary key
#  klass(对应设置的Model名称)                                                                               :string(255)
#  name(模型设置的中文名)                                                                                   :string(255)
#  association_chain(查找的关系列表)                                                                        :json
#  created_at                                                                                               :datetime         not null
#  updated_at                                                                                               :datetime         not null
#  klass_singular(自动生成的模型名称，因为路由上传是以underscore方式来传输，所以在这里需要能进行唯一性查找) :string(255)
#

class Cm::ModelDefine < ApplicationRecord
  validates_uniqueness_of :klass
  before_save :set_klass_singular!

  has_many :model_settings, dependent: :destroy

  def model_setting_by_setable setable, flag: 'relate'
    # 先查找自己的，再通过association_chain进行查找，最后再查找setable为nil的情况
    model_settings.find_by(setable: setable, flag: flag) ||
      model_setting_by_setable_with_assicaition_chain(setable, flag: flag) ||
      model_settings.find_by(setable: nil, flag: flag)
  end

  def model_setting_by_setable_with_assicaition_chain setable, flag: nil
    (association_chain || []).reverse.each do |association|
      assocaition_setable = setable.try(association)
      next if assocaition_setable.blank?

      _setting = model_settings.find_by(setable: assocaition_setable, flag: flag)
      return _setting if _setting.present?
    end
    return nil
  end

  def self.class_by_singular_name klass_singular
    find_by!(klass_singular: klass_singular.singularize).klass.constantize
  end

  def workflow_flags
    klass.constantize.workflow_settings.values.map do |workflow_setting|
      { flag: workflow_setting[:flag], flag_name: workflow_setting[:flag_name] }
    end
  rescue
    []
  end

  private

  def set_klass_singular!
    self.klass_singular = klass.constantize.model_name.singular
  end
end
