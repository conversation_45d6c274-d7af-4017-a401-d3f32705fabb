# == Schema Information
#
# Table name: cm_model_settings
#
#  id                                                                            :bigint           not null, primary key
#  model_define_id(对应某个模型的设置)                                           :bigint
#  setable_type                                                                  :string(255)
#  setable_id(绑定的对象)                                                        :bigint
#  flag(对于同一个业务对象，通过flag来区分不同类别的工作流)                      :string(255)
#  workflow_id(对应的工作流配置)                                                 :bigint
#  mform(这个只在workflow为空的时候，才会取这个字段，作为不需要工作流的模型配置) :json
#  created_at                                                                    :datetime         not null
#  updated_at                                                                    :datetime         not null
#
# Indexes
#
#  index_cm_model_settings_on_model_define_id              (model_define_id)
#  index_cm_model_settings_on_setable_type_and_setable_id  (setable_type,setable_id)
#  index_cm_model_settings_on_workflow_id                  (workflow_id)
#

class Cm::ModelSetting < ApplicationRecord
  belongs_to :model_define
  belongs_to :setable, polymorphic: true, optional: true
  belongs_to :workflow, optional: true

  def form
    workflow&.form || mform
  end

  def workflow_setting
    model_define.klass.camelize.constantize.workflow_settings[flag]
  end

  def flag_name
    workflow_setting[:flag_name]
  end
end
