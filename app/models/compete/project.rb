# == Schema Information
#
# Table name: compete_projects
#
#  id                :bigint           not null, primary key
#  activity_id(活动) :bigint
#  name(项目名称)    :string(255)
#  content(项目内容) :text(65535)
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#
# Indexes
#
#  index_compete_projects_on_activity_id  (activity_id)
#

class Compete::Project < ApplicationRecord
  belongs_to :activity
  delegate :name, to: :activity, prefix: true
  has_many :entries, through: :activity
end
