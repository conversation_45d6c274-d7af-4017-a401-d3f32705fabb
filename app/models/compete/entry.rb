# == Schema Information
#
# Table name: compete_entries
#
#  id                                      :bigint           not null, primary key
#  activity_id(对应的比赛活动)             :bigint
#  user_type                               :string(255)
#  user_id(报名的用户，可以是老师或者学生) :bigint
#  payload(报名表的内容)                   :json
#  state(报名表的状态)                     :string(255)
#  created_at                              :datetime         not null
#  updated_at                              :datetime         not null
#
# Indexes
#
#  index_compete_entries_on_activity_id            (activity_id)
#  index_compete_entries_on_user_type_and_user_id  (user_type,user_id)
#

class Compete::Entry < ApplicationRecord
  belongs_to :activity
  belongs_to :user, polymorphic: true
  delegate :name, :code, to: :user, prefix: true

  enum state: { todo: 'todo', accepted: 'accepted', rejected: 'rejected' }

  validates_uniqueness_of :activity_id, scope: [:user_type, :user_id]
end
