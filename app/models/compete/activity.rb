# == Schema Information
#
# Table name: compete_activities
#
#  id                     :bigint           not null, primary key
#  school_id(所属的学校)  :bigint
#  teacher_id(创建的老师) :bigint
#  name(名称)             :string(255)
#  content(内容)          :text(65535)
#  start_at(开始时间)     :datetime
#  end_at(结束时间)       :datetime
#  state(状态)            :string(255)
#  attachments(附件)      :json
#  meta(扩充字段)         :json
#  deleted_at(软删除字段) :datetime
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#
# Indexes
#
#  index_compete_activities_on_deleted_at  (deleted_at)
#  index_compete_activities_on_school_id   (school_id)
#  index_compete_activities_on_teacher_id  (teacher_id)
#

class Compete::Activity < ApplicationRecord
  belongs_to :school
  belongs_to :teacher, class_name: '::Teacher'
  has_many :projects, dependent: :destroy
  has_many :entries, dependent: :destroy

  enum state: { pending: 'pending', published: 'published', finished: 'finished' }

  def project_count
    projects.count
  end

  def accepted_entry_count
    entries.accepted.count
  end
end
