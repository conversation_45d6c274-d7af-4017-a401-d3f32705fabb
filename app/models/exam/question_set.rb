# == Schema Information
#
# Table name: question_sets
#
#  id                                                                                   :bigint           not null, primary key
#  type(STI type 类型)                                                                  :string(255)
#  name(题库模版的名称)                                                                 :string(255)
#  category(题库的分类)                                                                 :string(255)
#  meta(扩展字段)                                                                       :json
#  created_at                                                                           :datetime         not null
#  updated_at                                                                           :datetime         not null
#  school_id(对应的学校)                                                                :bigint
#  creator_type                                                                         :string(255)
#  creator_id(创建者)                                                                   :bigint
#  questionable_type                                                                    :string(255)
#  questionable_id(问卷关联的对象)                                                      :bigint
#  answer_mode(答案模式【disable, enable, auto】，隐藏答案/公布答案/答题后自动公布答案) :integer          default("disable")
#
# Indexes
#
#  index_question_sets_on_creator_type_and_creator_id            (creator_type,creator_id)
#  index_question_sets_on_questionable_type_and_questionable_id  (questionable_type,questionable_id)
#  index_question_sets_on_school_id                              (school_id)
#

class Exam::QuestionSet < QuestionSet
  has_many :answer_sets, class_name: 'Exam::AnswerSet', dependent: :destroy
  # meta里需要保存的信息：
  # * question的id，得分, 顺序是按照数组来实现的
  class Meta < MetaRecord
    embeds_many :catalogs, class_name: "::Exam::QuestionSet::Catalog"
    accepts_nested_attributes_for :catalogs
  end

  class Catalog < MetaRecord
    attribute :question_type, :string
    attribute :name, :string
    attribute :body, :string
    attribute :attachments, :json, default: {}
    attribute :total_score, :float
    attribute :score_mode, :string
    embeds_many :questions, class_name: "::Exam::QuestionSet::Question"
    accepts_nested_attributes_for :questions
  end

  class Question < MetaRecord
    attribute :id, :integer
    attribute :score, :float
  end

  serialize :meta, Meta

  def clone
    question_set = deep_clone include: :questions
    question_set.meta = meta
    question_set.questionable = nil
    question_set.save
    question_set.meta.catalogs.each do |m_catalog|
      m_catalog.questions.each do |_question|
        position = self.questions.find_by(id: _question.id)&.position
        _question.id = question_set.questions.find_by(position: position)&.id
      end
    end
    question_set.save
    question_set
  end
  # people_by_id = Person.find(ids).index_by(&:id) # Gives you a hash indexed by ID
  # ids.collect {|id| people_by_id[id] }
end
