# == Schema Information
#
# Table name: answer_sets
#
#  id                                          :bigint           not null, primary key
#  type(答题卷的STI)                           :string(255)
#  state(答题卷的状态，可以根据不同type不一致) :string(255)
#  question_set_id(对应的题集)                 :bigint
#  meta(扩展字段)                              :json
#  created_at                                  :datetime         not null
#  updated_at                                  :datetime         not null
#  answerable_type                             :string(255)
#  answerable_id(附属可以答题的对象)           :bigint
#  creator_type                                :string(255)
#  creator_id(创建人)                          :bigint
#  options(扩展字段，用于系统自动记录)         :json
#  result_score(得分)                          :float(24)
#
# Indexes
#
#  index_answer_sets_on_answerable_type_and_answerable_id  (answerable_type,answerable_id)
#  index_answer_sets_on_creator_type_and_creator_id        (creator_type,creator_id)
#  index_answer_sets_on_question_set_id                    (question_set_id)
#  index_answer_sets_on_state                              (state)
#

class Exam::AnswerSet < AnswerSet
  # meta里需要保存的信息：
  # * question的id，得分, 顺序是按照数组来实现的
  enum state: { todo: 'todo', doing: 'doing', done: 'done', checked: 'checked' }

  class Options < MetaRecord
    embeds_many :logs, class_name: "::Exam::AnswerSet::Log"
    accepts_nested_attributes_for :logs
  end

  class Log < MetaRecord
    attribute :ip, :string
    attribute :log_at, :datetime
    attribute :address, :string
  end

  serialize :meta, ::Exam::QuestionSet::Meta
  serialize :options, Options

  after_save :set_answer_score_when_done, if: :saved_change_to_state?

  def log! ip:, log_at:, address:
    options.logs << Exam::AnswerSet::Log.new(
      ip: ip,
      log_at: log_at,
      address: address,
  )
    self.save!
  end

  private
    def generate_empty_answers
      self.meta = question_set.meta
      self.meta.catalogs.each do |_catalog|
        _catalog.questions = _catalog.questions.shuffle
      end
      self.save

      questions.each do |question|
        answers.find_or_create_by!(question: question)
      end
    end

    def set_answer_score_when_done
      if state == 'done'
        score_hash = {}
        meta.catalogs.each do |_catalog|
          _catalog.questions.each do |_question|
            score_hash[_question.id] = _question.score
          end
        end
        answers.each do |_answer|
          answer_ana = _answer.answer_ana
          if answer_ana == 'correct'
            _result_score = _answer.result_score || score_hash[_answer.question_id]
          elsif answer_ana == 'incorrect'
            _result_score = _answer.result_score || 0
            _answer.result_score ||= 0
          end
					_answer.update_columns(result_score: _result_score)
        end
				reset_result_score!
      end
    end
end
