# == Schema Information
#
# Table name: exam_activities
#
#  id                                   :bigint           not null, primary key
#  creator_type                         :string(255)
#  creator_id(活动的创建者)             :bigint
#  title(活动的标题)                    :string(255)
#  duration_in_min(活动的时长)          :integer
#  start_at(活动开始时间)               :datetime
#  state(活动状态, 【todo,doing,done】) :string(255)
#  created_at                           :datetime         not null
#  updated_at                           :datetime         not null
#  school_id(考试对应的学校)            :bigint
#  department_name                      :string(255)
#
# Indexes
#
#  index_exam_activities_on_creator_type_and_creator_id  (creator_type,creator_id)
#  index_exam_activities_on_school_id                    (school_id)
#

class Exam::Activity < ApplicationRecord
  default_scope { order(start_at: :asc) }

  enum state: { todo: 'todo', approving: 'approving', doing: 'doing', done: 'done' }
  attribute :state, :string, default: 'todo'

  belongs_to :creator, polymorphic: true
  delegate :name, :code, to: :creator, prefix: true, allow_nil: true
  belongs_to :school
  has_one :question_set, class_name: 'Exam::QuestionSet', as: :questionable, dependent: :destroy
  has_many :answer_sets, class_name: 'Exam::AnswerSet', through: :question_set

  after_save :generate_answer_sets
  after_touch :generate_answer_sets

  include ActsAsPasting::Pasted
  pasted_with ::Student
  acts_as_pasted :students, class_name: '::Student', source_type: 'Student'

  ################# workflow ##########
  has_many :instances, class_name: 'Exam::ActivityInstance', as: :flowable, dependent: :destroy

  def instance
    instances.first
  end

  def approval!
    if state == 'todo'
      workflow = school.workflows.find_by!(type: 'Exam::ActivityWorkflow', state: 'done')
      new_instance = instances.create!(
        workflow: workflow,
        creator: creator,
      )
      new_instance.tokens.first.fire!(action: 'submit')
      update! state: 'approving'
    end
    self.instance
  end

  def flowable_info
    {
      seq: instance&.seq,
      title: title,
      state: state,
      start_at: start_at,
      duration_in_min: duration_in_min,
      creator_id: creator_id,
      creator_code: creator_code,
      creator_name: creator_name,
      student_count: student_count,
      department_name: department_name,
    }
  end

  #####################################

  def student_count
    paste_student_list.count
  end

  def student_ids
    paste_student_list.pluck(:id)
  end

  def time_infos
    now = Time.zone.now
    {
      to_start: [(start_at - now).to_i, 0].max,
      to_start_off: [(start_at - now).to_i + 30 * 60, 0].max, # 允许迟到30分钟,
      to_end: [(start_at - now).to_i + duration_in_min * 60, 0].max,
    }
  rescue
    {}
  end

  def answer_set_group_count
    answer_sets.group(:state).count
  end

  def generate_answer_sets
    self.reload
    if question_set.present? && state == 'doing'
      paste_student_list.each do |student|
        question_set.answer_sets.find_or_create_by(
          creator: student,
        ) do |answer_set|
          answer_set.state = 'todo'
        end
      end
    end
  end
end
