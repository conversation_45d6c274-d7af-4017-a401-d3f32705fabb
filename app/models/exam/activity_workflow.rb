# == Schema Information
#
# Table name: workflows
#
#  id                                               :bigint           not null, primary key
#  type(STI类型)                                    :string(255)
#  name(工作流名称)                                 :string(255)
#  desc(描述)                                       :text(65535)
#  state(状态)                                      :string(255)
#  created_at                                       :datetime         not null
#  updated_at                                       :datetime         not null
#  school_id(所属学校)                              :bigint
#  form(动态表单的json字段)                         :json
#  permit_options(是否可以查看该workflow的权限设置) :json
#  meta(工作流的附加工作字段)                       :json
#  creator_type                                     :string(255)
#  creator_id(流程创建者)                           :bigint
#  storage(workflow中存储instance的storage存储结构) :json
#  image                                            :json
#  map_keys                                         :json
#  catalog                                          :string(255)
#  permit_type(工作流权限支持的类型)                :string(255)
#  modul(工作流模块)                                :string(255)      default("业务流程")
#  print_component                                  :string(255)
#
# Indexes
#
#  index_workflows_on_creator_type_and_creator_id  (creator_type,creator_id)
#  index_workflows_on_school_id                    (school_id)
#

class Exam::ActivityWorkflow < Bpm::Workflow
  before_create :set_workflow_meta

  def instance_type
    'Exam::ActivityInstance'
  end

  private
    def set_workflow_meta
    workflow_attributes = [
      { name: '所属部门', attr: 'department_name', attr_type: 'string'},
    ]
      self.meta = {
        workflow_attributes: workflow_attributes
      }
    end
end
