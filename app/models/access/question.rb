# == Schema Information
#
# Table name: access_questions
#
#  id                             :bigint           not null, primary key
#  access_question_set_id(题目id) :bigint
#  teacher_id(教师id)             :bigint
#  title(题干)                    :text(65535)
#  meta(扩展字段)                 :json
#  score(分数)                    :integer          default(100)
#  deleted_at(软删除标识)         :datetime
#  created_at                     :datetime         not null
#  updated_at                     :datetime         not null
#  position(排序)                 :integer
#
# Indexes
#
#  index_access_questions_on_access_question_set_id  (access_question_set_id)
#  index_access_questions_on_teacher_id              (teacher_id)
#

class Access::Question < ApplicationRecord
  belongs_to :access_question_set, class_name: 'Access::QuestionSet'
  belongs_to :teacher, class_name: '::Teacher', optional: true

  acts_as_list scope: [:access_question_set_id, :teacher_id]
  default_scope { order(position: :asc) }
  scope :system, -> { where(teacher_id: nil) }

  validates :title, presence: true
end
