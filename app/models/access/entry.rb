# == Schema Information
#
# Table name: access_entries
#
#  id                                 :bigint           not null, primary key
#  teacher_id(教师id)                 :bigint
#  access_activity_id(活动id)         :bigint
#  total_score(理论分数)              :decimal(8, 2)    default(0.0)
#  score(实际分数)                    :decimal(8, 2)    default(0.0)
#  meta(扩展字段)                     :json
#  state(状态)                        :string(255)
#  deleted_at(软删除标识)             :datetime
#  created_at                         :datetime         not null
#  updated_at                         :datetime         not null
#  entry_meta(自评相关存放内容的meta) :json
#  level(评分等级)                    :string(255)      default("competent")
#  position(打分顺序)                 :integer
#
# Indexes
#
#  index_access_entries_on_access_activity_id  (access_activity_id)
#  index_access_entries_on_teacher_id          (teacher_id)
#

class Access::Entry < ApplicationRecord
  acts_as_paranoid

  include TalltyImportExport::Importable
  acts_as_list scope: :access_activity_id

  default_scope { order(position: :asc) }

  belongs_to :access_activity, class_name: 'Access::Activity', optional: true
  belongs_to :teacher, class_name: '::Teacher'
  has_many   :access_scores, class_name: 'Access::Score', foreign_key: :access_entry_id, dependent: :destroy

  validates :teacher_id, uniqueness: { scope: :access_activity_id }
  # competent: 称职 good: 优秀 qualified: 基本称职 bad: 不称职
  enum level: { competent: 'competent', good: 'good', qualified: 'qualified', bad: 'bad' }

  ransack_dynamic_attr :entry_meta, :total, question_count: :integer

  def reset_score!
    scopes = access_activity.access_scopes
    score_access_scope_ids = access_scores.pluck(:access_scope_id).uniq
    total_weight = scopes.where(id: score_access_scope_ids).pluck(:weight).sum
    # 统计大题分数 question_set
    keys   = access_activity.access_question_sets.pluck(:id).map{ |_id| ['qst_', _id] * '' }

    self.meta ||= {}
    keys.each do |key|
      _scores = access_scores.select("access_scope_id, avg(meta->'$.\"#{key}\"') as #{key}")
                              .where("ifnull(meta->'$.\"#{key}\"', 0) > ?", 0)
                              .group(:access_scope_id)
      _weight = scopes.where(id: _scores.pluck(:access_scope_id)).pluck(:weight).sum
      self.meta[key] = (_scores.reduce(0){ |sum, s| sum += s.access_scope.weight * s.send(key) * 1.0 / _weight  })
    end

    # 统计各个维度分数 score中的总分
    scores = access_scores.select("access_scope_id, avg(score) as avg_scope_score")
                          .group(:access_scope_id)
    scores.each do |s|
      _score = s.avg_scope_score
      self.meta["scope_#{s.access_scope_id}"] = _score.to_f
      self.meta["detail_scope_#{s.access_scope_id}"] = {
        score: _score,
        number: access_scores.where(access_scope_id: s.access_scope_id).done.count
      }
    end

    scopes.pluck(:id).each do |_id|
      key = "scope_#{_id}"
      next if _id.in?(score_access_scope_ids)
      self.meta["scope_#{_id}"] = 0
      self.meta["detail_scope_#{_id}"] = {
        score: 0,
        number: 0
      }
    end

    self.score = scores.reduce(0) { |sum, s| sum += s.access_scope.weight * s.avg_scope_score / total_weight }
    save
  end

  def get_access_score(teacher_id)
    access_scores.find_by(teacher_id: teacher_id)
  end

  def self.search_by_score_type(teacher_id, access_activity_id, type='')
    entries = self.where(access_activity_id: access_activity_id)
    return entries if type.to_s.in?(['all', ''])
    teacher = Teacher.find(teacher_id)

    entries = case type
            when 'score'
              entry_ids = teacher.access_scores.where(access_activity_id: access_activity_id).done
              entries.where(id: entry_ids)
            when 'noscore'
              entry_ids = teacher.access_scores.where(access_activity_id: access_activity_id).where(state: ['todo', nil])
              entries.where.not(id: entry_ids)
            end
  end

  def get_detail_score
    sql = _queue_detail_sql
    self.class.find_by_sql(sql).first
  end

  # 计算维度票数
  def get_detail_access_score
    access_scores.select("access_scope_id, level, count(*) as number")
                 .group(:access_scope_id, :level)
                 .group_by{|score| score.access_scope_id }
  end

  # 计算个人总票数
  def get_personal_score
    access_scores.select("level, count(*) as number")
                 .group(:level)
  end

  def get_no_score_scope_teacher(scope_id, opt={})
    # score_teacher_ids = access_scores.pluck(:teacher_id).push(teacher_id)
    # teacher_ids = access_activity.access_scope_teachers
    #                              .where(access_scope_id: scope_id)
    #                              .where.not(teacher_id: score_teacher_ids)
    #                              .pluck(:teacher_id)
    teacher_ids = access_scores.where(access_scope_id: scope_id, state: 'todo').pluck(:teacher_id)
    ::Teacher.where(id: teacher_ids)
              .ransack(opt)
              .result
              .pluck(:name)
  end

  def scope_teacher_ids
    access_activity.access_scopes.map do |scope|
      {
        id: scope.id,
        name: scope.name,
        teacher_ids: access_scores.where(access_scope: scope).pluck(:teacher_id)
      }
    end
  end

  def score_number
    access_scores.done.count
  end

  def total_number
    access_scores.count
  end

  def todo_score_number
    access_scores.todo.count
  end

  private
    # 获取考核维度 (sql)
    def _queue_detail_sql
      sql = [
              %q(access_entries.*),
              access_activity.generate_queue_sql,
              access_activity.generate_queue_sql('qst')
            ]
      sql.delete('')

      sql = sql * ','
      "select #{sql} from access_entries where id = #{id} and deleted_at is null"
    end

  class Import
    # 维度的导入，是导入这个维度下的score
    def import_headers **args
      [
        { key: 'position', name: '序号' },
        { key: 'teacher_name', name: '姓名' },
      ]
    end

    def import_record line_info, associations
      associations.ransack(teacher_name_eq: line_info[:teacher_name]).result.first.update position: line_info[:position]
    end
  end
end
