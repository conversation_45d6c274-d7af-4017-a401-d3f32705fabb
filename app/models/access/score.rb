# == Schema Information
#
# Table name: access_scores
#
#  id                                     :bigint           not null, primary key
#  access_activity_id(活动id)             :bigint
#  teacher_id(考核人员id)                 :bigint
#  user_id(被考核人员id)                  :bigint
#  access_entry_id(报名id)                :bigint
#  access_scope_id(考核人员范围id)        :bigint
#  score(分数)                            :decimal(8, 2)    default(0.0)
#  meta(扩展字段)                         :json
#  deleted_at(软删除标识)                 :datetime
#  created_at                             :datetime         not null
#  updated_at                             :datetime         not null
#  score_meta(用于存放question的评分结果) :json
#  level(评分等级)                        :string(255)
#
# Indexes
#
#  index_access_scores_on_access_activity_id  (access_activity_id)
#  index_access_scores_on_access_entry_id     (access_entry_id)
#  index_access_scores_on_access_scope_id     (access_scope_id)
#  index_access_scores_on_teacher_id          (teacher_id)
#  index_access_scores_on_user_id             (user_id)
#

class Access::Score < ApplicationRecord
  acts_as_paranoid

  belongs_to :access_activity,  class_name: 'Access::Activity'
  belongs_to :access_entry,     class_name: 'Access::Entry'
  belongs_to :access_scope,     class_name: 'Access::Scope'
  belongs_to :teacher,          class_name: '::Teacher'
  belongs_to :user,             class_name: '::Teacher', foreign_key: :user_id

  before_validation :set_data, on: :create
  before_save :set_level, on: [:create, :update]
  before_save :reset_score!, on: [:create, :update], if: :request_reset_score?
  after_commit  :reset_entry_score!, if: :request_reset_score?

  validates :access_entry_id, uniqueness: { scope: [:teacher_id, :access_scope_id, :deleted_at] }
  enum state: { todo: 'todo', done: 'done'}

  private
    def set_data
      self.access_activity_id = access_entry.access_activity_id if self.access_activity_id.nil?
      self.access_scope_id = access_activity.access_scope_teachers.find_by(teacher_id: teacher_id).access_scope_id if self.access_scope_id.nil?
      self.user_id = access_entry.teacher_id if self.user_id.nil?
      self.meta ||= {}
      self.state = 'todo' if self.state.nil?
      self.score_meta ||= {}
    end

    def reset_score!
      access_activity.access_question_sets.each do |q|
        _ids = q.system? ? q.access_questions.pluck(:id) : q.questions(access_entry.teacher_id).pluck(:id)
        next if _ids.size < 1
        sum, q_count = 0, 0
        _ids.each do |_id|
          next if score_meta[_id.to_s].blank?
          sum += score_meta[_id.to_s].to_i
          q_count += 1
        end
        _score = q_count > 0 ? sum * 1.0 / q_count : 0
        self.meta["qst_#{q.id}"] = _score
      end
    end

    def set_level
      score = self.score.to_i
      if score < 60
        self.level = 'bad'
      elsif score >= 60 && score < 80
        self.level = 'qualified'
      elsif score >= 80 && score < 90
        self.level = 'competent'
      else
        self.level = 'good'
      end
    end

    def reset_entry_score!
      access_entry.reset_score!
    end

    def request_reset_score?
      access_entry
    end
end
