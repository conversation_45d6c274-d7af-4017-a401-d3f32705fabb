# == Schema Information
#
# Table name: access_question_sets
#
#  id                         :bigint           not null, primary key
#  access_activity_id(活动id) :bigint
#  title(标题)                :string(255)      default("")
#  body(内容)                 :text(65535)
#  state(状态)                :string(255)
#  type(STI)                  :string(255)
#  start_at(开始时间)         :datetime
#  end_at(结束时间)           :datetime
#  meta(扩展字段)             :json
#  deleted_at(软删除标识)     :datetime
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  position(排序)             :integer
#
# Indexes
#
#  index_access_question_sets_on_access_activity_id  (access_activity_id)
#

class Access::QuestionSet < ApplicationRecord
  belongs_to :access_activity, class_name: 'Access::Activity'
  has_many   :access_questions, class_name: 'Access::Question', foreign_key: :access_question_set_id
  validates :title, :type, presence: true
  acts_as_list scope: :access_activity_id

  default_scope { order(position: :asc) }
  scope :normal, -> { where(type: 'Access::QuestionSet::Normal') }
  scope :system, -> { where(type: 'Access::QuestionSet::System') }

  class System < Access::QuestionSet
  end

  class Normal < Access::QuestionSet
    def questions(teacher_id)
      access_questions.where(teacher_id: teacher_id)
    end
  end

  def system?
    type == 'Access::QuestionSet::System'
  end
end
