# == Schema Information
#
# Table name: access_activities
#
#  id                   :bigint           not null, primary key
#  school_id(学校)      :bigint
#  name(名称)           :string(255)      default("")
#  body(活动信息)       :text(65535)
#  state(状态)          :string(255)
#  attachments(附件)    :json
#  start_at(开始时间)   :datetime
#  end_at(结束时间)     :datetime
#  meta(扩展字段)       :json
#  type(STI)            :string(255)
#  deleted_at(删除标识) :datetime
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  teacher_id(教师id)   :integer
#
# Indexes
#
#  index_access_activities_on_school_id  (school_id)
#

class Access::Activity < ApplicationRecord
  acts_as_paranoid
  include ActsAsPasting::Pasted

  pasted_with ::Teacher, prefix: :admin_
  acts_as_pasted :admin_teachers, source_type: '::Teacher', prefix: :admin_

  belongs_to :school,  class_name: '::School'
  belongs_to :teacher, class_name: '::Teacher'

  with_options foreign_key: :access_activity_id do
    has_many   :access_scopes,  class_name: 'Access::Scope'
    has_many   :access_scope_teachers, class_name: 'Access::ScopeTeacher'
    has_many   :access_entries, class_name: 'Access::Entry'
    has_many   :access_question_sets, class_name: 'Access::QuestionSet'
    has_many   :access_scores, class_name: 'Access::Score'
  end
  has_many   :teachers, through: :access_entries

  validates :name, :body, presence: true
  enum state: { pending: 'pending', publised: 'published', rejected: 'rejected' }

  after_commit :set_admin_teacher, on: [:create]

  alias_method :score_access_entries, :access_entries
  alias_method :entries, :access_entries

  class Score < Access::Activity
  end

  class Level < Access::Activity
  end

  def set_admin_teacher
    if teacher
      self.admin_teacher_ids = [teacher_id].compact
      teacher.admin_access_activity_ids = teacher.admin_access_activity_ids.push(id).compact.uniq
    end
  end

  def get_access_score(teacher_id)
    access_entries.find_by(teacher_id: teacher_id)
  end

  def get_score_count(teacher_id)
    access_scores.where(teacher_id: teacher_id).count
  end

  def done_scores_count(teacher_id=nil)
    teacher_id ? access_scores.done.where(teacher_id: teacher_id).count : access_scores.done.count
  end

  def total_scores_count(teacher_id=nil)
    teacher_id ? access_scores.where(teacher_id: teacher_id).count : access_scores.count
  end


  # 考核统计列表页 统计每位被考核人员各个维度的分数 总排名
  def get_statistic(query: nil)
    sql = <<-EOF
        access_entries.*,
        (select count(s.score)+1 from access_entries as s where s.access_activity_id = #{id} and s.score > access_entries.score) as rownum
      EOF

    sql = [
            sql,
            generate_queue_sql
          ]
    sql.delete('')

    sql = sql * ','
    entries.ransack(query).result.select(sql).unscope(:order).order("access_entries.score desc")
  end

  # 获取当前活动排名 维度排名 各个题目排名
  def get_rank(type, id)
    key = [type, id] * '_'
    result = entries.select(<<-EOF
        id, teacher_id, ifnull(meta->'$.\"#{key}\"', 0) as t_score,
        @rownum := @rownum + 1 as rownum
      EOF
      ).joins(<<-EOF
        , (select @rownum := 0) a
      EOF
    ).unscope(:order).order("t_score desc")
  end

  # 查询活动考核/被考核人数 活动给个维度平均分数 各个题目的平均分
  def queue_statistic
    # return false if entries.count < 1
    sql = <<-EOF
      (select count(*) from access_entries where access_activity_id=#{id}) as total_entry_count,
      (select count(*) from access_scope_teachers where access_activity_id=#{id}) as total_score_count,
      (select count(*) from access_entries where access_activity_id=#{id} and entry_meta is not null) as total_access_count
    EOF

    sql = [
            sql,
            generate_queue_sql,
            generate_queue_sql('qst')
          ]

    Access::Entry.levels.keys.each{|level| sql << "(select count(*) from access_entries where access_activity_id=#{id} and level='#{level}') as total_#{level}_count" }
    sql.delete('')
    sql = sql * ','

    sql = "select #{sql} from access_entries where access_activity_id=#{id} limit 1"
    Access::Entry.find_by_sql(sql).first
  end

  # 生成查询sql
  def generate_queue_sql(type='scope')
    objs, sql = type == 'scope' ? access_scopes : access_question_sets, []
    objs.each do |s|
      key = "#{type}_#{s.id}"
      sql.push(<<-EOF
              (select count(ifnull(#{type}_#{s.id}.meta->'$.\"#{key}\"', 0)) + 1
                from access_entries as #{type}_#{s.id} 
                where #{type}_#{s.id}.access_activity_id = #{id} and ifnull(#{type}_#{s.id}.meta->'$.\"#{key}\"', 0) > ifnull(access_entries.meta->'$.\"#{key}\"', 0))
                as #{type}_rownum_#{s.id},
                (select avg(meta->'$.\"#{key}\"') from access_entries where access_activity_id = #{id} and ifnull(meta->'$.\"#{key}\"', 0) > 0) as avg_#{type}_#{s.id}
              EOF
            )
    end
    sql * ','
  end

  def queue_statistic_scope_teacher
    access_scope_teachers.select(<<-EOF
      access_scope_teachers.*,
      (select count(*) from access_entries where access_entries.access_activity_id=#{id} and access_entries.teacher_id <> access_scope_teachers.teacher_id) as total_number,
      (select count(*) from access_scores where access_scores.access_activity_id=#{id} and access_scores.teacher_id = access_scope_teachers.teacher_id and access_scores.deleted_at is null) as score_number
    EOF
    )
  end
end
