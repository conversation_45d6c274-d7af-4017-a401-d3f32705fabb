# == Schema Information
#
# Table name: access_scope_teachers
#
#  id                         :bigint           not null, primary key
#  access_activity_id(活动id) :bigint
#  teacher_id(教师id)         :bigint
#  access_scope_id(维度id)    :bigint
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#
# Indexes
#
#  index_access_scope_teachers_on_access_activity_id  (access_activity_id)
#  index_access_scope_teachers_on_access_scope_id     (access_scope_id)
#  index_access_scope_teachers_on_teacher_id          (teacher_id)
#

class Access::ScopeTeacher < ApplicationRecord
  belongs_to :access_activity, class_name: 'Access::Activity', optional: true
  belongs_to :teacher,      class_name: '::Teacher'
  belongs_to :access_scope, class_name: 'Access::Scope', optional: true

  validates :teacher_id, uniqueness: { scope: :access_scope_id }

  before_create :set_access_activity_id

  private
    def set_access_activity_id
      self.access_activity_id = access_scope&.access_activity_id
    end
end
