# == Schema Information
#
# Table name: access_scopes
#
#  id                         :bigint           not null, primary key
#  access_activity_id(活动id) :bigint
#  weight(占比重)             :integer
#  name(名称)                 :string(255)
#  score_config(评分设置)     :string(255)
#  deleted_at(软删除标识)     :datetime
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  position(排序)             :integer
#  attachments(附件)          :json
#
# Indexes
#
#  index_access_scopes_on_access_activity_id  (access_activity_id)
#

class Access::Scope < ApplicationRecord
  belongs_to :access_activity, class_name: 'Access::Activity'
  has_many   :access_scope_teachers, class_name: 'Access::ScopeTeacher', foreign_key: :access_scope_id, dependent: :destroy
	has_many   :access_scores, class_name: 'Access::Score', foreign_key: :access_scope_id, dependent: :destroy
  has_many   :teachers, through: :access_scope_teachers,
                        after_remove: :destroy_access_scores!,
												after_add: :update_access_scores!

  acts_as_list scope: :access_activity_id
  default_scope { order(position: :asc) }

	validates :name, presence: true, uniqueness: { scope: :access_activity_id }
	# leader: 直接设置总分  normal: 根据每题打分自动生成总分  manual: 手动生成总分
	enum score_config: { leader: 'leader', normal: 'normal', manual: 'manual' }

	def get_scope_teacher_count(teacher_id)
		access_scope_teachers.where.not(teacher_id: teacher_id).count
	end

	def access_scores_count(access_entry_id=nil)
		access_entry_id ? access_scores.where(access_entry_id: access_entry_id).count : access_scores.count
	end

  def done_access_scores_count(access_entry_id=nil)
		access_entry_id ? access_scores.where(access_entry_id: access_entry_id).done.count : access_scores.done.count
	end

  def todo_access_scores_count(access_entry_id=nil)
		access_entry_id ? access_scores.where(state: ['todo', nil], access_entry_id: access_entry_id).count : access_scores.where(state: ['todo', nil]).count
	end

	def destroy_access_scores!(teacher)
		access_activity.access_scores.where(teacher_id: teacher.id).each{ |score| score.destroy }
	end

	def update_access_scores!(teacher)
		data = { deleted_at: nil, access_scope_id: access_activity.access_scope_teachers.find_by(teacher: teacher)&.access_scope_id }
		access_activity.access_scores.with_deleted.where(teacher_id: teacher.id).each{ |score| score.update(data) }
	end
end
