# -*- SkipSchemaAnnotations
#
# == Schema Information
#
# Table name: DZGY_JSKB
#
#  pkxxid :decimal(, )
#  rklsdm :string(30)
#  xndm   :string(4)
#  xqdm   :string(2)
#  zcbh   :string(50)
#  zcmc   :string(200)
#  week   :integer
#  ksjcdm :integer
#  jsjcdm :integer
#  zyyy   :string(953)
#  lskbxs :decimal(, )
#  zylb   :decimal(, )
#  kcdm   :string(60)
#  kcmc   :string(150)
#  jxbbh  :decimal(, )
#  bjjc   :string(4000)
#

class Bbz::Course < Bbz::Base
  self.table_name = 'DZGY_JSKB'

  def course_takes
    Bbz::CourseTake.where(jxbbh: jxbbh, jsjcdm: jsjcdm)
  end

  def self.sync school: School.first
    semester = Semester.first
    self.group(:jxbbh).count.keys.each do |key|
      bbz_courses = self.where(jxbbh: key)
      bbz_course = bbz_courses.first
      # 创建 CourseSet
      course_set = Teaching::CourseSet.find_or_create_by!(
        code: bbz_course.kcdm,
        project: '电子工业',
        school: school,
      )
      course_set.update(
        name: bbz_course.kcmc,
        period: 100,
        enabled: true,
      )
      # 创建 Course
      start_week, end_week = bbz_course.zcmc.split('-')
      course = Teaching::Course.find_or_create_by!(
        sub_school: '电子工业',
        semester: semester,
        no: bbz_course.jxbbh.to_i.to_s,
      ) do |obj|
        obj.course_set = course_set
        obj.start_week = start_week.to_i
        obj.end_week =  end_week.to_i
        obj.period = 100
        obj.name = bbz_course.zyyy
      end
      # 创建 CourseTake
      bbz_course.course_takes.each do |bbz_course_take|
        student = ::Student.find_or_create_by!(
          school: school,
          name: bbz_course_take.xm,
          code: bbz_course_take.xh,
        )
        course.course_takes.find_or_create_by!(
          student: student
        )
      end
      # 创建 CourseActivity（包含Teacher)
      bbz_courses.group(:zcbh, :week).count.keys.each do |week_state, week|
        bbz_week_courses = bbz_courses.where(week: week)

        units_arr = Teaching::CourseActivity.split_arr(bbz_week_courses.pluck(:jsjcdm))
        units_arr.each do |units|
          course_activity = course.course_activities.find_or_create_by!(
            start_unit: units[0],
            end_unit: units[-1],
            weekday: week + 1,
            week_state: '00' + week_state,
          )
          course_activity.update(
            teacher: ::Teacher.find_by(code: bbz_week_courses.first.rklsdm),
          )
          course_activity.reset_time_by_semester_schedule
          # 创建 Lesson
          course_activity.extract
        end
      end
    end
  end
end
