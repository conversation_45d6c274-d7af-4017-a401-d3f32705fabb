# -*- SkipSchemaAnnotations
#
# == Schema Information
#
# Table name: DZGY_JSXX
#
#  zgh  :string(20)       not null
#  bzsx :integer
#  xm   :string(30)       not null
#  ssbm :string(12)
#

class Bbz::Teacher < Bbz::Base
  self.table_name = "DZGY_JSXX"

  def self.sync school: School.first
    self.all.each do |bbz_teacher|
      ::Teacher.find_or_create_by!(
				school: school,
        name: bbz_teacher.xm,
        code: bbz_teacher.zgh,
      )
    end
  end
end
