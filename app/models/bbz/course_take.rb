# -*- SkipSchemaAnnotations
#
# == Schema Information
#
# Table name: DZGY_JSKB2019
#
#  xndm   :string(4)
#  xqdm   :string(2)
#  zcbh   :string(50)
#  zcmc   :string(200)
#  week   :integer
#  ksjcdm :integer
#  jsjcdm :integer
#  kcdm   :string(60)
#  kcmc   :string(150)
#  xh     :string(30)
#  xm     :string(60)
#  bjdm   :string(20)
#  zydm   :string(12)
#  zyfxdm :string(50)
#  jxbbh  :decimal(, )
#  bjjc   :string(4000)
#  rklsdm :string(30)
#

class Bbz::CourseTake < Bbz::Base
  self.table_name = 'DZGY_JSKB2019'
end
