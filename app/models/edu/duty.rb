# == Schema Information
#
# Table name: duties
#
#  id              :bigint           not null, primary key
#  duty_group_id   :bigint
#  name(职务名称)  :string
#  position(排序)  :integer
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  rank(职务等级)  :string
#  department_id   :bigint
#
# Indexes
#
#  index_duties_on_department_id  (department_id)
#  index_duties_on_duty_group_id  (duty_group_id)
#

class Edu::Duty < Edu::Base
  self.table_name = 'duties'

  belongs_to :department, optional: true

  has_many :memberships, dependent: :destroy
  has_many :members, through: :memberships
end
