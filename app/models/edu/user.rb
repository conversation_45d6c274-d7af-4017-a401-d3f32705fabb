# == Schema Information
#
# Table name: users
#
#  id                                              :bigint           not null, primary key
#  app_id                                          :bigint
#  account(账号，关联登录)                         :string
#  name(用户姓名)                                  :string
#  mobile(用户手机号)                              :string
#  email(用户邮箱)                                 :string
#  created_at                                      :datetime         not null
#  updated_at                                      :datetime         not null
#  avatar(用户头像)                                :text
#  identity_id(证件号码，需要时候可以作为唯一标识) :string
#
# Indexes
#
#  index_users_on_account  (account)
#  index_users_on_app_id   (app_id)
#

class Edu::User < Edu::Base
  self.table_name = 'users'

  has_many :members, dependent: :destroy
end
