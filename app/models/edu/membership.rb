# == Schema Information
#
# Table name: memberships
#
#  id                               :bigint           not null, primary key
#  org_id                           :bigint
#  user_id                          :bigint
#  member_id                        :bigint
#  department_id                    :bigint
#  duty_id                          :bigint
#  effective_at(生效时间，可以为空) :datetime
#  invalid_at(失效时间，可以为空)   :datetime
#  created_at                       :datetime         not null
#  updated_at                       :datetime         not null
#  app_id                           :bigint
#
# Indexes
#
#  index_memberships_on_app_id         (app_id)
#  index_memberships_on_department_id  (department_id)
#  index_memberships_on_duty_id        (duty_id)
#  index_memberships_on_member_id      (member_id)
#  index_memberships_on_org_id         (org_id)
#  index_memberships_on_user_id        (user_id)
#

class Edu::Membership < Edu::Base
  self.table_name = 'memberships'

  belongs_to :org, optional: true
  belongs_to :user
  belongs_to :member
  belongs_to :department, optional: true
  belongs_to :duty, optional: true

  before_validation :set_org
  before_validation :set_user

  default_value_for(:department_id) { |o| o.duty&.department_id }

  private

  def set_org
    self.department_id ||= duty&.department_id
    self.org_id ||= department&.org_id
  end

  def set_user
    self.user ||= member&.user
  end
end
