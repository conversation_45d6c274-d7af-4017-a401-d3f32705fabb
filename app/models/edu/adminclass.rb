# == Schema Information
#
# Table name: adminclasses
#
#  id                   :bigint           not null, primary key
#  app_id               :bigint
#  college_id           :bigint
#  major_id             :bigint
#  code(班级代码)       :string
#  name(班级名称)       :string
#  short_name(班级简称) :string
#  grade(年级)          :string
#  std_type(学生类型)   :string
#  plan_count(计划人数) :integer
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#
# Indexes
#
#  index_adminclasses_on_app_id      (app_id)
#  index_adminclasses_on_code        (code)
#  index_adminclasses_on_college_id  (college_id)
#  index_adminclasses_on_grade       (grade)
#  index_adminclasses_on_major_id    (major_id)
#  index_adminclasses_on_name        (name)
#  index_adminclasses_on_plan_count  (plan_count)
#  index_adminclasses_on_std_type    (std_type)
#

class Edu::Adminclass < Edu::Base
  self.table_name = 'adminclasses'
end
