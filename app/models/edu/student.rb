# == Schema Information
#
# Table name: members
#
#  id                                                            :bigint           not null, primary key
#  user_id                                                       :bigint
#  member_identity_id                                            :bigint
#  app_id                                                        :bigint
#  model_payload(model payload存储的字段)                        :jsonb
#  model_payload_summary(model summary存储的字段)                :jsonb
#  type(STI类型，可以是集团，或者在某些时候可能是学校这样的类型) :string
#  created_at                                                    :datetime         not null
#  updated_at                                                    :datetime         not null
#  model_flag(model flag，对应model_setting的flag)               :string
#  payload(payload payload存储的字段)                            :jsonb
#  payload_summary(payload summary存储的字段)                    :jsonb
#
# Indexes
#
#  index_members_on_app_id              (app_id)
#  index_members_on_member_identity_id  (member_identity_id)
#  index_members_on_type                (type)
#  index_members_on_user_id             (user_id)
#

class Edu::Student < Edu::Member
  has_one :adminclass_student
  has_one :adminclass, through: :adminclass_student
end
