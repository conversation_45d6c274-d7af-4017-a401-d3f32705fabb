# == Schema Information
#
# Table name: duty_groups
#
#  id               :bigint           not null, primary key
#  app_id           :bigint
#  name(角色组名称) :string
#  position(排序)   :integer
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#
# Indexes
#
#  index_duty_groups_on_app_id  (app_id)
#

class Edu::DutyGroup < Edu::Base
  self.table_name = 'duty_groups'
end
