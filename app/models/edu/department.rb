# == Schema Information
#
# Table name: departments
#
#  id                                                            :bigint           not null, primary key
#  org_id                                                        :bigint
#  department_identity_id                                        :bigint
#  code(组织标识)                                                :string
#  name(组织名称)                                                :string
#  short_name(组织简称)                                          :string
#  type(STI类型，可以是集团，或者在某些时候可能是学校这样的类型) :string
#  ancestry(树形结构)                                            :string
#  depth(树结构深度)                                             :integer
#  children_count(子对象的数据)                                  :integer          default(0)
#  created_at                                                    :datetime         not null
#  updated_at                                                    :datetime         not null
#
# Indexes
#
#  index_departments_on_ancestry                (ancestry)
#  index_departments_on_department_identity_id  (department_identity_id)
#  index_departments_on_org_id                  (org_id)
#

class Edu::Department < Edu::Base
  self.table_name = 'departments'

  has_ancestry cache_depth: true, depth_cache_column: :depth, touch: true
  self.inheritance_column = nil
end
