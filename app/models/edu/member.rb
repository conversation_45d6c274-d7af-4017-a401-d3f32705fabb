# == Schema Information
#
# Table name: members
#
#  id                                                            :bigint           not null, primary key
#  user_id                                                       :bigint
#  member_identity_id                                            :bigint
#  app_id                                                        :bigint
#  model_payload(model payload存储的字段)                        :jsonb
#  model_payload_summary(model summary存储的字段)                :jsonb
#  type(STI类型，可以是集团，或者在某些时候可能是学校这样的类型) :string
#  created_at                                                    :datetime         not null
#  updated_at                                                    :datetime         not null
#  model_flag(model flag，对应model_setting的flag)               :string
#  payload(payload payload存储的字段)                            :jsonb
#  payload_summary(payload summary存储的字段)                    :jsonb
#
# Indexes
#
#  index_members_on_app_id              (app_id)
#  index_members_on_member_identity_id  (member_identity_id)
#  index_members_on_type                (type)
#  index_members_on_user_id             (user_id)
#

class Edu::Member < Edu::Base
  self.table_name = 'members'

  belongs_to :user
  belongs_to :member_identity

  before_validation :set_relate_attributes

  private

  def set_relate_attributes
    self.app_id ||= user.app_id
    self.member_identity ||= Edu::MemberIdentity.find_by(app_id: self.app_id, member_type: type)
  end
end
