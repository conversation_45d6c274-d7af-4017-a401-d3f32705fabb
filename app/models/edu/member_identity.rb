# == Schema Information
#
# Table name: member_identities
#
#  id                           :bigint           not null, primary key
#  app_id                       :bigint
#  name(身份名称)               :string
#  member_type(Member的类型)    :string
#  ancestry(树形结构)           :string
#  depth(树结构深度)            :integer
#  children_count(子对象的数据) :integer          default(0)
#  created_at                   :datetime         not null
#  updated_at                   :datetime         not null
#  form(Member配置的表单)       :jsonb
#
# Indexes
#
#  index_member_identities_on_ancestry  (ancestry)
#  index_member_identities_on_app_id    (app_id)
#

class Edu::MemberIdentity < Edu::Base
  self.table_name = 'member_identities'
end
