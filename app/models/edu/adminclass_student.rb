# == Schema Information
#
# Table name: adminclass_students
#
#  id            :bigint           not null, primary key
#  student_id    :bigint
#  adminclass_id :bigint
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#
# Indexes
#
#  index_adminclass_students_on_adminclass_id  (adminclass_id)
#  index_adminclass_students_on_student_id     (student_id)
#

class Edu::AdminclassStudent < Edu::Base
  self.table_name = 'adminclass_students'

  belongs_to :student, class_name: 'Edu::Student'
  belongs_to :adminclass, class_name: 'Edu::Adminclass'
end
