module Edu::Ext::Student
  extend ActiveSupport::Concern

  included do
    after_create :sync_edu_record
    after_update :sync_edu_record
    after_destroy :destroy_edu_record
  end

  def sync_edu_record
    return if ENV['RAILS_ENV'] == 'test'

    user = Edu::User.where(app_id: self.school_id, account: self.code).first_or_create!
    user.update! name: self.name

    member = Edu::Student.where(user: user).first_or_create!
    member_attributes = self.attributes.except(
      'id',
      'created_at',
      'updated_at',
      'name',
      'code',
      'school_id',
      'major_id',
      'adminclass_id',
      'department_id',
      'college_id',
      'program_id',
    )
    member.update! payload: member_attributes
    # 班级
    member.update! adminclass: Edu::Adminclass.find(self.adminclass_id) if self.adminclass.present?
  end

  def destroy_edu_record
    return if ENV['RAILS_ENV'] == 'test'

    user = Edu::User.where(app_id: self.school_id, account: self.code).first
    Edu::Student.where(user: user).destroy_all
    user&.destroy
  end

  class_methods do
  end
end
