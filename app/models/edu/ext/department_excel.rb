module Edu::Ext::DepartmentExcel
  extend ActiveSupport::Concern
  include TalltyImportExport::Exportable

  included do |base|
    base.const_get('Export').include ExportEx
  end
  module ExportEx
    def export_headers **args
      [
        { key: 'name', name: '部门名称' },
        { key: 'short_name', name: '简称' },
        { key: 'code', name: '代码' },
        { key: 'path', name: '上级部门', proc: ->(val, context) { val.path.join(">") } },
        { key: 'duties_count', name: '子部门', proc: ->(val, context) { "包含#{val.duties.count}个" } },
        { key: 'manager_duties.count', name: '岗位领导', proc: ->(val, context) { "关联#{val.manager_duties.count}个岗位" } },
        { key: 'employee_duties.count', name: '岗位员工', proc: ->(val, context) { "关联#{val.duties.count - val.manager_duties.count}个岗位" } },
      ]
    end
  end

  class_methods do
  end
end
