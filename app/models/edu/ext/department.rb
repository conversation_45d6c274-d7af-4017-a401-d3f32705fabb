module Edu::Ext::Department
  extend ActiveSupport::Concern

  included do
    after_create :sync_edu_record
    after_update :sync_edu_record
    after_destroy :destroy_edu_record
  end

  def sync_edu_record
    return if ENV['RAILS_ENV'] == 'test'

    department = Edu::Department.where(id: self.id).first_or_create(org_id: self.school_id)
    department.update!(
      code: self.code,
      name: self.name,
      short_name: self.short_name,
      ancestry: self.ancestry,
      depth: self.depth,
      type: self.type&.remove('Department::'),
    )
  end

  def destroy_edu_record
    return if ENV['RAILS_ENV'] == 'test'

    Edu::Department.find_by(id: self.id)&.destroy
  end

  class_methods do
  end
end
