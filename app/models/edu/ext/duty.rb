module Edu::Ext::Duty
  extend ActiveSupport::Concern

  included do
    after_create :sync_edu_record
    after_update :sync_edu_record
    after_destroy :destroy_edu_record
  end

  def sync_edu_record
    return if ENV['RAILS_ENV'] == 'test'

    duty = Edu::Duty.where(id: self.id).first_or_create(duty_group_id: Edu::DutyGroup.first.id)
    duty.update!(
      department_id: self.department_id,
      name: self.name,
      rank: self.is_manager ? 'general_manager' : 'employee'
    )
  end

  def destroy_edu_record
    return if ENV['RAILS_ENV'] == 'test'

    Edu::Duty.find_by(id: self.id)&.destroy
  end

  def sync_duty_members
    return if ENV['RAILS_ENV'] == 'test'

    duty = Edu::Duty.find(self.id)

    if self.teachers.present?
      duty.member_ids = Edu::Teacher.ransack(user_account_in: self.teachers.pluck(:code)).result.pluck(:id)
    else
      duty.member_ids = []
    end
  end

  def add_teacher_to_duty(teacher)
    return if ENV['RAILS_ENV'] == 'test'

    members = Edu::User.find_by(app_id: teacher.school_id, account: teacher.code).members
    duty = Edu::Duty.where(id: self.id).first_or_create(duty_group_id: Edu::DutyGroup.first.id)
    duty.members << members
  end

  def remove_teacher_to_duty(teacher)
    return if ENV['RAILS_ENV'] == 'test'

    members = Edu::User.find_by(app_id: teacher.school_id, account: teacher.code).members
    duty = Edu::Duty.where(id: self.id).first_or_create(duty_group_id: Edu::DutyGroup.first.id)
    duty.members.delete(members)
  end

  class_methods do
  end
end
