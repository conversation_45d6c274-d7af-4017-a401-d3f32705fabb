module Edu::Ext::Adminclass
  extend ActiveSupport::Concern

  included do
    after_create :sync_edu_record
    after_update :sync_edu_record
    after_destroy :destroy_edu_record
  end

  def sync_edu_record
    return if ENV['RAILS_ENV'] == 'test'

    adminclass = Edu::Adminclass.where(id: self.id).first_or_create(app_id: 1)
    adminclass.update!(
      code: self.code,
      name: self.name,
      short_name: self.short_name,
      grade: self.grade,
      std_type: self.std_type,
      plan_count: self.plan_count,
      college_id: self.department_id,
      major_id: self.major&.department_id,
    )
  end

  def destroy_edu_record
    return if ENV['RAILS_ENV'] == 'test'

    Edu::Adminclass.find_by(id: self.id)&.destroy
  end

  class_methods do
  end
end
