module Edu::Ext::Teacher
  extend ActiveSupport::Concern

  included do
    after_create :sync_edu_record
    after_update :sync_edu_record
    after_destroy :destroy_edu_record
  end

  def sync_edu_record
    return if ENV['RAILS_ENV'] == 'test'

    user = Edu::User.where(app_id: self.school_id, account: self.code).first_or_create!
    user.update! name: self.name, identity_id: self.identity_id

    member = Edu::Teacher.where(user: user).first_or_create! user: user
    member_attributes =  self.attributes.except('id', 'created_at', 'updated_at', 'name', 'code', 'type', 'school_id', 'department_id', 'college_id', 'major_id', '_duty_id').merge(
      school_titles: self.active_teacher_titles.map { |title| title.school_title&.name }
    )
    member.update! payload: member_attributes
  end

  def destroy_edu_record
    return if ENV['RAILS_ENV'] == 'test'

    user = Edu::User.where(app_id: self.school_id, account: self.code).first
    Edu::Teacher.where(user: user).destroy_all
    user&.destroy
  end

  def add_duty_to_teacher(duty)
    return if ENV['RAILS_ENV'] == 'test'

    edu_duty = Edu::Duty.find(duty.id)
    members = Edu::User.find_by(app_id: self.school_id, account: self.code).members
    edu_duty.members << members
  end

  def remove_duty_to_teacher(duty)
    return if ENV['RAILS_ENV'] == 'test'

    edu_duty = Edu::Duty.find(duty.id)
    members = Edu::User.find_by(app_id: self.school_id, account: self.code).members
    edu_duty.members.delete(members)
  end

  class_methods do
  end
end
