# == Schema Information
#
# Table name: cdn_attachments
#
#  id                 :bigint           not null, primary key
#  school_id(学校)    :bigint
#  name(文件名)       :string(255)      default("")
#  address(存放路径)  :string(255)      default("")
#  filesize(文件大小) :integer          default(0)
#  filetype(文件类型) :string(255)
#  state(状态)        :string(255)
#  flag(标识)         :string(255)
#  meta(扩展字段)     :json
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  source_type(目标)  :string(255)
#  source_id(目标id)  :integer
#
# Indexes
#
#  index_cdn_attachments_on_school_id  (school_id)
#

class Cdn::Attachment < ApplicationRecord
  belongs_to :source, polymorphic: true, optional: true

  enum state: { pending: 'pending', wait: 'wait', displayed: 'displayed' }
  enum flag:  { system: 'system', mnt: 'mnt' }
  enum filetype: { image: 'image', video: 'video', other: 'other' }
  attribute :state, :string, default: 'pending'
  BASE_DIR = '/mnt/home/<USER>/cdn'

  # 同步作业数据
  def self.async_report_data
    Report.where.not(attachments: nil).find_each do |report|
      next if report.files.blank?
      report.files.each do |file|
        next if file['cdnTag'].to_i == 0
        if file['fileCategory'].to_s.in?(['video', 'image'])
          filetype = file['fileCategory']
        else
          filetype = 'other'
        end
        system.create(
          address: file['downloadPath'],
          name: file['fileName'],
          source_type:'Report',
          source_id: report.id,
          filetype: filetype,
          filesize: file['fileSize'] / 1024
        )
      end
    end
  end

  # 同步评论数据
  def self.async_comment_data
    Comment.where.not(attachments: nil).find_each do |comment|
      next if comment.files.blank?
      comment.files.each do |file|
        next if file['cdnTag'].to_i == 0
        if file['fileCategory'].to_s.in?(['video', 'image'])
          filetype = file['fileCategory']
        else
          filetype = 'other'
        end
        system.create(
          address: file['downloadPath'],
          name: file['fileName'],
          source_type: 'Comment',
          source_id: comment.id,
          filetype: filetype,
          filesize: file['fileSize'] / 1024
        )
      end
    end
  end

  # 同步课件数据
  def self.async_lesson_item_data
    Teaching::LessonItem.find_each do |lesson|
      next if lesson.attachments.blank?
      next unless files = lesson.attachments['attachments']
      files.each do |file|
        next if file['cdnTag'].to_i == 0
        if file['fileCategory'].to_s.in?(['video', 'image'])
          filetype = file['fileCategory']
        else
          filetype = 'other'
        end
        system.create(
          address: file['downloadPath'],
          name: file['fileName'],
          source_type: 'Teaching::LessonItem',
          source_id: lesson.id,
          filetype: filetype,
          filesize: file['fileSize'] / 1024
        )
      end
    end
  end

  # 同步服务器数据
  def self.async_mnt_data(base_dir='/mnt/home/<USER>/cdn')
    find_dir(BASE_DIR)
  end

  def self.find_dir(current_dir)
    Dir.foreach(current_dir) do |dir|
      next if dir.to_s.in?(['.', '..'])
      file_path = File.join(current_dir, dir)
      if File.directory?(file_path)
        find_dir(file_path)
      else
        file = File.new(file_path)
        address = file_path.to_s.gsub(BASE_DIR, '')
        filetype = Rack::Mime.mime_type(File.extname(dir))
        if filetype.to_s.include?('video')
          filetype = 'video'
        elsif filetype.to_s.include?('image')
          filetype = 'image'
        else
          filetype = 'other'
        end
        mnt.create(
          address: address,
          name: dir,
          filesize: file.size / 1024,
          filetype: filetype
        )
      end
    end
  end

  def self.set_mnt_state_wait!(options={})
    pending.mnt.where(options).find_each do |att|
      if file = system.find_by(name: att.name, address: att.address)
        att.update_columns(source_type: file.source_type, source_id: file.source_id)
      else
        att.wait!
      end
    end
  end

  def self.destroy_files(limit=1000, options={})
    wait.mnt.where(options).limit(limit).order(filesize: :desc).each do |att|
      att.destroy_disk_file
    end
  end

  def destroy_disk_file
    file_name = File.join(BASE_DIR, self.address)
    File.delete(file_name) if File.exists?(file_name)
    self.displayed!
  end

  def path
    File.join(BASE_DIR, self.address || '')
  end

  def self.compress_videos
    video.where.not("meta->'$.compressed' = ?", true).each(&:compress_video)
  end
  
  def compress_video
    return unless File.exist?(path) && meta&.[]('compressed') != true
    extname = File.extname(address) rescue return

    new_path = path.gsub(extname, '.mp4')
    transcoding_path = path.gsub(extname, '.transcoding.mp4')
    
    basename = File.basename(address)
    movie = FFMPEG::Movie.new(path)
    options = { video_codec: 'libx264' }

    if movie.width && movie.height
      options.merge!(custom: %w(-vf scale=1280:-1)) if movie.width > 1280 && movie.height < 1280
      options.merge!(custom: %w(-vf scale=-1:1280)) if movie.height > 1280
    end

    movie.transcode(transcoding_path, **options) rescue return
    raise '转换失败' unless File.exist?(transcoding_path)
    # movie.transcode(new_path, video_codec: 'libx264', custom: %w(-s 720X480)) 

    self.class.transaction do
      self.meta = (meta || {}).merge(compressed: true)
      save!
      unless extname == '.mp4' || source.nil?
        s = source
        s.attachments = JSON.parse(s.attachments.to_json.gsub(
          basename, basename.gsub(extname, '.mp4')
        ))
        s.save!
      end
    end
    FileUtils.rm(path)
    FileUtils.mv(transcoding_path, new_path)
  end

  def self.perform
    async_report_data
    async_lesson_item_data
    async_comment_data
    async_mnt_data
  end
end
