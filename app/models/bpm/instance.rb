# == Schema Information
#
# Table name: instances
#
#  id                                                        :bigint           not null, primary key
#  workflow_id(所属流程)                                     :bigint
#  creator_type                                              :string(255)
#  creator_id(流程发起人)                                    :bigint
#  payload(流程表单)                                         :json
#  state(流程状态)                                           :string(255)
#  seq(序列号)                                               :string(255)
#  type(STI类型)                                             :string(255)
#  created_at                                                :datetime         not null
#  updated_at                                                :datetime         not null
#  meta(扩展存储的字段json)                                  :json
#  flowable_type                                             :string(255)
#  flowable_id(流程化的对应实例)                             :bigint
#  storage(instance的数据存储，主要是有配置map_key 的 value) :json
#  summary(显示在列表页的 key-value)                         :json
#  flag(对于flowable时候使用进行区分)                        :string(255)
#
# Indexes
#
#  index_instances_on_creator_type_and_creator_id    (creator_type,creator_id)
#  index_instances_on_flowable_type_and_flowable_id  (flowable_type,flowable_id)
#  index_instances_on_state                          (state)
#  index_instances_on_type                           (type)
#  index_instances_on_workflow_id                    (workflow_id)
#

class Bpm::Instance < Instance
  include Bpm::Excel::Instance
  after_create :generate_token

  include AASM
  aasm :state do
    state :created, initial: true
    state :processing, :completed, :failed, :unexpected, :terminated

    event :submit do
      after do
        on_submit
      end
      transitions from: [:processing, :created], to: :processing
    end

    event :complete do
      after do
        on_complete
      end
      transitions from: [:processing, :completed], to: :completed
    end

    event :fail do
      after do
        on_fail
      end
      transitions from: :processing, to: :failed
    end

    event :terminate do
      after do
        on_terminate
      end
      transitions from: [:created, :processing, :failed], to: :terminated
    end

    event :unexpect do
      transitions from: [:processing], to: :unexpected
    end
  end

  def state_zh
    {
      created: '已提交',
      processing: '审批中',
      completed: '已完成',
      terminated: '已终止',
    }[state.to_sym]
  end

  def enable_actions user
    _token = current_token user
    action_permits = _token&.place&.transition&.callback_options&.action_permits&.with_indifferent_access || {}
    [:print, :edit, :submit, :assign, :recall, :accept, :reject, :fail, :terminate, :forward, :open].select do |action|
      action_permit = action_permits.has_key?(action) ? action_permits[action] : true
      self.send("can_#{action}?", user) && action_permit
    end
  end

  def can_open? user
    token = current_processing_token(user)
    token.present? && token.options && token.options.as_json.with_indifferent_access[:url]
  end

  def can_forward? user
    token = current_processing_token(user)
    token.present? && token.transition_type != 'Transitions::Submit'
  end

  def can_print? _user
    state == 'completed' || last_token&.place&.name.to_s.include?('打印')
  end

  def can_edit? user
    current_processing_token(user).present?
  end

  def can_assign? user
    current_preparing_token(user).present?
  end

  def can_recall? user
    tokens.where(state: [:preparing, :processing]).last&.pre_perform_token&.operator == user
  end

  def can_accept? user
    token = current_processing_token(user)
    token.present? && token.transition_type != 'Transitions::Submit'
  end

  def can_submit? user
    token = current_processing_token(user)
    token.present? && token.transition_type == 'Transitions::Submit'
  end

  def can_reject? user
    token = current_processing_token(user)
    token.present? && token.previous_token.present?
  end

  def can_fail? user
    token = current_processing_token(user)
    token.present? && token.previous_token.present?  && token.previous_token&.state != 'failed' # 如果是fail回来的就不允许再fail了
  end

  def can_terminate? user
    tokens.where(state: [:preparing, :processing], operator: user).present?
  end

  def last_token
    tokens.where(state: [:processing, :preparing]).last || tokens&.last
  end

  def current_token user
    tokens.where(state: [:preparing, :processing], operator: user)&.first || tokens.last
  end

  def current_preparing_token user
    tokens.where(state: :preparing, operator: user)&.first
  end

  def current_processing_token user
    tokens.where(state: :processing, operator: user)&.first
  end

  def self.todo_instances operator
    Instance.joins(:tokens).where(tokens: { state: ['preparing', 'processing'], operator: operator, type: 'Tokens::Approval' } )
  end

  # def self.export_xlsx instances
  #   xls_name = "工作流信息统计_#{Time.zone.now.to_i}.xlsx"
  #   origin_header = [
  #     '流程流水号',
  #     '流程名称',
  #     '姓名',
  #     '工号',
  #     '部门名称',
  #     '部门路径',
  #     '流程状态',
  #     '流程发起时间',
  #     '流程最后更新时间',
  #   ]
  #   workflow_ids = instances.group(:workflow_id).count.keys.compact
  #   p = Axlsx::Package.new
  #   wb = p.workbook
  #   workflow_ids.each do |w_id|
  #     wf = Workflow.find(w_id)
  #     wb.add_worksheet(name: wf.name) do |sheet|
  #       info_header = wf.form.fields.map(&:name)
  #       storage_info_header = wf.storage[:fields].map(&:name)
  #       workflow_info_header = wf.meta[:workflow_attributes]&.map { |attr| attr[:name] } || []
  #       header = (origin_header + info_header + storage_info_header + workflow_info_header).flatten
  #       sheet.add_row header
  #
  #       instances.where(workflow: wf).each do |instance|
  #         origin_row = [
  #           instance.seq,
  #           wf.name,
  #           instance.creator_name,
  #           instance.creator_code,
  #           instance.creator_department_name,
  #           instance.creator_department_path&.join('/'),
  #           instance.state_zh,
  #           instance.created_at,
  #           instance.updated_at,
  #         ]
  #         keys = wf.form.fields.map(&:key)
  #         info_row = keys.map do |key|
  #           value = instance.payload[key]
  #           if value&.instance_of? Array
  #             value.join(';')
  #           elsif key.start_with?('date_') && value.present?
  #             Time.zone.parse(value).to_date
  #           else
  #             "\'#{value}"
  #           end
  #         end
  #         storage_keys = wf.storage[:fields].map(&:map_key)
  #         storage_info_row = storage_keys.map do |key|
  #           value = instance.storage[key]
  #           if value&.instance_of? Array
  #             value.join(';')
  #           elsif key.start_with?('date_') && value.present?
  #             Time.zone.parse(value).to_date
  #           else
  #             "\'#{value}"
  #           end
  #         end
  #         storage_wf_attribute_keys = wf.meta[:workflow_attributes]&.map { |attr| attr[:attr] } || []
  #         storage_wf_attribute_row = storage_wf_attribute_keys&.map do |key|
  #           instance.storage[key] || instance.flowable.try(key)
  #         end || []
  #         entry_row = (origin_row + info_row + storage_info_row + storage_wf_attribute_row).flatten
  #         sheet.add_row entry_row
  #       end
  #     end
  #   end
  #
  #   FileUtils.mkdir_p './public/export'
  #   p.serialize "./public/export/#{xls_name}"
  #   ActionController::Base.helpers.asset_url("/export/#{xls_name}")
  # end


  def self.refresh_seq_by_department_abbr payload
    instance = payload[:instance]
    seq = "w#{instance.workflow.id}-#{instance.department_abbr}-#{instance.updated_at.strftime('%Y%m%d')}-#{instance.digit_single_index}"
    instance.update( seq: seq)
  rescue
    nil
  end

  def department_abbr
    instance = self
    department = instance.creator.department&.depth == 1 ?
      instance.creator.department :
      instance.creator.department&.ancestors&.find_by(depth: 1)
    department_name = department.short_name || department.name
    Spinying.parse(word: department_name).upcase
  end

  def digit_index
    index = workflow.instances.where(state: 'completed').by_day(field: :updated_at).count + 1
    no_length = options&.digit.to_i
    ("%0#{no_length}d" % index).last no_length
  end

  def digit_single_index
    workflow.instances.where(state: 'completed').by_day(field: :updated_at).count + 1
  end

  protected

  def on_submit
  end

  def on_complete
    generate_notification state: 'completed'
  end

  def on_terminate
    tokens.where(state: ['processing', 'preparing']).destroy_all
    generate_notification state: 'terminated'
  end

  def on_fail
  end

  private

  def generate_token
    workflow.start_place.transition&.create_tokens(instance: self)
  end

  def generate_notification state:
    case state.to_s
    when 'completed'
      _title = "您的一项审批工作已办结：#{self.workflow_name}"
      _state = '已办结'
    when 'terminated'
      _title = "您的一项审批工作已作废：#{self.workflow_name}"
      _state = '已作废'
    end

  Wechat::TemplateMessage.find_or_create_by(
    school: creator.school,
    app_id: 'wxb1daea392d68c3f5',
    receiver: self.creator,
    notifyable: self,
  ) do |notification|
    notification.message = {
      template_id: 'YMFWHgdaTIr8grKr3KyMZVzP4FQPHEuiq57OkGeIOVo',
      url: "#{ENV['MOBILE_URL']}/bpm/instances/#{self.id}",
      topcolor: '#FF0000',
      data: {
        first: {
          color: '#0033FF',
          value: _title,
        },
        keyword1: {
          value: _state,
        },
        keyword2: {
          value: self.workflow_name,
        },
        keyword3: {
          value: self.creator_name,
        },
        keyword4: {
          value: self.updated_at.strftime('%Y-%m-%d %H:%M'),
        },
        remark: {
          color: '#0ABB0A',
          value: "请确认办理结果。",
        },
      }
    }
  end
  end
end
