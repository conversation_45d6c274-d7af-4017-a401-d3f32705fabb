module Excel::Helper
	def handle!(&block)
		Axlsx::Package.new do |pack|
      workbook = pack.workbook
      # excel导出样式
      alignment = { vertical: :center, horizontal: :center }
      left_alignment =  { vertical: :center, horizontal: :left }
      right_alignment = { vertical: :center, horizontal: :right }

      border = { color: '969696', style: :thin }
      title1 = workbook.styles.add_style(alignment: alignment, border: border, sz: 16, b: true)
      title2 = workbook.styles.add_style(alignment: alignment, border: border, bg_color: "2a5caa",  sz: 16, fg_color: "fffffb")
      title3 = workbook.styles.add_style(alignment: alignment.merge(wrap_text: true), border: border, sz: 14)
      title4 = workbook.styles.add_style(alignment: alignment.merge(wrap_text: true), border: border, sz: 15, bg_color: "afdfe4")

      # left_title  = workbook.styles.add_style(alignment: left_alignment.merge(wrap_text: true), border: border, sz: 14)
      # right_title = workbook.styles.add_style(alignment: right_alignment.merge(wrap_text: true), border: border, sz: 14)
      # global_style = { left_style: left_title, right_style: right_title }

      if block_given?
        yield workbook, { title1: title1, title2: title2, title3: title3, title4: title4 }
      else
        workbook.add_worksheet do |sheet|
          if respond_to?(:first_header)
            row_index = Axlsx.col_ref(headers.size - 1)
            sheet.merge_cells("A1:#{row_index}1")
            sheet.add_row [first_header], style: title1, height: 40
          end

          sheet.add_row _titles, style: title2, height: 39

          if targets.is_a?(Array)
            targets.each{ |row| sheet.add_row row, style: title3, height: row_height, types: _types }
          else
            targets.each do |target|
              row = []
              if headers.is_a?(Array)
                headers.each{ |header| row.push(handle_data(target, header)) }
              else
                headers.each do |key, header|
                  header[:key] = key unless header[:key]
                  row.push(handle_data(target, header))
                end
              end
              sheet.add_row row, style: title3, height: row_height, types: _types
            end
          end
          sheet.column_widths *_widths
        end
      end

      file_path = File.join(Rails.root, 'public', 'export')
      FileUtils.mkdir_p(file_path) unless Dir.exist?(file_path)
      _state = filename.to_s.include?('/') || filename.to_s.include?('&')
      file_name = _state ? "#{Time.now.strftime('%Y%m%d%H%M%S')}.xlsx" : "#{Time.now.strftime('%Y%m%d%H%M%S')}#{filename}.xlsx"
      pack.serialize(File.join(file_path, file_name))
      url = ActionController::Base.helpers.asset_url("/export/#{file_name}")
      return url
    end
	end

  def init!
    self._titles, self._methods, self._widths, self._attr_types, self._types = [], [], [], [], []
    self._order_number = 0
    datas = headers.is_a?(Array) ? headers : headers.values
    datas.each do |header|
      header = header.with_indifferent_access
      self._titles.push(header['name'])
      self._methods.push(header['method'])
      self._widths.push(header['width'] || 30)
      self._attr_types.push(header['attr_type'])
      self._types.push(:string)
    end
  end

  def get_row_data target
    headers.map{ |header| handle_data(target, header) }
  end

  def row_height; 35; end
  def filename; ''; end
  def order_number *args; self._order_number += 1; end
  def default_headers; {}; end

  def handle_headers!
    datas, _default_headers = {}, default_headers
    if options[:titles].present?
      options[:titles].each do |k, v|
        header = _default_headers[k.to_s]
        header[:name] = v if v
        header[:key] = k unless header[:key]
        datas[k] = header
      end
    end
    options[:titles].present? ? datas : _default_headers
  end

  private
    def handle_data data, header
      begin
        if header[:method].present?
          send(header[:method], data, header[:key])
        elsif header[:attr_type] == 'date'
          handle_date data.send(header[:key])
        elsif header[:attr_type] == 'datetime'
          handle_datetime data.send(header[:key])
        else
          data.send(header[:key])
        end
      rescue
        ''
      end
    end

    def handle_date data
      data ? data.strftime('%F') : nil
    end

    def handle_datetime data
      data ? data.strftime('%F %H:%M') : nil
    end
end
