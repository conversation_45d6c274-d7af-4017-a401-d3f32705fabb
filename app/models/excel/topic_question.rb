class Excel::TopicQuestion < Excel
  def default_headers; Translate::TopicQuestion.new.headers; end
  def source
    @source ||= QuestionSet.find(args['question_set_id'])
  end
  def filename; source&.name.to_s; end
  def row_height; 60; end

  def targets
    datas = []
    source.questions.each do |q|
      content = q.title
      right_answer = q.answer_meta ? q.answer_meta['value'] : nil
      # 特殊处理单选多选
      if q.type.to_s.in?(['Question::SingleChoice', 'Question::MultipleChoice'])
        choice, content, right_answers = 'A', [q.title], []
        options = q.choices ? q.choices['options'] : []
        (options || []).each do |option|
          content.push("#{choice}: " + option['value'])
          right_answers.push(choice) if right_answer.to_s.include?(option['key'])
          choice = choice.next
        end
        content = content * "\r\n"
        right_answer = right_answers * ','
      end
      datas.push([question_types[q.type], question_types[q.type], content, right_answer, nil])
    end
    datas
  end

  def question_types
    {
      'Question::SingleChoice' => '单选题',
      'Question::MultipleChoice' => '多选题',
      'Question::FillBlank' => '填空题',
      'Question::Essay' => '简答题',
      'Question::Any' => '综合题',
    }
  end
end
