class Excel::FinanceVoucherInstance < Excel
  def headers
    [
      { name: '流程单号',   key: 'seq', type: :string },
      { name: '资金卡卡号', key: 'project_uid', method: :flowable, type: :string },
      { name: '资金卡名',   key: 'project_name', method: :flowable, width: 60 },
      { name: '申请人',     key: 'creator_name' },
      { name: '申请人工号', key: 'creator_code' },
      { name: '部门',       key: 'department_name', method: 'department_name', width: 50 },
      { name: '事由备注', key: 'remark', method: :flowable },
      { name: '科目', key: 'subject_name', method: :subject_name, width: 50 },
      { name: '来源', key: 'origin_name', method: :origin_name, width: 50 },
      { name: '报销金额(￥)', key: 'amount', method: :flowable },
      { name: '总金额(￥)', key: 'final_amount', method: :flowable },
      { name: '收款人姓名', key: 'name', method: :payee_meta },
      { name: '收款部门', key: 'department', method: :payee_meta },
      { name: '收款方式', key: 'payment_way', method: :payee_meta },
      { name: '创建时间', key: 'created_at', attr_type: 'datetime' },
      { name: '状态', key: 'state_zh' }
    ]
  end

  def targets; Finance::VoucherInstance.ransack(options[:q]).result; end
  def flowable instance, key; instance.flowable.send(key); end
  def filename; '报销记录导出'; end;
  def department_name instance, key
    instance.creator_department_path.push(instance.creator_department_name) * '/'
  end

  def payee_meta instance, key
    voucher = instance.flowable
    voucher.payee_meta[key.to_s]
  end

  def subject_name instance, key
    voucher = instance.flowable
    voucher.payments.map(&:subject_name) * ','
  end

  def origin_name instance, key
    voucher = instance.flowable
    voucher.payments.map(&:origin_name) * ','
  end
end