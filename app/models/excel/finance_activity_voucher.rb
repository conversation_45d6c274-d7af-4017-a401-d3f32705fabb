class Excel::FinanceActivityVoucher < Excel
  def headers
    [
      { name: '流程单号',   key: 'seq', type: :string },
      { name: '发起人',   key: 'teacher_name', type: :string },
      { name: '资金卡卡号', key: 'uid', method: :project, type: :string },
      { name: '资金卡名',   key: 'name', method: :project, width: 60 },
      { name: '科目', key: 'subject_name', method: :subject_name, width: 50 },
      { name: '来源', key: 'origin_name', method: :origin_name, width: 50 },
      { name: '总金额(￥)', key: 'amount', method: :amount, width: 35 },
      { name: '报销金额(￥)', key: 'final_amount', method: :amount, width: 35 },
      { name: '劳务费个税(￥)', key: 'tax', method: :amount, width: 35 },
      { name: '收款人名称', key: 'name', method: :payee_meta, width: 35 },
      { name: '收款人部门', key: 'department', method: :payee_meta, width: 35 },
      { name: '收款支付方式', key: 'payment_way', method: :payee_meta, width: 30 },
      { name: '事由备注', key: 'remark', width: 50 },
      { name: '创建时间', key: 'created_at', attr_type: 'datetime' },
      { name: '类型', key: 'type_zh' },
      { name: '状态', key: 'state_zh' },
    ]
  end

  def source_class; Finance::Activity end
  def filename; '报销单据导出'; end
  def project voucher, key; voucher.project.send(key); end
  def amount voucher, key; voucher.send(key); end
  def payee_meta voucher, key; voucher.payee_meta[key.to_s]; end
  def subject_name voucher, key; voucher.payments.map(&:subject_name) * ','; end
  def origin_name voucher, key; voucher.payments.map(&:origin_name) * ','; end

  def targets
    if options[:parent_type] == 'budget'
      vouchers = Finance::Budget.find(options[:parent_id]).vouchers
    elsif options[:parent_type] == 'project'
      vouchers = Finance::Project.find(options[:parent_id]).vouchers
    elsif options[:role] == 'teacher'
      vouchers = current_user.finance_vouchers.where(activity_id: options[:source_id])
    else
      vouchers = Finance::Activity.find(options[:source_id]).vouchers
    end
    vouchers.distinct.ransack(options[:q]).result
  end
end
