class Excel::TeachingLessonRegister < Excel::TeachingLesson
  def default_headers; Translate::TeachingLessonRegister.new(args: args).headers; end
  def filename; source&.course_set_name.to_s + '考勤统计'; end

  def lesson student, lesson_id
    lesson = Teaching::Lesson.find(lesson_id)
    register = lesson.registers.find_by(user: student)
    state = register&.state || 'absent'
    _states[state]
  end

  def total_count student, key; @count ||= source.lessons.count; end
  def done_count student, key; _registers(student).where(state: 'done').count; end
  def late_count student, key; _registers(student).where(state: 'late').count; end
  def absent_count student, key
    total_count(student, key) - _registers(student).where(state: ['done', 'late']).count
  end

  private
    def _states
      {
        'undo' => '缺勤',
        'done' => '正常',
        'late' => '迟到',
        'absent' => '缺勤'
      }
    end

    def _registers student
      @_registers = nil unless @student == student
      @student = student
      condition = { source_type: 'Teaching::Lesson', source_id: source.lessons.pluck(:id) }
      @_registers ||= student.registers.where(condition)
    end

end
