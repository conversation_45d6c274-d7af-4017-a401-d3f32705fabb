class Excel::FinanceProjectBudget < Excel
  def source_class; Finance::Project; end
  def filename; '三级单据'; end;
  def default_headers; Translate::FinanceProjectBudget.new.headers; end
  def executors budget, key; budget.executors.pluck(:name) * ','; end
  def origin_name budget, key; budget.origin_name || '资金池统筹'; end
  def finance_project; @project ||= source; end
  def project obj, key; finance_project.send(key); end
  def project_date obj, key; finance_project.send(key).strftime('%F'); end
  def tax obj, key; obj.vouchers.sum(:tax); end

  def user_infos project, key
    @roles ||= finance_project.project_roles
    @roles.each do |role|
      return role.teacher_name if key == 'name1' && role.approval_role_name == '部门领导'
      return role.teacher_name if key == 'name2' && role.approval_role_name == '归口（协管）领导'
      return role.teacher_name if key == 'name3' && role.approval_role_name == '分管领导'
      return role.teacher_name if key == 'name4' && role.approval_role_name == '校领导'
    end
    return ''
  end

  def balance budget, key
    budget.amount - budget.processing_payment_amount - budget.completed_payment_amount
  end

  def already_use budget, key
    if budget.amount > 0
      sum = budget.completed_payment_amount
      sum > 0 ? (sum * 1.0 / budget.amount).round(3) : 0
    else
      0
    end
  end

  def wish_use budget, key
    if budget.amount > 0
      sum = budget.processing_payment_amount + budget.completed_payment_amount
      sum > 0 ? (sum * 1.0 / budget.amount).round(3) : 0
    else
      0
    end
  end

  def project_states obj, key
    h = {
      'pending' => '未承诺',
      'approving' => '审批中',
      'using' => '使用中',
      'finished' => '已完成',
    }
    h[finance_project.state]
  end

end
