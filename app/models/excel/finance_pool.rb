class Excel::FinancePool < Excel
  def source; Finance::Activity.find(3); end

  def handle!
    super do |workbook, styles|
      workbook.add_worksheet do |sheet|
        titles = _titles
        _types = [:string] * titles[:title].size

        sheet.add_row titles[:title], style: styles[:title2], height: 40

        titles[:datas].each do |data|
          data = data.with_indifferent_access
          row = [data[:catalog_name], data[:plan_amount], data[:plan_amount], data[:plan_amount]]
          data[:origin_cols].each do |col|
            row.push(col[:plan_amount], col[:plan_amount], col[:plan_amount])
          end
          sheet.add_row row, style: styles[:title4], height: 35, types: _types

          data[:subject_rows].each do |option|
            option = option.with_indifferent_access
            row = [option[:subject_name], option[:plan_amount], option[:plan_amount], option[:plan_amount]]
            option[:origins_subjects].each do |col|
              row.push(col[:plan_amount], col[:plan_amount], col[:plan_amount])
            end
            sheet.add_row row, style: styles[:title3], height: 30, types: _types
          end
        end

        sheet.column_widths *_widths
      end
    end
  end

  private
    def _titles
      _titles = ['科目', '总收入', '总收入(已使用)', '总收入(已锁定)']
      data = Finance::Pool.new(source).as_plan_json

      data[:col_names].each do |name|
        _titles.push("#{name}总收入")
        _titles.push("#{name}(已使用)")
        _titles.push("#{name}(已锁定)")
      end

      self._widths = [40] * _titles.size
      {
        title: _titles,
        datas: data[:catalog_rows] || []
      }
    end
end