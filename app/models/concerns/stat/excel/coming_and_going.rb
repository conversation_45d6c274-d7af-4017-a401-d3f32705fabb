module Stat::Excel::ComingAndGoing
  extend ActiveSupport::Concern

  included do |base|
    include TalltyImportExport::Exportable
    base.const_get('Export').include ExportEx
  end

  module ExportEx
    def export_headers **args
      [
        { key: 'user_name', name: '姓名' },
        { key: 'type', name: '类型', method: :handle_type },
        { key: 'user_code', name: '工号/学号', format: :string },
        { key: 'coming_at', name: '当天最早入校时间', attr_type: :datetime },
        { key: 'going_at', name: '当天最晚出校时间', attr_type: :datetime },
        { key: 'coming_count', name: '当日入校次数'},
        { key: 'going_count', name: '当日出校次数' },
        { key: 'date', name: '日期', attr_type: :date },
        { key: 'campus', name: '校区' },
        { key: 'phone', name: '联系电话', method: :handle_phone },
        { key: 'address', name: '家庭住址', method: :handle_address },
        { key: 'jtcyxm1', name: '家庭成员名称', method: :handle_relate_name },
        { key: 'sjh1', name: '家庭成员联系方式', method: :handle_relate_phone },
      ]
    end

    def handle_type record, header
      if record.user_type == 'Teacher'
        record.user.work_way
      elsif record.user_type == 'Student'
        '学生'
      end
    rescue
      ''
    end

    def handle_phone record, header
      if record.user_type == 'Teacher'
        record.user.phone
      else
        record.user.column1_key_value['input_1609508286848']
      end
    rescue
      ''
    end

    def handle_address record, header
      if record.user_type == 'Teacher'
        record.user.address
      else
        record.user.column1_key_value['input_1609508387472']
      end
    rescue
      ''
    end

    def handle_relate_name record, header
      if record.user_type == 'Student'
        record.user.column1_key_value['input_1609508529973']
      end
    rescue
      ''
    end

    def handle_relate_phone record, header
      if record.user_type == 'Student'
        record.user.column1_key_value['input_1609508544353']
      end
    rescue
      ''
    end
  end

  module ClassMethods
  end
end
