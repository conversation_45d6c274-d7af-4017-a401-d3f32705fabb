module Ep::Ext::Teacher
  extend ActiveSupport::Concern

  # 老师的信息变动以后需要回调处理相关EP的逻辑
  # 1. 新增/修改，如果是 在职（在岗） 则需要加到相关部门的记录里；如果不在岗，则把原来的打卡签到任务设置为不打卡
  # 2. 删除，如果是 在职（在岗） 则需要加到相关部门的记录里；如果不在岗，则把原来的打卡签到任务设置为不打卡
  included do
    after_create :refresh_ep_attendance
    after_save :refresh_ep_attendance, if: :saved_change_to_state?
  end

  def refresh_ep_attendance
    if self.state == '在职（在岗）'
      self.departments_by_level(2).each do |college|
        college.self_ep_activities.each do |activity|
          activity.attendances.unscope(where: :state).find_or_create_by(
            user: self,
          ).update state: :active
        end

        college.subtree_ep_activities.each do |activity|
          activity.attendances.unscope(where: :state).find_or_create_by(
            user: self,
          ).update state: :active
        end
      end
    else
      self.ep_attendances.update(state: :unactive)
    end
  end

  class_methods do
  end
end
