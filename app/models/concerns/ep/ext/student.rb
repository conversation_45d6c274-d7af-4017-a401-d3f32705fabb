module Ep::Ext::Student
  extend ActiveSupport::Concern

  # 学生的信息变动以后需要回调处理相关EP的逻辑
  # 1. 新增/修改，如果是 在校 则需要加到相关班级的记录里；如果不在校，则把原来的打卡签到任务设置为不打卡
  # 2. 删除，如果是 在校 则需要加到相关班级的记录里；如果不在校，则把原来的打卡签到任务设置为不打卡
  included do
    after_create :refresh_ep_attendance
    after_save :refresh_ep_attendance, if: :saved_change_to_study_stat?
    has_many :ep_attendances, as: :user, class_name: 'Ep::Attendance', dependent: :destroy
  end

  def refresh_ep_attendance
    if self.study_stat == '在校' && self.adminclass.present?
      self.adminclass.ep_activities.each do |activity|
          activity.attendances.unscope(where: :state).find_or_create_by(
            user: self,
          ).update state: :active
      end
    else
      self.ep_attendances.update(state: :unactive)
    end
  end

  class_methods do
  end
end
