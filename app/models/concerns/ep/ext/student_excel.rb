module Ep::Ext::StudentExcel
  extend ActiveSupport::Concern

  included do |base|
    include TalltyImportExport::Exportable
    base.const_get('Export').include ExportEx
    include TalltyImportExport::Importable
    base.const_get('Import').include ImportEx
  end

  module ExportEx
    def export_headers **args
      [
        { key: 'name', name: '姓名', chain: [:user, :name] },
        { key: 'code', name: '学号', chain: [:user, :code], format: :string },
        { key: 'grade', name: '年级', chain: [:user, :grade] },
        { key: 'department_name', name: '学院', chain: [:user, :adminclass, :department, :name] },
        { key: 'adminclass_name', name: '班级', chain: [:user, :adminclass, :name] },
        { key: 'teacher_name', name: '辅导员', chain: [:activity, :teacher, :name], format: :string },
        { key: 'teacher_code', name: '辅导员工号', chain: [:activity, :teacher, :code]}
      ]
    end
  end

  module ImportEx
    def import_headers **args
      [
        { key: 'name', name: '姓名' },
        { key: 'student', name: '学号', convert: :convert_student },
        { key: 'department_name', name: '学院' },
        { key: 'adminclass_name', name: '班级' },
        { key: 'teacher', name: '工号', convert: :convert_teacher },
        { key: 'manage_teacher', name: '管理老师工号', convert: :convert_teacher },
      ]
    end

    def convert_student code, processing_line_info, raw_line_info
      ::Student.find_by(code: code)
    end

    def convert_teacher teacher_code, processing_line_info, raw_line_info
      ::Teacher.find_by(code: teacher_code)
    end

    def convert_college name, processing_line_info, raw_line_info
      ::Department::College.find_by(name: name)
    end

    def convert_adminclass name, processing_line_info, raw_line_info
      ::Adminclass.find_by(name: name)
    end

    # 1. 查看学生是否存在打卡，如果存在，则更新老师的信息到巡查老师里，另外假如管理教师
    # 2. 如果学生不存在打卡任务，则查看学生的班级是否存在，如果班级不存在，则需要在学院下面新建班级
    # 3. 创建好班级之后，在班级下的打卡任务假如学生老师等

    # associations 是学校的班级列表
    def import_record line_info, associations
      student = line_info[:student]
      school = student.school
      start_at = Time.zone.now.beginning_of_year
      end_at = Time.zone.now.end_of_year

      if student.ep_attendances.present?
        student.ep_attendances.each do |ep_attendance|
          ep_attendance.activity.ep_inspect_teacher_ids = (ep_attendance.activity.ep_inspect_teachers << line_info[:teacher]).map(&:id)
          ep_attendance.activity.manage_teachers = ep_attendance.activity.manage_teachers << line_info[:manage_teacher] if line_info[:manage_teacher].present?
        end
      else
        # 创建班级
        if student.adminclass.present?
          adminclass = student.adminclass
          student.update study_stat: '在校'
        else
          adminclass = Adminclass.find_or_create_by(name: line_info[:adminclass_name]) do |_adminclass|
            _adminclass.department = school.departments.find_by(name: line_info[:department_name])
          end
          student.update adminclass: adminclass, study_stat: '在校'
        end
        # 创建活动
        manage_teachers = adminclass.department.managers.tagged_with('学生管理副院长').to_a << line_info[:manage_teacher] if line_info[:manage_teacher].present?
        activity = associations.find_or_create_by!(
          name: adminclass.name,
        ) do |_activity|
          _activity.teacher = line_info[:teacher]
          _activity.start_at = start_at
          _activity.end_at = end_at
          _activity.state = 'starting'
          _activity.paste_adminclass_ids = [adminclass.id]
          _activity.paste_manageteacher_ids = manage_teachers.map(&:id)
          _activity.meta = associations.first.meta
        end
        activity.ep_inspect_teacher_ids = (activity.ep_inspect_teachers << line_info[:teacher]).map(&:id)
        # 根据学生信息，加入打卡任务
        student.reload.refresh_ep_attendance
      end
    end
  end
end
