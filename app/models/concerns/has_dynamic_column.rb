module HasDynamicColumn
  extend ActiveSupport::Concern

  included do
  end

  module ClassMethods
    def dynamic_columns *columns
      columns.each do |column|
        class_eval <<-RUBY, __FILE__, __LINE__
          def #{column}_form
            DynamicColumn.find_by(model: self.class.name, column: '#{column}')&.dynamic_form || { fields: [] }
          end

          def #{column}_key_value= val
            self.#{column} = Bpm.map_key_value(payload: val, form: #{column}_form)
          end

          def #{column}_key_value
            Bpm.map_key_value_to_key_value(map_key_value: #{column}, form: #{column}_form)
          end
        RUBY
      end
    end
  end
end
