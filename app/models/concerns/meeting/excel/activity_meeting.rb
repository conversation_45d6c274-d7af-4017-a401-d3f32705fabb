module Meeting::Excel::ActivityMeeting
  extend ActiveSupport::Concern

  included do |base|
    # include TalltyImportExport::Importable
    # base.const_get('Import').include ImportEx
    include TalltyImportExport::Exportable
    base.const_get('Export').include ExportEx
  end

  module ExportEx
    def export_headers **args
      [
        { key: 'date', name: '日期', attr_type: :date, width: 15 },
        { key: 'wday', name: '星期', method: :handle_wday, width: 10 },
        { key: 'time_range', name: '时间', method: :handle_time_range },
        { key: 'title', name: '活动内容', width: 40 },
        { key: 'moderator_names', name: '主持人', method: :handle_moderator_names },
        { key: 'user_desc', name: '参加者' },
        { key: 'meeting_room_name', name: '地点' },
        { key: 'department_names', name: '组织部门' },
      ]
    end

    def handle_wday record, header
      case record.date.wday
      when 0
        '日'
      when 1
        '一'
      when 2
        '二'
      when 3
        '三'
      when 4
        '四'
      when 5
        '五'
      when 6
        '六'
      end
    end

    def handle_time_range record, header
      "#{record.begin_time.strftime('%H:%M')} - #{record.end_time.strftime('%H:%M')}"
    end

    def handle_moderator_names record, header
      record.moderator_names&.join('、')
    end

    def first_header
      "会议活动安排"
    end
  end

  class_methods do
  end
end
