module Succ::Assessment::CompeteEntry
  extend ActiveSupport::Concern

  included do
    def succ_compete_entry_export_headers
      score_templates = dimensions.map(&:score_template).uniq
      columns = score_templates.map do |score_template|
        arr = []
        score_template.form.catalogs.each_with_index do |catalog, index|
          arr << {
            key: "#{score_template.id}__#{catalog['id']}",
            name: "【#{score_template.name}】#{catalog['name']}",
            method: :score_template_score,
          }
        end
        arr << {
          key: "#{score_template.id}",
          name: "【#{score_template.name}】平均分",
          method: :score_avg_score,
        }
      end.flatten
      columns << { key: 'score', name: '总得分' }

      [
        { key: '_index', name: '序号' },
        { key: 'seq', name: '参赛号', chain: [:user, :phone] },
        { key: 'school', name: '学校', chain: [:user, :address] },
        { key: 'name', name: '姓名', chain: [:user, :name] },
        { key: 'identity_id', name: '身份证号', chain: [:user, :identity_id], format: :string },
        { key: 'code', name: '抽签号', chain: [:user, :code] },
        *columns,
      ]
    end
  end
end
