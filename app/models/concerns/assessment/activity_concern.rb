module Assessment::ActivityConcern
  extend ActiveSupport::Concern

  included do
    has_many :assessment_entries, class_name: 'Assessment::Entry', as: :user, dependent: :destroy
    has_many :assessment_scores, class_name: 'Assessment::Score', as: :user, dependent: :destroy
    has_many :entry_assessment_activities, -> {distinct }, class_name: 'Assessment::Activity', through: :assessment_entries, source: :activity
    has_many :score_assessment_activities, -> {distinct }, class_name: 'Assessment::Activity', through: :assessment_scores, source: :activity
    has_many :score_assessment_entries, -> {distinct}, class_name: 'Assessment::Entry', through: :assessment_scores, source: :entry
  end

  def relate_assessment_activities
    Assessment::Activity.where(id: (
      entry_assessment_activities.select(:id) +
      score_assessment_activities.select(:id) )
    )
  end

  module ClassMethods
  end
end
