module StudentEnum
  extend ActiveSupport::Concern

  included do
    extend Enumerize
    enumerize :state, in: [:newcommer, :studying, :graduated], default: :studying, scope: true

    enum sex: { 男: '1', 女: '2' }, _suffix: true

    enum nation: {
      汉族: '01',
      蒙古族: '02',
      回族: '03',
      藏族: '04',
      维吾尔族: '05',
      苗族: '06',
      彝族: '07',
      壮族: '08',
      布依族: '09',
      朝鲜族: '10',
      满族: '11',
      侗族: '12',
      瑶族: '13',
      白族: '14',
      土家族: '15',
      哈尼族: '16',
      哈萨克族: '17',
      傣族: '18',
      黎族: '19',
      傈僳族: '20',
      佤族: '21',
      畲族: '22',
      高山族: '23',
      拉祜族: '24',
      水族: '25',
      东乡族: '26',
      纳西族: '27',
      景颇族: '28',
      柯尔克孜族: '29',
      土族: '30',
      达斡尔族: '31',
      仫佬族: '32',
      羌族: '33',
      布朗族: '34',
      撒拉族: '35',
      毛难族: '36',
      仡佬族: '37',
      锡伯族: '38',
      阿昌族: '39',
      普米族: '40',
      塔吉克族: '41',
      怒族: '42',
      乌孜别克族: '43',
      俄罗斯族: '44',
      鄂温克族: '45',
      崩龙族: '46',
      保安族: '47',
      裕固族: '48',
      京族: '49',
      塔塔尔族: '50',
      独龙族: '51',
      鄂伦春: '52',
      郝哲族: '53',
      门巴族: '54',
      珞巴族: '55',
      基诺族: '56',
      川青人: '57',
      穿青人: '59',
      其他族: '91',
      外国血统: '92',
      其他: '97',
      外国血统中国籍人士: '98',
      未知: '99',
      其它: '其他',
    }, _suffix: true

    enum study_stat: {
      结业: '22',
      死亡: '23',
      在校: '01',
      离校: '02',
      毕业: '06',
      退学: '09',
      休学: '15',
      保留入学资格: '18',
      保留学籍: '20',
      '': nil,
    }, _suffix: true

    enum province: {
      北京: '11',
      天津: '12',
      河北: '13',
      山西: '14',
      内蒙古: '15',
      辽宁: '21',
      吉林: '22',
      黑龙江: '23',
      上海: '31',
      江苏: '32',
      浙江: '33',
      安徽: '34',
      福建: '35',
      江西: '36',
      山东: '37',
      河南: '41',
      湖北: '42',
      湖南: '43',
      广东: '44',
      广西: '45',
      海南: '46',
      重庆: '50',
      四川: '51',
      贵州: '52',
      云南: '53',
      西藏: '54',
      陕西: '61',
      甘肃: '62',
      青海: '63',
      宁夏: '64',
      新疆: '65',
    }, _suffix: true

    enum catalog: {
      三校生: '01',
      自主招生: '02',
      上海秋季招生: '03',
      中高职转段: '04',
      五年一贯制转段: '05',
      外地生源: '51',
      保留入学资格: '00',
    }, _suffix: true
  end

  module ClassMethods
  end
end
