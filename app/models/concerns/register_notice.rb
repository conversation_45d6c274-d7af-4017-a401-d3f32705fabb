module RegisterNotice
  extend ActiveSupport::Concern

  included do
    has_many :notices, as: :source
  end

  # notice_source
  def notice_source
    self
  end

  # default from_user
  def from_user
    user
  end

  # default to_user
  def to_user
    user
  end

  module ClassMethods
    # warning
    # you must be sure caller can respond to from_user and to_user
    # @params from_user 消息发送者 example: from_user: :teacher
    # @params to_user   消息接受者 example: to_user:   :student
    # @params callbacks 回调信息
    def register_notice_callback(from_user: :from_user, to_user: :to_user, **callback)
      # raise "callback must be include method and method must be in create"
      method, state = callback[:method], callback[:state]
      key = [method, state] * '_'
      alias_method "notice_#{key}_from_user".to_sym, from_user
      alias_method "notice_#{key}_to_user".to_sym, to_user
      set_callback method, :after, "generate_#{key}_notice!".to_sym, if: callback[:condition] || true

      define_method "generate_#{key}_notice!" do
        notice = Notice.find_or_initialize_by(source: notice_source, to_user: send("notice_#{key}_to_user"))
        notice.update(from_user: send("notice_#{key}_from_user"), type: notice_source.class.to_s) unless notice.persisted?
        notice.notice_infos.find_or_create_by(source: self, flag: [method, 'd'] * '')
      end
    end
  end
end