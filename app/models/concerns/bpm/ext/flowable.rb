module Bpm::Ext::Flowable
  extend ActiveSupport::Concern

  included do
    mattr_accessor :workflow_settings
  end

  def model_define
    Cm::ModelDefine.find_by(klass: self.class.to_s)
  end

  def model_setting flag: 'relate'
    model_define&.model_setting_by_setable(self, flag: flag)
  end

  def workflows
    flags = model_define.model_settings.distinct.pluck(:flag)
    workflow_ids = flags.map do |flag|
      model_setting(flag: flag)&.workflow_id
    end.compact
    Workflow.where(id: workflow_ids)
  rescue
    Workflow.none
  end

  module ClassMethods
    def acts_as_flowable(
      flag: 'relate',
      flag_name: '',
      relation: 'many',
      workflow_attributes: [],
      workflow_callbacks: [],
      workflow_roles: [],
      instance_type: 'Bpm::Instance'
    )
      self.workflow_settings ||= {}
      self.workflow_settings[flag] = {
        flag: flag,
        flag_name: flag_name,
        workflow_attributes: workflow_attributes,
        workflow_callbacks: workflow_callbacks,
        workflow_roles: workflow_roles,
        instance_type: instance_type,
      }

      define_instance_relations(flag: flag, relation: relation, instance_type: instance_type)
      define_generate_instance_method(flag: flag)
      define_model_relations(flag: flag)
    end

    private

    def define_instance_relations flag:, relation:, instance_type:
      has_many :flowable_instances, class_name: '::Instance', as: :flowable, dependent: :nullify

      if relation.to_sym == :one
        has_one [flag.to_s, 'instance'].reject(&:blank?).join('_').to_sym, class_name: instance_type, as: :flowable, dependent: :nullify
      else
        has_many [flag.to_s, 'instances'].reject(&:blank?).join('_').to_sym, -> { where(flag: flag) }, class_name: instance_type, as: :flowable, dependent: :nullify
        has_one [flag.to_s, 'instance'].reject(&:blank?).join('_').to_sym, -> { where(flag: flag).order(id: :desc) }, class_name: instance_type, as: :flowable, dependent: :nullify
      end
    end

    def define_generate_instance_method flag:
      resource_instance_name = [flag.to_s, 'instance'].reject(&:blank?).join('_')
      collection_instance_name = [flag.to_s, 'instances'].reject(&:blank?).join('_')

      define_method(['generate', resource_instance_name, 'by_user'].join('_')) { |user, auto_submit: false, payload: {}, **args|
        workflow = model_setting(flag: flag)&.workflow
        if workflow.present?
          instance = self.send(collection_instance_name).create!(
            workflow: workflow,
            type: workflow_settings[flag][:instance_type],
            flag: flag,
            creator: user,
          )
          instance.update! payload: payload
          # 自动提交
          if auto_submit
            instance.tokens.first.fire!(action: :submit)
            instance.reload
          else
            instance
          end
        end
      }
    end

    def define_model_relations flag:
      define_method([flag.to_s, 'model_define'].reject(&:blank?).join('_')) {
        model_define(flag: flag)
      }

      define_method([flag.to_s, 'workflow'].reject(&:blank?).join('_')) {
        model_setting(flag: flag)&.workflow
      }

      define_method([flag.to_s, 'form'].reject(&:blank?).join('_')) {
        model_setting(flag: flag)&.form
      }
    end
  end
end
