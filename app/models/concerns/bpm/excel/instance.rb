module Bpm::Excel::Instance
  extend ActiveSupport::Concern

  included do |base|
    include TalltyImportExport::Exportable
    base.const_get('Export').include ExportEx
  end

  module ExportEx
    def export_headers **args
      arr = [
        { key: 'seq', name: '流水号', format: :string },
        { key: 'workflow_name', name: '流程名称', format: :string },
        { key: 'creator_code', name: '发起人工号/学号', format: :string },
        { key: 'creator_name', name: '发起人姓名', format: :string },
        { key: 'creator_sex', name: '性别', format: :string, chain: [:creator, :sex] },
        { key: 'creator_department_name', name: '学院' },
        { key: 'state_zh', name: '状态' },
        { key: 'created_at', name: '流程发起时间', attr_type: :datetime },
        { key: 'updated_at', name: '流程最后更新时间', attr_type: :datetime },
      ]
      payload_arr = Workflow.find(@group_key).form.fields.map do |field|
        next if field.key.to_s.start_with?('if_')

        { key: field.key, name: field.name, method: :instance_payload, attr_type: field.model&.attr_type }
      end rescue []
      storage_arr = Workflow.find(@group_key).storage[:fields]&.map do |field|
        { o_key: field.key, key: field.map_key, name: field.name, attr_type: field.model&.attr_type, method: :instance_storage }
      end rescue []
      arr.concat(
        (payload_arr + storage_arr).compact.uniq { |field| field[:o_key] || field[:key] }
      )
      arr
    end

    def process_options options = {}
      super(options)
      @group_by = 'workflow_id'
      @group_where = 'workflow_id_eq'
      @headers = nil
    end

    def instance_payload instance, header
      value = instance.payload[header[:key]]
      value = extract_file(value) if header[:key].to_s.start_with?('file_')
      value = extract_teachers(value) if header[:key].to_s.start_with?('teacher_')
      value = extract_students(value) if header[:key].to_s.start_with?('student_')
      value
    end

    def instance_storage instance, header
      value = instance.storage[header[:key]] || instance.flowable.try(header[:key])
      value = extract_file(value) if header[:o_key].to_s.start_with?('file_')
      value = extract_teachers(value) if header[:o_key].to_s.start_with?('teacher_')
      value = extract_students(value) if header[:o_key].to_s.start_with?('student_')
      value
    end

    def extract_file file_arr
      file_arr.map do |file|
        File.join('https://soa-file.tallty.com/', file['downloadPath'])
      end.join(',')
    end

    def extract_students student_arr
      Student.where(id: student_arr).pluck(:name).join('，')
    end

    def extract_teachers teacher_arr
      Teacher.where(id: teacher_arr).pluck(:name).join('，')
    end

    def extract_storage_teachers teacher_arr
      teacher_arr.map { |teacher| teacher['name']}.join('，')
    end
  end

  class_methods do
  end
end
