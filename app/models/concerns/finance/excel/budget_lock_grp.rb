module Finance::Excel::BudgetLockGrp
  extend ActiveSupport::Concern

  included do |base|
    include TalltyImportExport::Exportable
    base.const_get('Export').include ExportEx
  end

  module ExportEx
    def export_headers **args
      [
        { key: 'name', name: '名称' },
        { key: 'name', name: '资金卡名称' },
        { key: 'seq', name: '资金卡号' },
        { key: 'amount',  name: '总金额' },
        { key: 'completed_payment_amount',  name: '已使用' },
        { key: 'locking_amount',  name: '已锁定' },
        { key: 'processing_payment_amount',  name: '请购中' },
        { key: 'owner.name',  name: '负责人' },
        { key: 'created_at', name: '创建时间',  attr_type: 'datetime' },
        { key: 'state_zh', name: '状态' }
      ]
    end
  end
end