module Finance::Excel::Instance
  extend ActiveSupport::Concern

  included do |base|
    include TalltyImportExport::Exportable
    base.const_get('Export').include ExportEx
  end

  module ExportEx
    def export_headers **args
      [
        { key: 'seq', name: '流程单号' },
        { key: 'flowable.project_uid', name: '资金卡卡号' },
        { key: 'flowable.project_name', name: '资金卡名' },
        { key: 'creator.name',  name: '申请人' },
        { key: 'creator.code', name: '申请人工号' },
        { key: 'department_name', name: '部门', method: :department_name },
        { key: 'flowable.remark', name: '事由备注' },
        { key: 'subject_name', name: '科目',  method: :subject_name },
        { key: 'origin_name', name: '来源',method: :origin_name },
        { key: 'flowable.amount', name: '金额(￥)', key: 'amount' },
        { key: 'flowable.payee_meta.name', name: '收款人姓名', },
        { key: 'flowable.payee_meta.department', name: '收款部门' },
        { key: 'flowable.payee_meta.payment_way',  name: '收款方式' },
        { key: 'created_at', name: '创建时间',  attr_type: 'datetime' },
        { key: 'state_zh', name: '状态' }
      ]
    end

    def department_name instance, header
      instance.creator_department_path.push(instance.creator_department_name) * '/'
    end

    # def payee_meta instance, header
    #   voucher = instance.flowable
    #   voucher.payee_meta[key.to_s]
    # end

    def subject_name instance, header
      voucher = instance.flowable
      voucher.payments.map(&:subject_name) * ','
    end

    def origin_name instance, header
      voucher = instance.flowable
      voucher.payments.map(&:origin_name) * ','
    end
  end

end