# 暂未用到
module Finance::Ext::ActivityBudgetExcel
  extend ActiveSupport::Concern
  include TalltyImportExport::Exportable

  included do |base|
    base.const_get('Export').include ExportEx
  end

  module ExportEx
    def export_headers **args
      [
        { key: 'activity_year', name: '年份' },
        { key: 'activity_school_name', name: '学校' },
        { key: 'project_uid', name: '资金卡编号' },
        { key: 'project_name', name: '资金卡名称' },
        { key: 'department_name', name: '部门名称', chain: [:project, :department_code] },
        { key: 'project_category_name', name: '一级', chain: [:project, :project_category_name] },
        { key: 'catalog_name', name: '二级' },
        { key: 'name', name: '三级' },
        { key: 'subject_name', name: '科目' },
        { key: 'origin_name', name: '来源' },
        { key: 'amount', name: '金额' },
        { key: 'owner_name', name: '负责人', chain: [:project, :owner_name] },
        { key: 'processing_payment_amount', name: '报销中金额' },
        { key: 'completed_payment_amount', name: '已报销金额' },
        { key: 'purchase', name: '采购方式' },
        { key: 'start_at', name: '开始日期', chain: [:project, :start_at] },
        { key: 'end_at', name: '结束日期', chain: [:project, :end_at] },
        { key: 'state', name: '状态', chain: [:project, :state] },
      ]
    end
  end

  module ClassMethods
  end
end
