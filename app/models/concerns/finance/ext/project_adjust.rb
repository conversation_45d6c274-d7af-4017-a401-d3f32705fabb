# 资金卡延期，申请后，修改项目的延期时间
# 资金卡修改预算的金额，增或者减
# 资金卡新增三级预算

module Finance::Ext::ProjectAdjust
  extend ActiveSupport::Concern

  included do
    include Bpm::Ext::Flowable
    # 延期申请
    acts_as_flowable(
      flag: 'postpone',
      flag_name: '延期申请',
      workflow_callbacks: [
        { name: '延期申请', callback_method: :postpone_instance_callback },
      ],
      workflow_roles: workflow_roles,
    )

    # 预算金额调整
    acts_as_flowable(
      flag: 'amount_adjustment',
      flag_name: '调整预算金额',
      workflow_callbacks: [
        { name: '预算金额调整', callback_method: :amount_adjustment_instance_callback },
      ],
      workflow_roles: workflow_roles,
    )

    # 预算新增
    acts_as_flowable(
      flag: 'budget_creation',
      flag_name: '预算新增',
      workflow_callbacks: [
        { name: '预算新增', callback_method: :budget_creation_instance_callback },
      ],
      workflow_roles: workflow_roles,
    )
  end

  # end_at
  def postpone_instance_callback instance
    map_key_value = instance.map_key_value
    end_at = map_key_value[:end_at]
    update! end_at: end_at
  end

  # budgets: [
  #   { :id, :delta  }
  # ]
  def amount_adjustment_instance_callback instance
    map_key_value = instance.map_key_value

    # 检查资金池金额是否充足
    adjust_amount = map_key_value[:budgets].reduce(0) do |amount, budget_info|
      amount += budget_info[:delta].to_f
      amount
    end
    if Finance::BudgetLog.where(activity: self.activity).avaliable_amount - adjust_amount < 0
      raise Error::BaseError.new(message: '预算池金额不足')
    end

    map_key_value[:budgets].each do |budget_info|
      budget = budgets.find(budget_info[:id])
      amount = budget.amount + budget_info[:delta].to_f
      budget.update! amount: amount
    end

    # 更新资金池记录
    Finance::BudgetLog.create!(
      amount: adjust_amount * -1,
      year: Time.zone.now.year,
      creator: instance.creator,
      instance: instance,
      project_id: self.id,
      catalog: self.catalog,
      origin_id: map_key_value[:budgets]&.first[:origin_id] || self.budgets&.first&.origin_id,
      school: school,
    )
  end

  # budgets: [
  #   { :catalog_name, :amount, :number, :unit_price, :unit, :origin_id, :subject_id, :name, :purchase, :payment_way  }
  # ]
  # def budget_creation_instance_callback instance
  #   map_key_value = instance.map_key_value
  #
  #   # 检查资金池金额是否充足
  #   adjust_amount = map_key_value[:budgets].reduce(0) do |amount, budget_info|
  #     amount += budget_info[:delta].to_f
  #     amount
  #   end
  #   if Finance::BudgetLog.avaliable_amount - adjust_amount < 0
  #     raise Error::BaseError.new(message: '预算池金额不足')
  #   end
  #
  #   project_info = map_key_value[:project]&.first
  #   project = project_info.present? ? Finance::Project.find(project_info[:id]) : self
  #   _catalogs = project.catalogs
  #   map_key_value[:budgets].each do |budget_info|
  #     catalog = _catalogs.find_or_create_by!(name: budget_info[:catalog_name])
  #     catalog.budgets.create!(
  #       project: project,
  #       name: budget_info[:name],
  #       amount: budget_info[:amount],
  #       number: budget_info[:number],
  #       unit_price: budget_info[:unit_price],
  #       unit: budget_info[:unit],
  #       origin_id: budget_info[:origin_id],
  #       subject_id: budget_info[:subject_id],
  #       purchase: budget_info[:purchase],
  #       payment_way: budget_info[:payment_way],
  #     )
  #   end
  #
  #   # 更新资金池记录
  #   Finance::BudgetProjectLog.create!(
  #     amount: adjust_amount * -1,
  #     year: Time.zone.now.year,
  #     creator: instance.creator,
  #     instance: instance,
  #     project_id: project.id,
  #     catalog: project.catalog,
  #     origin_id: project.catalogs&.first&.origin_id,
  #     school: school,
  #     remark: "资金卡#{project.name}预算调整",
  #   )
  # end

  module ClassMethods
    def workflow_roles
      Finance::Voucher.workflow_roles
    end

    def project_amount_adjustment_instance_callback payload
      instance = payload[:instance]
      map_key_value = instance.map_key_value

      project_info = map_key_value[:project]&.first
      project = Finance::Project.find(project_info[:id])
      activity = project.activity
      _catalogs = project.catalogs
      _budget = Finance::Budget.find_by(id: map_key_value[:budgets]&.first&.[](:id)) || project.budgets&.first
      _origin_id = _budget&.origin_id

      # 检查资金池金额是否充足
      adjust_amount = map_key_value[:budgets].reduce(0) do |amount, budget_info|
        amount += budget_info[:delta].to_f
        amount
      end

      if Finance::BudgetLog.where(origin_id: _origin_id, activity: activity).sum(:amount) - adjust_amount < 0
        raise Error::BaseError.new(message: '预算池金额不足')
      end

      map_key_value[:budgets].each do |budget_info|
        budget = Finance::Budget.find(budget_info[:id])
        amount = budget.amount.to_f + budget_info[:delta].to_f
        budget.update! amount: amount
      end

      instance.update!(flowable: project, flag: 'amount_adjustment')

      # 更新资金池记录
      Finance::BudgetProjectLog.create!(
        amount: adjust_amount * -1,
        year: Time.zone.now.year,
        creator: instance.creator,
        instance: instance,
        project_id: project.id,
        catalog: project.catalog,
        origin_id: _origin_id,
        school: instance.creator.school,
        remark: "资金卡#{project.name}预算调减",
      )
    end

    def project_budget_creation_instance_callback payload
      instance = payload[:instance]
      map_key_value = instance.map_key_value

      project_info = map_key_value[:project]&.first
      project = Finance::Project.find(project_info[:id])
      activity = project.activity
      _catalogs = project.catalogs

      map_key_value[:budgets].group_by { |budget_info| budget_info[:origin_id] }.each do |_origin_id, budget_infos|
        # 检查资金池金额是否充足
        adjust_amount = budget_infos.reduce(0) do |amount, budget_info|
          amount += budget_info[:amount].to_f
          amount
        end

        if Finance::BudgetLog.where(origin_id: _origin_id, activity: activity).sum(:amount) - adjust_amount < 0
          raise Error::BaseError.new(message: '预算池金额不足')
        end

        budget_infos.each do |budget_info|
          catalog = _catalogs.find_or_create_by!(name: budget_info[:catalog_name])
          catalog.budgets.create!(
            project: project,
            name: budget_info[:name],
            amount: budget_info[:amount],
            number: budget_info[:number],
            unit_price: budget_info[:unit_price],
            unit: budget_info[:unit],
            origin_id: budget_info[:origin_id],
            subject_id: budget_info[:subject_id],
            purchase: budget_info[:purchase],
            payment_way: budget_info[:payment_way],
          )
        end

        instance.update!(flowable: project, flag: 'budget_creation')

        # 更新资金池记录
        Finance::BudgetProjectLog.create!(
          amount: adjust_amount * -1,
          year: Time.zone.now.year,
          creator: instance.creator,
          instance: instance,
          project_id: project.id,
          catalog: project.catalog,
          origin_id: _origin_id,
          school: instance.creator.school,
          remark: "资金卡#{project.name}预算新增",
        )
      end
    end
  end
end
