module Finance::Ext::ProjectExcel
  extend ActiveSupport::Concern
  include TalltyImportExport::Importable
  include TalltyImportExport::Exportable

  included do |base|
    base.const_get('Import').include ImportEx
    base.const_get('Export').include ExportEx
  end

  module ExportEx
    def export_headers **args
      [
        { key: '_index', name: '序号' },
        { key: 'name', name: '资金卡名' },
        { key: 'uid', name: '资金卡卡号' },
        { key: 'project_category.name', name: '一级' },
        { key: 'activity.school_name', name: '学校名' },
        { key: 'department.name', name: '部门' },
        { key: 'department_code', name: '部门编号' },
        { key: 'amount', name: '总金额' },
        { key: 'available_amount', name: '可报销', proc: ->(record, context) { record.amount.to_f - record.locking_amount - record.processing_voucher_amount - record.completed_voucher_amount } },
        { key: 'locking_amount', name: '锁定中' },
        { key: 'processing_voucher_amount', name: '报销中' },
        { key: 'completed_voucher_amount', name: '已报销' },
        { key: 'completed_ratio', name: '实际使用率', proc: ->(record, context) { record.completed_voucher_amount.to_f / record.amount } },
        { key: 'processing_completed_ratio', name: '预期使用率', proc: ->(record, context) { (record.completed_voucher_amount + record.processing_voucher_amount) / record.amount.to_f } },
        { key: 'owner.name', name: '负责人' },
        { key: 'role1', name: '部门领导', proc: ->(record, context) { record.teacher_by_role_name('部门领导')&.name } },
        { key: 'role2', name: '分管领导', proc: ->(record, context) { record.teacher_by_role_name('分管领导')&.name } },
        { key: 'role3', name: '校领导', proc: ->(record, context) { record.teacher_by_role_name('校领导')&.name } },
        { key: 'start_at', name: '开始日期', attr_type: :date },
        { key: 'end_at', name: '结束日期', attr_type: :date },
        { key: 'state_zh', name: '状态' },
      ]
    end
  end

  module ImportEx
    def import_headers **args
      [
        { key: 'uid', name: '资金卡编号' },
        { key: 'name', name: '资金卡名称' },
        { key: 'department_code', name: '部门名称', convert: :handle_department },
        { key: 'owner', name: '项目负责人工号', convert: :handle_teacher },
        { key: 'department_leader', name: '部门领导工号', convert: :handle_teacher },
        { key: 'assist_leader', name: '归口（协管）领导工号', convert: :handle_teacher },
        { key: 'charge_leader', name: '分管领导工号', convert: :handle_teacher },
        { key: 'school_leader', name: '校领导工号', convert: :handle_teacher },
        { key: 'project_category', name: '一级', convert: :handle_project_category },
        { key: 'catalog_name', name: '二级' },
        { key: 'budget_name', name: '三级明细' },
        { key: 'subject', name: '科目', convert: :handle_subject },
        { key: 'number', name: '数量' },
        { key: 'unit_price', name: '单价' },
        { key: 'amount', name: '金额' },
        { key: 'payment_way', name: '支付方式' },
        { key: 'purchase', name: '政府采购方式' },
        { key: 'origin', name: '资金来源', convert: :handle_origin },
        { key: 'start_at', name: '开始日期' },
        { key: 'end_at', name: '结束日期' },
        { key: 'state', name: '状态' },
      ]
    end

    def handle_department department_name, processing_line_info, raw_line_info
      ::Department.find_by_name(department_name)&.code
    end

    def handle_teacher teacher_code, processing_line_info, raw_line_info
      ::Teacher.find_by_code teacher_code
    end

    def handle_project_category name, processing_line_info, raw_line_info
      Finance::ProjectCategory.find_by_name name
    end

    def handle_subject name, processing_line_info, raw_line_info
      Finance::Subject.find_by_name name
    end

    def handle_origin name, processing_line_info, raw_line_info
      Finance::Origin.find_by_name name
    end

    def import_record line_info, associations
      # 创建资金卡
      project = associations.find_or_create_by!(uid: line_info[:uid]) do |_project|
        _project.name = line_info[:name]
      end
      project.start_at = line_info[:start_at]
      project.end_at = line_info[:end_at]
      project.department_code = line_info[:department_code]
      project.owner = line_info[:owner]
      project.project_category = line_info[:project_category]
      project.save!

      catalog = project.catalogs.find_or_create_by!(name: line_info[:catalog_name])

      if line_info[:department_leader].present?
        approval_role = Finance::ApprovalRole.find_by(name: '部门领导')
        project.project_roles.find_or_create_by!(teacher: line_info[:department_leader], approval_role: approval_role)
      end

      if line_info[:assist_leader].present?
        approval_role = Finance::ApprovalRole.find_by(name: '归口（协管）领导')
        project.project_roles.find_or_create_by!(teacher: line_info[:assist_leader], approval_role: approval_role)
      end

      if line_info[:charge_leader].present?
        approval_role = Finance::ApprovalRole.find_by(name: '分管领导')
        project.project_roles.find_or_create_by!(teacher: line_info[:charge_leader], approval_role: approval_role)
      end

      if line_info[:school_leader].present?
        approval_role = Finance::ApprovalRole.find_by(name: '校领导')
        project.project_roles.find_or_create_by!(teacher: line_info[:school_leader], approval_role: approval_role)
      end

      budget = catalog.budgets.find_or_create_by!(name: line_info[:budget_name], project: project, subject: line_info[:subject])
      budget.amount = line_info[:amount]
      budget.number = line_info[:number]
      budget.unit_price = line_info[:unit_price]
      budget.payment_way = line_info[:payment_way]
      budget.origin = line_info[:origin]
      budget.purchase = line_info[:purchase]
      budget.save!
    end
  end

  module ClassMethods
  end
end
