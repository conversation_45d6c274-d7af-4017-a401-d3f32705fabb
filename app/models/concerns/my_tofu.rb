module MyTofu
  extend ActiveSupport::Concern

  included do
    has_many :tofu_stars, class_name: 'Tofu::Star', as: :auth
    has_many :liked_tofu_objs, class_name: 'Tofu', through: :tofu_stars, source: :tofu
    has_many :custom_tofu_objs, class_name: 'Tofu::Custom', as: :creator

    def liked_tofus count: true
      liked_tofu_objs.by_position.reload.map do |tofu|
        top_tofu = tofu.super_tofu_id ? tofu.top_tofu : nil
        {
          id: tofu.id,
          name: tofu.name,
          url: tofu.url,
          image: tofu.image,
          mobile_url: tofu.mobile_url,
          mobilable: tofu.mobilable,
          top_tofu_name: top_tofu && top_tofu.name,
          top_tofu_image: top_tofu && top_tofu.image,
          instance_count: count ? tofu.try(:instance_count, self) : nil,
        }
      end
    end

    def liked_tofu_ids= ids
      exists_ids = tofu_stars.pluck(:tofu_id)
      remove_ids = exists_ids - ids
      add_ids = ids - exists_ids
      Tofu::Star.transaction do
        tofu_stars.where(tofu_id: remove_ids).destroy_all
        add_ids.each do |id|
          tofu_stars.create!(tofu_id: id)
        end
      end
    end

    def app_tofus mod=nil, nested: false, count: false, q: {}
      if mod
        # 过滤部分数据，优化查询
        apps = Tofu::App.where(mod: mod)
      else
        # 不传 mod => 首页/顶层
        apps = Tofu::App.all
      end

      # 新需求，需要检索
      apps = apps.ransack(q).result
      # 根据是否定义 roles 区分老师与学生，
      # teacher 权限表示该模块对所有老师可见，student 表示该模块对所有学生可见
      auths = if try(:roles)
        roles.pluck(:name) << (type === 'Expert' ? 'expert' : 'teacher' )
      else
        ['student']
      end

      Tofu::App.info_ary(apps.by_position, auths: auths, nested: nested, count_role: count ? self : nil)
    end

    def website_tofus
      auths = if try(:roles)
        roles.pluck(:name) << (type === 'Expert' ? 'expert' : 'teacher' )
      else
        ['student']
      end
      websites = Tofu::Website.by_position.select do |website|
        ((website.auths || []) & auths).any?
      end

      websites.map do |tofu|
        {
          id: tofu.id,
          name: tofu.name,
          url: tofu.url,
          mobile_url: tofu.mobile_url,
          image: tofu.image,
        }
      end
    end

    def custom_tofus
      custom_tofu_objs.by_position.reload.map do |tofu|
        {
          id: tofu.id,
          name: tofu.name,
          url: tofu.url,
          image: tofu.image,
        }
      end
    end
  end

  module ClassMethods
  end
end
