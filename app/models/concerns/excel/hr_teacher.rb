module Excel::<PERSON><PERSON><PERSON><PERSON><PERSON>
  extend ActiveSupport::Concern

  included do |base|
    include TalltyImportExport::Importable
    base.const_get('Import').include ImportEx
    include TalltyImportExport::Exportable
    base.const_get('Export').include ExportEx
  end

  module ImportEx
    def primary_keys
      [:code]
    end

    def import_headers **args
      [
        { key: 'name', name: '姓名' },
        { key: 'name_pinyin', name: '姓名拼音' },
        { key: 'identity_id', name: '证件号码' },
        { key: 'identity_type', name: '证件类型' },
        { key: 'code', name: '工号' },
        { key: 'birthday', name: '生日' },
        { key: 'sex', name: '性别' },
        { key: 'phone', name: '手机' },
        { key: 'tel', name: '电话' },
        { key: 'email', name: '邮箱' },
        # { key: 'meta', name: '扩展属性' },
        # { key: 'dynamic_attrs', name: '动态属性' },
        # { key: 'school_id', name: '所属学校' },
        # { key: 'department_name', name: '所属部门', convert: :skip },
        # { key: 'college_name', name: '学院', convert: :skip },
        { key: 'major_id', name: '专业', convert: :hanlde_major_id },
        { key: 'to_school_at', name: '进校时间', convert: :handle_date },
        { key: 'leave_school_at', name: '离校时间', convert: :handle_date },
        { key: 'work_way', name: '用人方式' },
        { key: 'degree', name: '学位' },
        { key: 'education', name: '学历' },
        { key: 'state', name: '状态' },
        { key: 'office', name: '办公室' },
        { key: 'address', name: '住址' },
        { key: 'category', name: '人员类别' },
        { key: 'politics', name: '政治面貌' },
        { key: 'place', name: '籍贯' },
        { key: 'wage', name: '工资关系' },
        { key: 'certificate_type', name: '资格证类别' },
        { key: 'expert_post_at', name: '专技岗位晋级时间' },
        *Teacher.dynamic_attrs_headers
      ]
    end

    def hanlde_major_id major_name, processing_line_info, raw_line_info
      ::Department::Major.find_by_name(major_name)&.id
    end

    def handle_date date_string, processing_line_info, raw_line_info
      Date.parse date_string.to_s rescue nil
    end
  end

  module ExportEx
    def export_headers **args
      [
        { key: 'name', name: '姓名' },
        { key: 'name_pinyin', name: '姓名拼音' },
        { key: 'identity_id', name: '证件号码', format: :string },
        { key: 'identity_type', name: '证件类型' },
        { key: 'code', name: '工号' },
        { key: 'birthday', name: '生日' },
        { key: 'sex', name: '性别' },
        { key: 'phone', name: '手机' },
        { key: 'tel', name: '电话' },
        { key: 'email', name: '邮箱' },
        { key: 'department_name', name: '所属部门', convert: :skip },
        { key: 'college_name', name: '学院', convert: :skip },
        { key: 'college_code', name: '学院编码', convert: :skip },
        { key: 'major_name', name: '专业', convert: :handle_major_name },
        { key: 'to_school_at', name: '进校时间', convert: :handle_date },
        { key: 'leave_school_at', name: '离校时间', convert: :handle_date },
        { key: 'work_way', name: '用人方式' },
        { key: 'degree', name: '学位' },
        { key: 'education', name: '学历' },
        { key: 'state', name: '状态' },
        { key: 'office', name: '办公室' },
        { key: 'address', name: '住址' },
        { key: 'category', name: '人员类别' },
        { key: 'politics', name: '政治面貌' },
        { key: 'place', name: '籍贯' },
        { key: 'wage', name: '工资关系' },
        { key: 'certificate_type', name: '资格证类别' },
        { key: 'expert_post_at', name: '专技岗位晋级时间' },
        { key: 'title', name: '职称', method: :handle_title },
        { key: 'country_title', name: '评定职称', method: :handle_country_title },
        { key: 'country_title_at', name: '评定职称', method: :handle_country_title_at },
        { key: 'school_title', name: '聘用职称', method: :handle_school_title },
        { key: 'school_title_at', name: '聘用职称', method: :handle_school_title_at },
        { key: 'teacher_title', name: '内聘职称', method: :handle_teacher_title },
        { key: 'teacher_title_at', name: '内聘职称', method: :handle_teacher_title_at },
        { key: 'latest_edu_edu', name: '全日制', method: :handle_latest_edu_edu },
        { key: 'latest_edu_school', name: '毕业院校及专业', method: :handle_latest_edu_school },
        { key: 'latest_edu_end_at', name: '毕业时间', method: :handle_latest_edu_end_at },
        { key: 'latest_edu_degree', name: '学位', method: :handle_latest_edu_degree },
        { key: 'duties', name: '职务', method: :handle_teacher_duties },
        *Teacher.dynamic_attrs_headers
      ]
    end

    def handle_latest_edu_edu record, header
      record.latest_edu&.edu
    end

    def handle_latest_edu_school record, header
      record.latest_edu&.school
    end

    def handle_latest_edu_end_at record, header
      record.latest_edu&.school
    end

    def handle_latest_edu_degree record, header
      record.latest_edu&.degree
    end

    def handle_country_title record, header
      record.active_teacher_titles.map { |title| title.country_title&.name }.join('，')
    end

    def handle_school_title record, header
      record.active_teacher_titles.map { |title| title.school_title&.name }.join('，')
    end

    def handle_teacher_title record, header
      record.active_teacher_titles.map { |title| title.teacher_title&.name }.join('，')
    end

    def handle_country_title_at record, header
      record.active_teacher_titles.map { |title| title.country_auth_at }.join('，')
    end

    def handle_school_title_at record, header
      record.active_teacher_titles.map { |title| title.school_auth_at }.join('，')
    end

    def handle_teacher_title_at record, header
      record.active_teacher_titles.map { |title| title.teacher_auth_at }.join('，')
    end

    def handle_title record, header
      record.active_teacher_titles.map(&:max_level).join('，')
    end

    def handle_teacher_duties record, header
      record.duties.pluck(:name).join('，')
    end
  end

  module ClassMethods
    def dynamic_attrs_headers
      dynamic_column = DynamicColumn.find_by(model: 'Teacher', column: 'dynamic_attrs')
      return [] unless dynamic_column && dynamic_column.form && dynamic_column.form['fields']
      dynamic_column.form['fields'].map do |field|
        { key: field['key'], name: field['name'], json: :dynamic_attrs }
      end
    end
  end
end
