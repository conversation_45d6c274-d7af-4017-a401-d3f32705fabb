module Excel::PtPractice
  extend ActiveSupport::Concern

  included do |base|
    include TalltyImportExport::Importable
    include TalltyImportExport::Exportable
    base.const_get('Import').include ImportEx
    base.const_get('Export').include ExportEx
  end

  module ImportEx
    def import_headers **args
      [
        { key: 'student', name: '学号', convert: :handle_student },
        { key: 'teacher_by_code', name: '辅导教师工号', convert: :handle_teacher_code },
        { key: 'teacher_by_name', name: '辅导教师姓名', convert: :handle_teacher_name },
        { key: 'start_at', name: '开始时间', convert: :handle_date },
        { key: 'end_at', name: '结束时间', convert: :handle_date },
        { key: 'company', name: '实习单位' },
        { key: 'city', name: '实习省市' },
      ]
    end

    def handle_student student_code, processing_line_info, raw_line_info
      ::Student.find_by code: student_code
    end

    def handle_teacher_code teacher_code, processing_line_info, raw_line_info
      ::Teacher.find_by_code teacher_code
    end

    def handle_teacher_name teacher_name, processing_line_info, raw_line_info
      ::Teacher.find_by_name teacher_name
    end

    def handle_date date_string, processing_line_info, raw_line_info
      Date.parse date_string.to_s
    end

    def import_record line_info, associations
      practice = associations.find_or_initialize_by(student: line_info[:student])
      practice.assign_attributes start_at: line_info[:start_at], end_at: line_info[:end_at], origin: 'system', company: line_info[:company], state: 'starting'
      practice.save!
      practice.guide_teachers.find_or_create_by(teacher: line_info[:teacher_by_code] || line_info[:teacher_by_name]).update state: 'active'
    end
  end

  module ExportEx
    def export_headers **args
      [
        { key: 'student_name', name: '姓名', chain: [:student, :name]  },
        { key: 'student_code', name: '学号', chain: [:student, :code]  },
        { key: 'start_at', name: '开始时间', format: :date  },
        { key: 'end_at', name: '结束时间', format: :date  },
        { key: 'student_college', name: '学院', chain: [:student, :college_name] },
        { key: 'student_major', name: '专业', chain: [:student, :major_name] },
        { key: 'student_adminclass', name: '班级', chain: [:student, :adminclass_name] },
        { key: 'student_sex', name: '性别', chain: [:student, :sex] },
        { key: 'report_avg_score', name: '实习周记得分', method: :extract_nested_score },
        { key: 'company_score', name: '企业评价', method: :extract_nested_score },
        { key: 'thesis_score', name: '实习报告', method: :extract_nested_score },
        { key: 'reply_score', name: '答辩成绩', method: :extract_nested_score },
        { key: 'course_score', name: '过程成绩', method: :extract_nested_score },
        { key: 'total_score', name: '总分', method: :extract_nested_score },
      ]
    end

    def extract_nested_score record, header
      record.nested_score.with_indifferent_access[header[:key]]
    end
  end

  module ClassMethods
  end
end
