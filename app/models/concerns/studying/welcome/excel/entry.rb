module Studying::Welcome::Excel::Entry
  extend ActiveSupport::Concern

  included do |base|
    include TalltyImportExport::Exportable
    base.const_get('Export').include ExportEx
  end

  module ExportEx
    def export_headers **args
      [
        { key: 'activity_name', name: '迎新活动' },
        { key: 'student_name', name: '姓名' },
        { key: 'student_code', name: '准考证号' },
        { key: 'identity_id', name: '身份证号', format: :string },
        { key: 'instance.state', name: '状态' },
        { key: 'student.admisstion_major_name', name: '专业名称' },
        { key: 'student.sex', name: '性别' },
        { key: 'instance.storage.height', name: '身高（cm)' },
        { key: 'instance.storage.weight', name: '体重（kg）' },
        { key: 'instance.storage.waistline', name: '腰围（cm）' },
        { key: 'instance.storage.waistline', name: '腰围（cm）' },
        { key: 'instance.storage.live_in_campus', name: '是否住宿' },
        { key: 'instance.storage.duration', name: '公共交通时间' },
        { key: 'instance.storage.学费', name: '学费' },
        { key: 'instance.storage.代办费', name: '代办费' },
        { key: 'instance.storage.工具箱', name: '工具箱' },
        { key: 'instance.storage.住宿费', name: '住宿费' },
        { key: 'instance.storage.g1_relation', name: '监护人1关系' },
        { key: 'instance.storage.g1_name', name: '监护人1姓名' },
        { key: 'instance.storage.g1_mobile', name: '监护人1手机号' },
        { key: 'instance.storage.g1_identity_id', name: '监护人1身份证号', format: :string },
        { key: 'instance.storage.g2_relation', name: '监护人2关系' },
        { key: 'instance.storage.g2_name', name: '监护人2姓名' },
        { key: 'instance.storage.g2_mobile', name: '监护人2手机号' },
        { key: 'instance.storage.g2_identity_id', name: '监护人2身份证号', format: :string },
        { key: 'instance.storage.funding_policy', name: '资助情况', format: :string },
      ]
    end
  end
end
