module Authable
	extend ActiveSupport::Concern

	included do
	end

	module ClassMethods
		def auth! request
			token = request.env['HTTP_AUTHORIZATION'].to_s.sub(/^Token /, '')
			user_info = AuthToken.auth! token
      user_info['type'] ||= user_info['account_type']
      user_info['code'] ||= user_info['account']
			type = user_info['type']
      if type == self.to_s || type == 'Expert'
        user_info
      else
				raise Error::AuthError.new
      end
		end

		def idcode idcode
			AuthToken.auth! idcode
		end
	end
end
