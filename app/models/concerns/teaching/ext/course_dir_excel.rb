module Teaching::Ext::CourseDirExcel
  extend ActiveSupport::Concern

  included do |base|
    include TalltyImportExport::Exportable
    base.const_get('Export').include ExportEx
    include TalltyImportExport::Importable
    base.const_get('Import').include ImportEx
  end

  module ExportEx
    def export_headers **args
      [
        { key: 'id', name: '索引' },
        { key: 'name', name: '课程目录名称' },
        { key: 'teacher_name', name: '课程负责人', format: :string },
        { key: 'teacher_code', name: '课程负责人工号' },
        { key: 'department_name', name: '所属部门'},
        { key: 'major_name', name: '所属专业' },
        { key: 'course_type', name: '课程类别' },
      ]
    end
  end

  module ImportEx
    def import_headers **args
      [
        { key: 'id', name: '索引', primary_key: true  },
        { key: 'name', name: '课程目录名称' },
        { key: 'teacher', name: '课程负责人工号', convert: :handle_teacher_code },
        { key: 'department', name: '所属部门', convert: :handle_department_name },
        { key: 'department_major', name: '所属专业', convert: :handle_department_major_name },
        { key: 'course_type', name: '课程类别' },
      ]
    end

    def handle_teacher_code teacher_code, processing_line_info, raw_line_info
      ::Teacher.find_by_code teacher_code
    end

    def handle_department_name department_name, processing_line_info, raw_line_info
      Department::College.find_by_name department_name
    end

    def handle_department_major_name major_name, processing_line_info, raw_line_info
      Department::Major.find_by_name major_name
    end

    def valid? line_info
      line_info[:department].present? || line_info[:department_major].present?
    end
  end

  class_methods do
  end
end
