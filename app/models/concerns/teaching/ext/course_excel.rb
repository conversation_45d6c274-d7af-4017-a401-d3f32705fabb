module Teaching::Ext::CourseExcel
  extend ActiveSupport::Concern

  included do |base|
    include TalltyImportExport::Importable
    base.const_get('Import').include ImportEx
  end


  module ImportEx
    def import_headers **args
      [
        { key: 'name', name: '姓名' },
        { key: 'code', name: '工号' },
        { key: 'department', name: '开通范围' },
        { key: 'state', name: '状态' }
      ]
    end

    def import_record line_info, associations
      associations = associations.where(semester: School.first.current_semester)
      results = line_info[:department] == '全校' ?
        associations :
        associations.ransack(department_name_or_teach_depart_name_eq: line_info[:department].strip).result
      results.find_each do |result|
        teacher = Teacher.find_by(code: line_info[:code].to_i.to_s)
        next unless teacher
        if line_info[:state] == '终止'
          result.courses_inspectors.find_by(inspector: teacher)&.destroy
        else
          result.courses_inspectors.find_or_create_by!(inspector: teacher)
        end
      end
    end
  end

  class_methods do
  end
end
