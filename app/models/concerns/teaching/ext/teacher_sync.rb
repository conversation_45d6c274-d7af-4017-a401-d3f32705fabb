module Teaching::Ext::TeacherSync
  extend ActiveSupport::Concern

  included do
  end

  def sync_to_stiei_teaching
    StieiTeaching::TeacherTemp.find_or_create_by(id: code) do |_teacher|
      _teacher.code = code
      _teacher.name = name
      _teacher.department = StieiTeaching::Department.find_by(name: college.name) if college.present?
      _teacher.mobile = phone
      _teacher.email = email
      _teacher.xb = sex
      _teacher.sfzh = identity_id
    end
  end

  def sync_to_stiei_auth
    StieiTeaching::UserTemp.find_or_create_by(id: code) do |_user|
      _user.fullname = name
      _user.name = code
    end
  end

  class_methods do
  end
end
