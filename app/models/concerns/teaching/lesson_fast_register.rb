module Teaching::LessonFastRegister
	extend ActiveSupport::Concern

  included do

    def autoswitch_registers
      fast ? fast_registers : registers
    end

    def create_fast
      Teaching::Lesson::Fast.create_by_record(self)
    end

    def fast
      Teaching::Lesson::Fast.find(lesson_id: id).first
    end

    def fast_registers
      Register::LessonRegister::Fast.find(lesson_id: id).to_a
    end

    # 签到时间范围计算
    def register_time_rules
      [ENV['REGISTER_TIME_OFFSET_START'].to_i.minutes, ENV['REGISTER_TIME_OFFSET_END'].to_i.minutes]
    end

    def calculate_register_time_range
      [start_datetime + register_time_rules.first, start_datetime + register_time_rules.last]
    end

    # 评价时间范围计算
    def evaluate_time_rules
      [ENV['EVALUATE_TIME_OFFSET_START'].to_i.minutes, ENV['EVALUATE_TIME_OFFSET_END'].to_i.minutes]
    end

    def calculate_evaluate_time_range
      [end_datetime + evaluate_time_rules.first, end_datetime + evaluate_time_rules.last]
    end
	end

  module ClassMethods
    def cache_today_fast_objects
      Teaching::Lesson.where(date: Date.today).find_each do |lesson|
        lesson.create_fast
        lesson.students.each do |student|
          p student
          # 签到创建数据库对象
          register = lesson.registers.find_or_initialize_by(user: student)
          register.update!(state: 'undo') if register.new_record?
          # 签到对象录入 Ohm
          Register::LessonRegister::Fast.create_by_record(register)
          # 评价创建数据库对象
          evaluation = lesson.evaluations.find_or_initialize_by(student: student)
          evaluation.update!(
            question_set_id: student.school.teaching_evaluate_question_sets.last&.id,
            state: 'todo',
          ) if evaluation.new_record?

          # 手动释放内存
          evaluation = nil
          register = nil
        end
      end
    end
	end
end
