module StieiFund::PaymentScope
  extend ActiveSupport::Concern

  included do
    belongs_to :school_dic, class_name: 'StieiFund::Dic', foreign_key: 'school'
    delegate :name, to: :school_dic, prefix: true, allow_nil: true

    include AASM

    enum state: {
      付款失败: '4028817163ece82c0163ed52d03a0003',
      付款成功: '4028817163ece82c0163ed52892a0002',
      凭证出纳审核不通过: '4028a081554e5ace01554e5fe4ac0007',
      凭证出纳审核通过: '4028a081554e5ace01554e5f8e430006',
      凭证分管领导免签: '4028817163c8515d0163c855d69c0002',
      凭证分管领导审核不通过: '402881ef5fbd74e0015fbd7f3c3c0003',
      凭证分管领导审核通过: '402881ef5fbd74e0015fbd7ec66d0002',
      凭证已收件: '4028518157b35bb60157b36fc3a80001',
      凭证已经保存: '4028a081554e5ace01554e5d68d70002',
      凭证已经提交: '4028a081554e5ace01554e5da50b0003',
      凭证已退件: '4028518157b35bb60157b37366700002',
      凭证负责人免签: '402881d16458af79016459e115450040',
      凭证负责人审核不通过: '4028817163ba733d0163ba78b0860001',
      凭证负责人审核通过: '4028817163ba733d0163ba78f9890002',
      凭证财务免签: '4028817163c8515d0163c854a2b60001',
      凭证财务审核不通过: '4028a081554e5ace01554e5f4fa30005',
      凭证财务审核通过: '4028a081554e5ace01554e5e51520004',
      凭证财务记账不通过: '402881d16458af79016459e540490041',
      凭证财务记账通过: '402881d16458af79016459df9953003f',
      凭证退回到财务: '4028518157b35bb60157b37837820003',
      已强制收回: '402881ef63c858e60163c974667d000d',
      院长免签: '402881d1602fce2801602ff30d7f0017',
      院长审核不通过: '402881ed5436bf4a015436e825470008',
      院长审核通过: '402881ed5436bf4a015436e7d7340007',
    }

    aasm column: :state, enum: true do
      state :凭证已经保存, initial: true
      state :凭证已经提交, :凭证负责人审核不通过, :凭证负责人免签, :凭证负责人审核通过, :凭证财务审核不通过, :凭证财务免签, :凭证财务审核通过,
        :凭证退回到财务, :凭证分管领导审核不通过, :凭证分管领导免签, :凭证分管领导审核通过, :院长审核不通过, :院长免签, :院长审核通过,
        :凭证财务记账不通过, :凭证财务记账通过, :凭证出纳审核不通过, :凭证出纳审核通过, :凭证已退件, :凭证已收件, :付款失败,
        :付款成功, :已强制收回

			after_all_transitions :generate_payment_check

      event :提交凭证 do
        transitions from: [:凭证已经保存, :凭证负责人审核不通过, :凭证分管领导审核不通过, :院长审核不通过], to: :凭证已经提交
      end

      event :负责人审核通过 do
        transitions from: [:凭证已经提交], to: :凭证负责人审核通过
      end

      event :负责人审核不通过 do
        transitions from: [:凭证已经提交], to: :凭证负责人审核不通过
      end

      event :财务审核通过 do
        transitions from: [:凭证负责人审核通过], to: :凭证财务审核通过
      end

      event :财务审核不通过 do
        transitions from: [:凭证负责人审核通过], to: :凭证已经保存
      end

      event :分管领导审核通过 do
        transitions from: [:凭证财务审核通过, :凭证财务免签], to: :凭证分管领导审核通过
      end

      event :分管领导审核不通过 do
        transitions from: [:凭证财务审核通过, :凭证财务免签], to: :凭证分管领导审核不通过
      end

      event :分管领导免签 do
        transitions from: [:凭证财务审核通过, :凭证财务免签], to: :凭证分管领导免签
      end

      event :院长审核通过 do
        transitions from: [:凭证分管领导审核通过, :凭证分管领导免签], to: :院长审核通过
      end

      event :院长审核不通过 do
        transitions from: [:凭证分管领导审核通过, :凭证分管领导免签], to: :院长审核不通过
      end

      event :院长免签 do
        transitions from: [:凭证分管领导审核通过, :凭证分管领导免签], to: :院长免签
      end

      event :出纳审核通过 do
        transitions from: [:院长免签, :院长审核通过], to: :凭证出纳审核通过
      end

      event :出纳审核不通过_重新走审批流程 do
        transitions from: [:院长免签, :院长审核通过], to: :凭证已经保存
      end

      event :出纳审核不通过_补充后审核 do
        transitions from: [:院长免签, :院长审核通过], to: :凭证财务审核不通过
      end

      event :财务记账通过 do
        transitions from: [:凭证出纳审核通过], to: :凭证财务记账通过
      end

      event :财务记账不通过_重新走审批流程 do
        transitions from: [:凭证出纳审核通过], to: :凭证已经保存
      end

      event :财务记账不通过_补充后审核 do
        transitions from: [:凭证出纳审核通过], to: :凭证财务审核不通过
      end

      event :重新提交凭证至财务 do
        transitions from: [:凭证财务审核不通过], to: :院长免签
      end

      event :收件 do
        transitions from: [:凭证财务记账通过], to: :凭证已收件
      end

      event :退件 do
        transitions from: [:凭证财务记账通过], to: :凭证已退件
      end

      event :付款成功 do
        transitions from: [:凭证已收件], to: :付款成功
      end

      event :付款失败 do
        transitions from: [:凭证已收件], to: :付款失败
      end
    end

    scope :paid, -> { where(state: ['付款成功', '凭证已收件', '凭证退回到财务']) }
    scope :paying, -> { where(state: ['凭证出纳审核不通过', '凭证分管领导审核不通过', '凭证分管领导审核通过',  '凭证已经保存', '凭证已经提交', '凭证已退件', '凭证负责人审核通过', '凭证负责人审核不通过', '凭证财务免签', '院长免签', '院长审核通过', '凭证财务记账通过', '凭证分管领导免签', '凭证财务审核通过', '院长审核不通过', '凭证财务审核不通过', '凭证出纳审核通过', '凭证出纳审核不通过']) }
    scope :checking, -> { where(state: '凭证负责人审核通过') }
    scope :printable, -> { where(state: '凭证财务记账通过') }
    scope :approving, -> (code) {
      joins(routine_third: { routine_budget: :routine_base}).where(zjk_routine_base: { personInCharge: code }, state: '凭证已经提交').or(
        joins(routine_third: { routine_budget: :routine_base}).where(zjk_routine_base: { manger: code }, state: '凭证财务审核通过')
      ).or(
        joins(routine_third: { routine_budget: :routine_base}).where(zjk_routine_base: { dean: code }, state: '凭证分管领导审核通过')
      )
    }

    def generate_payment_check params={}
      event = params[:event]
      payment_checks.create!(
        id: SecureRandom.hex,
        checkResult: aasm.to_state,
        checker: params[:checker],
        checkDate: Time.zone.today + 8.hours,
        checkType:  event_to_check_type(event)[:check_type],
        opinion: params[:opinion],
        insert_u: params[:checker],
        insert_d: Time.zone.now + 8.hours,
        update_u: params[:checker],
        update_d: Time.zone.now + 8.hours,
      )
    end

    def routine_base_code
      routine_third&.routine_budget&.routine_base&.code
    end

    def routine_budget_name
      routine_third&.routine_budget&.name
    end

    def routine_base_name
      routine_third&.routine_budget&.routine_base&.name
    end

    def person_in_charge
      routine_third&.routine_budget&.routine_base&.personInCharge
    end

    def person_in_charge_teacher_name
      routine_third&.routine_budget&.routine_base&.person_in_charge_teacher_name
    end

    def department_leadership_teacher_name
      routine_third&.routine_budget&.routine_base&.department_leadership_teacher_name
    end

    def manager_teacher_name
      routine_third&.routine_budget&.routine_base&.manager_teacher_name
    end

    def manager
      routine_third&.routine_budget&.routine_base&.manger
    end

    def dean_teacher_name
      routine_third&.routine_budget&.routine_base&.dean_teacher_name
    end

    def dean
      routine_third&.routine_budget&.routine_base&.dean
    end

    def is_need_dean
      routine_third&.routine_budget&.routine_base&.is_sign
    end

    def department_ref_name
      routine_third&.routine_budget&.routine_base&.department_ref_name
    end

    # invoke aasm event
    def fire event
      aasm.fire!(event.to_sym)
    end

    def permit_events
      aasm.events(permitted: true).map do |event|
        event_to_check_type(event.name).merge(action: event.name)
      end
    end

    def event_to_check_type event
      event_map = {
        提交凭证: {
          role: :submitter,
          check_type: '',
          type: :next,
        },
        负责人审核通过: {
          role: :person_in_charge,
          check_type: '负责人确认',
          type: :next,
        },
        负责人审核不通过: {
          role: :person_in_charge,
          check_type: '负责人确认',
          type: :reject,
        },
        财务审核通过: {
          role: :fund_leader,
          check_type: '财务审核',
          type: :next,
        },
        财务审核不通过: {
          role: :fund_leader,
          check_type: '财务审核',
          type: :reject,
        },
        分管领导审核通过: {
          role: :manager,
          check_type: '分管领导审核',
          type: :next,
        },
        分管领导审核不通过: {
          role: :manager,
          check_type: '分管领导审核',
          type: :reject,
        },
        分管领导免签: {
          role: :system,
          check_type: '分管领导审核',
          type: :auto,
        },
        院长审核通过:  {
          role: :dean,
          check_type: '院长意见',
          type: :next,
        },
        院长审核不通过:  {
          role: :dean,
          check_type: '院长意见',
          type: :reject,
        },
        院长免签:  {
          role: :system,
          check_type: '院长意见',
          type: :auto,
        },
        出纳审核通过:  {
          role: :fund_checker,
          check_type: '出纳审核',
          type: :next,
        },
        出纳审核不通过_重新走审批流程:  {
          role: :fund_checker,
          check_type: '出纳审核',
          type: :reject,
        },
        出纳审核不通过_补充后审核:  {
          role: :fund_checker,
          check_type: '出纳审核',
          type: :rewind,
        },
        财务记账通过:  {
          role: :fund_leader,
          check_type: '财务审核',
          type: :next,
        },
        财务记账不通过_重新走审批流程:  {
          role: :fund_leader,
          check_type: '财务审核',
          type: :reject,
        },
        财务记账不通过_补充后审核:  {
          role: :fund_leader,
          check_type: '财务审核',
          type: :rewind,
        },
        重新提交凭证至财务:  {
          role: :submitter,
          check_type: '',
          type: :next,
        },
        收件:  {
          role: :fund_cashier,
          check_type: '出纳审核',
          type: :next,
        },
        退件:  {
          role: :fund_cashier,
          check_type: '出纳审核',
          type: :reject,
        },
        付款成功:  {
          role: :fund_cashier,
          check_type: '出纳审核',
          type: :next,
        },
        付款失败:  {
          role: :fund_cashier,
          check_type: '出纳审核',
          type: :reject,
        },
      }
      event_map[event.to_sym]
    end
  end

  class_methods do
    def ransackable_scopes(_auth_object = nil)
      [ :paid, :paying, :checking, :approving, :printable ]
    end

  end
end
