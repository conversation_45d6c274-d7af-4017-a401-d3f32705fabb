module StieiFund::Payment
  extend ActiveSupport::Concern

  included do
    def routine_payment_paid_amount
      routine_payments.paid.sum(:reimbursementAmount)
    end

    def routine_payment_paid_count
      routine_payments.paid.count
    end

    def outside_payment_paid_amount
      outside_payments.paid.sum(:reimbursementAmount)
    end

    def outside_payment_paid_count
      outside_payments.paid.count
    end

    def routine_payment_paying_amount
      routine_payments.paying.sum(:reimbursementAmount)
    end

    def routine_payment_paying_count
      routine_payments.paying.count
    end

    def outside_payment_paying_amount
      outside_payments.paying.sum(:reimbursementAmount)
    end

    def outside_payment_paying_count
      outside_payments.paying.count
    end

    def avalible_amount
      amount_money - routine_payment_paid_amount - outside_payment_paid_amount - routine_payment_paying_amount - outside_payment_paying_amount
    end
  end
end
