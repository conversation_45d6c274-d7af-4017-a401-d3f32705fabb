module RegisterSource
  extend ActiveSupport::Concern

  included do

    def register_init users=nil
      return false if recently_inited?
      $redis.setex(inited_key, 900, true)
      users = users || send(self.class.instance_variable_get('@_register_user_key'))
      users.each do |user|
        register = register_association.find_or_initialize_by(user: user)
        register.update!(state: register_init_state) if register.new_record?
      end
      true
    end

    def recently_inited?
      $redis.get(inited_key)
    end

    def inited_key
      "register_init_key_#{self.class}#{id}"
    end

    def register_refresh nonce=register_nonce_generator
      $redis.setex(register_class.klass_redis_key(nonce), register_ttl, "#{self.class.name}##{id}")
      return nonce
    end

    def register_ttl
      20
    end

    def register_init_state
      'undo'
    end

    def register_nonce_generator
      SecureRandom.hex(10).chars.map { |i| i.hex }.join
    end

    # 二维码枪 扫用户码，获取用户对象
    def qrcode_get_user qrcode
      user = nil
      Array(qrcode_user_klasses).each do |klass|
        u = klass.idcode(qrcode) rescue nil
        if u
          user = u
          break
        end
      end
      unless user
        raise Error::BaseError.new(status: 422, message: '无效二维码')
      end
      return user
    end

    def qrcode_user_klasses
      raise ArgumentError.new('未定义 qrcode 鉴权对象类')
    end
  end

  module ClassMethods
    def register_user users_key, register_association, class_name:
      @_register_user_key = users_key
      has_many register_association, class_name: class_name, as: :source
      alias_method :register_association, register_association
      alias_method :registers, register_association
      define_method(:register_class) { class_name.constantize }
    end
  end
end
