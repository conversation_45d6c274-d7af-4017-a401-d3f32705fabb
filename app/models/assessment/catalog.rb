# == Schema Information
#
# Table name: assessment_catalogs
#
#  id                                  :bigint           not null, primary key
#  activity_id(所属活动)               :bigint
#  score_template_id(评分模版)         :bigint
#  name(考核分类名称)                  :string(255)
#  submit_workflow_id(提交考核工作流)  :bigint
#  confirm_workflow_id(提交考核工作流) :bigint
#  created_at                          :datetime         not null
#  updated_at                          :datetime         not null
#
# Indexes
#
#  index_assessment_catalogs_on_activity_id          (activity_id)
#  index_assessment_catalogs_on_confirm_workflow_id  (confirm_workflow_id)
#  index_assessment_catalogs_on_score_template_id    (score_template_id)
#  index_assessment_catalogs_on_submit_workflow_id   (submit_workflow_id)
#

class Assessment::Catalog < ApplicationRecord
  belongs_to :activity
  belongs_to :score_template
  belongs_to :submit_workflow, class_name: 'Workflow', optional: true
  belongs_to :confirm_workflow, class_name: 'Workflow', optional: true
  has_many :sub_groups, dependent: :destroy
  has_many :groups, through: :sub_groups
  has_many :dimensions, dependent: :destroy
  has_many :entries, through: :sub_groups, dependent: :destroy
  has_many :scores, through: :entries

  delegate :name, to: :score_template, prefix: true, allow_nil: true

  def dimension_stat
    dimensions.map do |dimension|
      dimension_scores = scores.where(dimension: dimension)
      dimension.dimension_stat dimension_scores: dimension_scores
    end
  end

  def done_score_ratio
    done_score_count = scores.where(state: 'done').count
    score_count = scores.count
    score_count == 0 ? 0 : done_score_count.to_f / score_count
  end

  def entry_count
    entries.count
  end
end
