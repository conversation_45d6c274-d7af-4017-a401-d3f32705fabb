# == Schema Information
#
# Table name: instances
#
#  id                                                        :bigint           not null, primary key
#  workflow_id(所属流程)                                     :bigint
#  creator_type                                              :string(255)
#  creator_id(流程发起人)                                    :bigint
#  payload(流程表单)                                         :json
#  state(流程状态)                                           :string(255)
#  seq(序列号)                                               :string(255)
#  type(STI类型)                                             :string(255)
#  created_at                                                :datetime         not null
#  updated_at                                                :datetime         not null
#  meta(扩展存储的字段json)                                  :json
#  flowable_type                                             :string(255)
#  flowable_id(流程化的对应实例)                             :bigint
#  storage(instance的数据存储，主要是有配置map_key 的 value) :json
#  summary(显示在列表页的 key-value)                         :json
#  flag(对于flowable时候使用进行区分)                        :string(255)
#
# Indexes
#
#  index_instances_on_creator_type_and_creator_id    (creator_type,creator_id)
#  index_instances_on_flowable_type_and_flowable_id  (flowable_type,flowable_id)
#  index_instances_on_state                          (state)
#  index_instances_on_type                           (type)
#  index_instances_on_workflow_id                    (workflow_id)
#

class Assessment::ConfirmInstance < Bpm::Instance
end
