# == Schema Information
#
# Table name: assessment_groups
#
#  id                                     :bigint           not null, primary key
#  activity_id(所属活动)                  :bigint
#  name(考核组的名称)                     :string(255)
#  meta(扩展字段，例如用来存储部门名称等) :json
#  created_at                             :datetime         not null
#  updated_at                             :datetime         not null
#
# Indexes
#
#  index_assessment_groups_on_activity_id  (activity_id)
#

class Assessment::Group < ApplicationRecord
  belongs_to :activity
  delegate :name, to: :activity, prefix: true
  has_many :sub_groups, dependent: :destroy
  has_many :catalogs, through: :sub_groups
  has_many :dimensions, through: :catalogs
  has_many :entries, through: :sub_groups, dependent: :destroy
  has_many :scores, through: :entries, dependent: :destroy

  delegate :name, to: :activity, prefix: true, allow_nil: true

  include ActsAsPasting::Pasted
  pasted_with ::Department
  acts_as_pasted :departments, source_type: 'Department'

  def entry_count
    entries.count
  end

  def done_score_ratio
    done_score_count = scores.where(state: 'done').count
    score_count = scores.count
    score_count == 0 ? 0 : done_score_count.to_f / score_count
  end
end
