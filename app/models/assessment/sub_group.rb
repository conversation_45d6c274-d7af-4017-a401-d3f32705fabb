# == Schema Information
#
# Table name: assessment_sub_groups
#
#  id                     :bigint           not null, primary key
#  group_id(对应的分组)   :bigint
#  catalog_id(对应的分类) :bigint
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#
# Indexes
#
#  index_assessment_sub_groups_on_catalog_id  (catalog_id)
#  index_assessment_sub_groups_on_group_id    (group_id)
#

class Assessment::SubGroup < ApplicationRecord
  belongs_to :group
  delegate :name, to: :group, prefix: true, allow_nil: true
  belongs_to :catalog
  delegate :name, to: :catalog, prefix: true, allow_nil: true
  has_many :entries, dependent: :destroy
  has_many :dimensions, through: :catalog
  has_many :scores, through: :entries
  has_one :activity, through: :group

  accepts_nested_attributes_for :entries, allow_destroy: true, reject_if: :all_blank

  def activity_id
    activity&.id
  end

  def entry_count
    entries.count
  end
end
