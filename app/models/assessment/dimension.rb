# == Schema Information
#
# Table name: assessment_dimensions
#
#  id                                        :bigint           not null, primary key
#  catalog_id(归属的类别)                    :bigint
#  weight(权重)                              :float(24)
#  type(STI)                                 :string(255)
#  score_template_id(对应的评分表，可以为空) :bigint
#  name(考核维度的名称)                      :string(255)
#  deleted_at(软删除字段)                    :datetime
#  created_at                                :datetime         not null
#  updated_at                                :datetime         not null
#  stage(考核打分的阶段)                     :string(255)
#  options(其他数据格式)                     :json
#
# Indexes
#
#  index_assessment_dimensions_on_catalog_id         (catalog_id)
#  index_assessment_dimensions_on_deleted_at         (deleted_at)
#  index_assessment_dimensions_on_score_template_id  (score_template_id)
#

class Assessment::Dimension < ApplicationRecord
  include TalltyImportExport::Importable
  include ActsAsPasting::Pasted
  pasted_with Assessment::Dimension
  acts_as_pasted :allow_dimensions, source_type: 'Assessment::Dimension'

  attribute :weight, :float, default: 1

  belongs_to :catalog
  delegate :activity, to: :catalog
  belongs_to :score_template, optional: true
  delegate :name, to: :score_template, prefix: true, allow_nil: true
  has_many :entries, through: :catalog, dependent: :destroy
  has_many :scores, dependent: :destroy

  before_save :set_type_nil_when_type_is_not_correct

  def dimension_stat dimension_scores: self.scores
    {
      id: id,
      name: name,
      score_stat: dimension_scores.group(:state).count,
      score: dimension_scores.average(:score).to_f.round(2),
    }
  end

  def entry_count
    entries.count
  end

  def done_score_ratio
    done_score_count = scores.where(state: 'done').count
    score_count = scores.count
    score_count == 0 ? 0 : done_score_count.to_f / score_count
  end

  def init_scores_by_activity
    activity.groups.each do |group|
      init_scores group: group
    end
  end

  def init_scores_by_catalog
    catalog.groups.each do |group|
      init_scores group: group
    end
  end

  def init_scores group:
  end

  private

  def set_type_nil_when_type_is_not_correct
    self.type = nil unless type.to_s.start_with?('Assessment::Dimensions')
  end

  class Import
    # 维度的导入，是导入这个维度下的score
    def import_headers **args
      [
        { key: 'entries', name: '工号', convert: :fetch_entries },
        { key: 'score', name: '年度平均分' },
      ]
    end

    def import_xlsx xlsx_file, associations, **options
      @dimension = Assessment::Dimension.find options[:dimension_id]
      super(xlsx_file, associations, **options)
    end

    def fetch_entries teacher_code, processing_line_info, raw_line_info
      teacher = ::Teacher.find_by(code: teacher_code)
      @dimension.entries.where(user: teacher)
    end

    def import_record line_info, associations
      line_info[:entries].each do |entry|
        score = associations.find_or_create_by(
          dimension: @dimension,
          entry: entry,
          user: nil,
        )
        score.update(
          score: line_info[:score],
          state: 'done',
        )
      end
    end
  end
end
