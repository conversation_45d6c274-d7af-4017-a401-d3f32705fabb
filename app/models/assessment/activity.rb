# == Schema Information
#
# Table name: assessment_activities
#
#  id                     :bigint           not null, primary key
#  school_id(对应学校)    :bigint
#  name(考核活动名称)     :string(255)
#  content(活动内容)      :text(65535)
#  start_at(活动开始时间) :datetime
#  end_at(活动结束时间)   :datetime
#  state(活动状态)        :string(255)
#  attachments(活动附件)  :json
#  stages(活动阶段)       :json
#  meta(扩展字段)         :json
#  deleted_at(软删除字段) :datetime
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#
# Indexes
#
#  index_assessment_activities_on_deleted_at  (deleted_at)
#  index_assessment_activities_on_school_id   (school_id)
#

class Assessment::Activity < ApplicationRecord
  include ActsAsPasting::Pasted
  include Succ::Assessment::CompeteEntry

  pasted_with ::Teacher, prefix: :admin_
  acts_as_pasted :admin_teachers, source_type: 'Teacher', prefix: :admin_

  pasted_with ::Teacher, prefix: :manage_
  acts_as_pasted :manage_teachers, source_type: 'Teacher', prefix: :manage_

  enum state: { pending: 'pending', published: 'published', finished: 'finished' }

  belongs_to :school
  has_many :groups, dependent: :destroy
  has_many :catalogs, dependent: :destroy
  has_many :dimensions, through: :catalogs
  has_many :sub_groups, through: :groups
  has_many :entries, dependent: :destroy
  has_many :scores, dependent: :destroy

  def sync_entry_level
    entries.each do |entry|
      entry.sync_level!
    end
  end

  def stage
    doing_stage = stages.stages.find do |_stage|
      _stage.state == 'doing'
    end
    doing_stage
  end

  def scored user
    scores.where(user: user).present?
  end

  def entried user
    entries.where(user: user).present?
  end

  def managed user
    manage_teachers.where(id: user.id).exists?
  end

  def entry_count
    entries.count
  end

  def group_count
    groups.count
  end

  # 现在是手动调用
  def init_scores
    dimensions.each do |dimension|
      dimension.init_scores_by_catalog
    end
  end

  def init_mutual_scores
    dimensions.where(type: Assessment::Dimensions::Mutual).each do |dimension|
      dimension.init_scores_by_catalog
    end
  end

  def exclude_user_ids
    # exclude_teacher_codes = %w(
    #   20200013 20200014 20200015 20200016 20200018 20200019 20200020 20200021 20200022 20200023 20200024 20200025
    #   20200026 20200027 20200028 20200029 20200030 20200031 20200032 20200033 20200034 20200035 20200036 20200037
    #   20200038 20200039 20200040 20200041 20200042 20200043 20200044 20200045 20200046 20200047 20200048 20200049
    #   20200050 20200051 20200052 20200053 20200054 20200055 20200056 20200057 20200058 20200059 20200060 20200061
    #   20200062 20200063 20200064 20200065 20200066 20200067 20200101
    #   20070093 20070353 20070072 20070130 20130003 20140027
    # )
    # school.teachers.where(code: exclude_teacher_codes).pluck(:id)
  end

  def include_user_ids
    # include_teacher_codes = %w(
    #   20072012 20190100 20142001 20152002 20202001 20132012 20152012 20070153 20112005 20170103 20132009 20132013 20152020
    # )
    # school.teachers.where(code: include_teacher_codes).pluck(:id)
  end

  def init_college_teacher_groups
    c1 = catalogs.find_or_create_by(name: '专任教师')
    school.departments.college.each do |college|
      manager_users = college.managers.full_time
      users = college.teachers.ransack(duties_name_eq: '专任教师').result.full_time.distinct.where.not(id: manager_users.select(:id)).where.not(id: exclude_user_ids)
      extract department: college, catalog: c1, users: users
    end

    c3 = catalogs.find_or_create_by(name: '教辅/管理/工勤')
    school.departments.college.each do |college|
      exclulde_users = college.teachers.ransack(duties_name_in: ['专任教师', '辅导员']).result.full_time.distinct
      manager_users = college.managers.full_time
      users = college.teachers.full_time.where.not(id: exclulde_users.select(:id) + manager_users.select(:id)).where.not(id: exclude_user_ids)
      extract department: college, catalog: c3, users: users
    end
  end

  def init_department_teacher_groups
    groups_arr = [
      %w( 党委办公室 党委组织（统战）部 宣传部 学生处 学生工作部 校工会 退休教职工管理委员会办公室 中国共产主义青年团委员会 人事处 教师工作部 纪检监察处 纪律检查委员会),
      %w(学校办公室 国际交流处 财务处 资产管理处 审计处),
      %w(教务处 科研处/学报编辑部暨高等教育研究所/发展规划处 校企合作处 质量管理处),
      %w(继续教育学院 图书馆 综合档案馆 信息中心),
      %w(安全保卫处 人民武装部 后勤保障处 基建处),
    ]
    catalog = catalogs.find_or_create_by(name: '教辅/管理/工勤')

    groups_arr.each_with_index do |group_names, index|
      group = groups.find_or_create_by(name: "职能部门分组#{index + 1}")
      group.departments = school.departments.where(depth: 1).where(name: group_names)
      sub_group = group.sub_groups.find_or_create_by(catalog: catalog)
      group_names.each do |group_name|
        department = school.departments.where(depth: 1).find_by(name: group_name)
        manager_users = department.managers.full_time
        users = department.teachers.full_time.distinct.where.not(id: manager_users.select(:id)).where.not(id: exclude_user_ids)
        include_users = department.teachers.where(id: include_user_ids)

        (users + include_users).each do |user|
          sub_group.entries.with_deleted.find_or_create_by(user: user).update(deleted_at: nil)
        end
        #
        # sub_group.entries.where.not(user: users).destroy_all
      end
    end
  end

  def init_college_counselor_group
    c2 = catalogs.find_or_create_by(name: '辅导员')
    school.departments.college.each do |college|
      users = college.teachers.ransack(duties_name_eq: '辅导员').result.full_time.distinct
      extract department: college, catalog: c2, users: users
    end
  end

  def extract department:, catalog:, users:
    return unless users.present?
    group = groups.find_or_create_by(name: department.name)
    group.departments = [department]
    sub_group = group.sub_groups.find_or_create_by(catalog: catalog)
    users.each do |user|
      sub_group.entries.with_deleted.find_or_create_by(user: user).update(deleted_at: nil)
    end

    sub_group.entries.where.not(user: users).destroy_all
  end

  class StageCollection < MetaRecord
    embeds_many :stages, class_name: '::Assessment::Activity::Stage'
    accepts_nested_attributes_for :stages
  end

  class Stage < MetaRecord
    attribute :state, :string
    attribute :key, :string
    attribute :name, :string
    attribute :start_at, :date
    attribute :end_at, :date

    def state
      if Time.zone.today < start_at.to_date
        'pending'
      elsif Time.zone.today > end_at.to_date
        'done'
      else
        'doing'
      end
    end
  end

  serialize :stages, StageCollection
end
