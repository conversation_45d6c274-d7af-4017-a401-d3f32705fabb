# == Schema Information
#
# Table name: assessment_dimensions
#
#  id                                        :bigint           not null, primary key
#  catalog_id(归属的类别)                    :bigint
#  weight(权重)                              :float(24)
#  type(STI)                                 :string(255)
#  score_template_id(对应的评分表，可以为空) :bigint
#  name(考核维度的名称)                      :string(255)
#  deleted_at(软删除字段)                    :datetime
#  created_at                                :datetime         not null
#  updated_at                                :datetime         not null
#  stage(考核打分的阶段)                     :string(255)
#  options(其他数据格式)                     :json
#
# Indexes
#
#  index_assessment_dimensions_on_catalog_id         (catalog_id)
#  index_assessment_dimensions_on_deleted_at         (deleted_at)
#  index_assessment_dimensions_on_score_template_id  (score_template_id)
#

class Assessment::Dimensions::Mutual < Assessment::Dimension
  # 互评，需要对于Group进行初始化设置
  def init_scores group:
    users = group.entries.map do |entry|
      OpenStruct.new(type: entry.user_type, id: entry.user_id)
    end

    # 添加互评信息
    group.entries.each do |entry|
      next unless entry.sub_group.catalog_id == self.catalog_id

      users.each do |user|
        next if user.type == entry.user_type && user.id == entry.user_id

        entry.scores.with_deleted.find_or_create_by!(user_type: user.type, user_id: user.id, dimension: self).update(deleted_at: nil)
      end
    end

    # 删除目前不在这里面的互评信息
    group.scores.where(dimension: self).each do |score|
      unless users.include?(OpenStruct.new(type: score.user_type, id: score.user_id))
        score.destroy
      end
    end
  end
end
