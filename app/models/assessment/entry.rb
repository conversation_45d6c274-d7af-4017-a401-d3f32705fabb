# == Schema Information
#
# Table name: assessment_entries
#
#  id                         :bigint           not null, primary key
#  activity_id(对应活动)      :bigint
#  sub_group_id(对应的子分组) :bigint
#  user_type                  :string(255)
#  user_id(对应的被考核人)    :bigint
#  deleted_at(软删除字段)     :datetime
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  score(考核总分)            :float(24)
#  levels                     :json
#
# Indexes
#
#  index_assessment_entries_on_activity_id            (activity_id)
#  index_assessment_entries_on_deleted_at             (deleted_at)
#  index_assessment_entries_on_sub_group_id           (sub_group_id)
#  index_assessment_entries_on_user_type_and_user_id  (user_type,user_id)
#

class Assessment::Entry < ApplicationRecord
  include TalltyImportExport::Exportable
  acts_as_paranoid
  validates_as_paranoid
  validates_uniqueness_of_without_deleted :sub_group_id, scope: [:user_type, :user_id]
  before_validation :set_activity

  belongs_to :activity, with_deleted: true
  belongs_to :sub_group
  belongs_to :sub_group_including_deleted, class_name: "Assessment::SubGroup",
    foreign_key: 'sub_group_id', with_deleted: true
  has_one :catalog, through: :sub_group
  has_one :group, through: :sub_group
  delegate :name, to: :group, prefix: true, allow_nil: true
  has_many :dimensions, through: :catalog
  delegate :catalog_name, :group_name, to: :sub_group
  belongs_to :user, polymorphic: true, with_deleted: false
  has_one :self_ref, class_name: self.name, foreign_key: :id
  has_one :teacher, through: :self_ref, source: :user, source_type: 'Teacher'
  has_one :student, through: :self_ref, source: :user, source_type: 'Student'
  delegate :name, :code, :department_name, to: :user, prefix: true, allow_nil: true
  has_many :dimensions, through: :sub_group
  has_many :scores, dependent: :destroy
  has_one :submit_instance, class_name: 'Assessment::SubmitInstance', as: :flowable, dependent: :destroy
  has_one :confirm_instance, class_name: 'Assessment::ConfirmInstance', as: :flowable, dependent: :destroy

  accepts_nested_attributes_for :scores, allow_destroy: true, reject_if: :all_blank

  delegate :id, to: :submit_instance, prefix: true, allow_nil: true
  delegate :id, to: :confirm_instance, prefix: true, allow_nil: true

  store_accessor :levels, :department_level, :code_level

  def sync_level!
    self.code_level = self.department_level
    self.save!
  end

  def set_score
    score = dimensions.reduce(0) do |sum_score, dimension|
      _score = scores.where(dimension: dimension, state: 'done').average(:score) * dimension.weight rescue 0
      sum_score += _score
    end&.round(2)
    update_columns(score: score) unless destroyed?
  end

  def score_stat
    scores.group(:state).count
  end

  def is_prepare
    submit_instance.present?
  end

  def dimension_stat
    dimensions.map do |dimension|
      dimension_scores = scores.where(dimension: dimension)
      dimension.dimension_stat dimension_scores: dimension_scores
    end
  end

  private

  def set_activity
    self.activity_id ||= sub_group.activity_id
  end

  class Export
    def export_headers **args
      arr = [
        { key: '_index', name: '序号', width: 10 },
        { key: 'user_name', name: '姓名', width: 15 },
        { key: 'user_code', name: '工号', width: 15 },
        { key: 'user_department_name', name: '部门' },
      ]
      if args[:group_key].present?
        dimension_arr = Assessment::Catalog.find(args[:group_key]).dimensions.map do |dimension|
          [
            { key: dimension.id, name: "#{dimension.name} 权重(#{dimension.weight*100}%)", method: :dimension_score },
            # { key: "score_#{dimension.id}", name: "#{dimension.name} 评分", method: :dimension_origin_score },
            # { key: "score_count_#{dimension.id}", name: "#{dimension.name} 总评分数", method: :dimension_score_count },
            # { key: "score_done_count_#{dimension.id}", name: "#{dimension.name} 已评分数", method: :dimension_score_done_count },
          ]
        end
        arr.concat dimension_arr
      end
      arr.concat(
        [
          { key: 'score', name: '总分', width: 15},
          { key: 'department_level', name: '综合评定等第', width: 20},
          { key: 'code_level', name: '单位确定等次', width: 20 },
        ]
      )
      arr.flatten
    end

    def dimension_score entry, header
      dimension_id = header[:key]
      weight = Assessment::Dimension.find(dimension_id).weight
      (entry.scores.where(dimension_id: dimension_id).average(:score).to_f * weight).round(2)
    end

    def dimension_origin_score entry, header
      dimension_id = header[:key].split('_')[-1]
      entry.scores.where(dimension_id: dimension_id).average(:score).to_f.round(2)
    end

    def dimension_score_count entry, header
      dimension_id = header[:key].split('_')[-1]
      entry.scores.where(dimension_id: dimension_id).count
    end

    def dimension_score_done_count entry, header
      dimension_id = header[:key].split('_')[-1]
      entry.scores.where(dimension_id: dimension_id).done.count
    end

    def process_options options = {}
      super(options)
      @group_by = 'assessment_catalogs.id'
      @group_where = 'catalog_id_eq'
    end

    def first_header
      "#{Assessment::Catalog.find(@group_key).name}考核表       #{Time.zone.today.strftime('%Y年%m月%d日')}"
    end

    def sheet_name
      Assessment::Catalog.find(@group_key).name.gsub(/\[|\]|\*|\/|\\|\?|:/, '|')
    end

    def with_scope entries
      entries.joins(:catalog).order(score: :desc)
    end
  end
end
