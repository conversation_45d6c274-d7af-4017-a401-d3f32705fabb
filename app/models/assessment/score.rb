# == Schema Information
#
# Table name: assessment_scores
#
#  id                                                       :bigint           not null, primary key
#  activity_id(考核活动)                                    :bigint
#  entry_id(被考核人)                                       :bigint
#  user_type                                                :string(255)
#  user_id(考核人)                                          :bigint
#  dimension_id(考核维度)                                   :bigint
#  score(考核得分)                                          :float(24)
#  state(考核状态)                                          :string(255)
#  deleted_at(软删除字段)                                   :datetime
#  created_at                                               :datetime         not null
#  updated_at                                               :datetime         not null
#  catalog_payload(对应score_template存储的catalog评分字段) :json
#  item_payload(对应score_template存储的item评分结果)       :json
#
# Indexes
#
#  index_assessment_scores_on_activity_id            (activity_id)
#  index_assessment_scores_on_deleted_at             (deleted_at)
#  index_assessment_scores_on_dimension_id           (dimension_id)
#  index_assessment_scores_on_entry_id               (entry_id)
#  index_assessment_scores_on_user_type_and_user_id  (user_type,user_id)
#

class Assessment::Score < ApplicationRecord
  acts_as_paranoid
  validates_as_paranoid
  validates_uniqueness_of_without_deleted :entry_id, scope: [:user_type, :user_id, :dimension_id]

  include TalltyImportExport::Exportable

  attribute :state, :string, default: 'todo'
  enum state: { todo: 'todo', done: 'done' }

  before_validation :set_activity

  belongs_to :activity
  delegate :stage, to: :activity, prefix: true
  belongs_to :entry, touch: true
  delegate :catalog_name, :group_name, to: :entry
  delegate :user_code, :user_name, :user_department_name, to: :entry, prefix: true
  belongs_to :user, polymorphic: true, optional: true
  delegate :name, :code, :department_name, to: :user, prefix: true, allow_nil: true
  belongs_to :dimension
  delegate :name, to: :dimension, prefix: true
  has_one :score_form, as: :source, dependent: :destroy

  before_save :set_score
  after_commit :set_entry_score

  accepts_nested_attributes_for :score_form, allow_destroy: true, reject_if: :all_blank

  def score_template
    dimension.score_template || entry.catalog&.score_template
  end

  def submit_instance_id
    entry.submit_instance&.id
  end

  def set_catalog_payload_by_item_payload
    return unless item_payload.present?

    _payload = {}
    score_template.form.catalogs.each do |catalog|
      arr = catalog.items.map do |item|
        item_payload[item.id]
      end.compact
      self.catalog_payload[catalog.id] = arr.sum if arr.size > 0
    end
  end

  def dimension_stat
    # 只需要获取该维度能看到其他维度的汇总
    allow_dimensions = self.dimension.allow_dimensions
    allow_dimensions.map do |_dimension|
      dimension_scores = entry.scores.where(dimension: _dimension)
      _dimension.dimension_stat dimension_scores: dimension_scores
    end
  end

  private

  def set_activity
    self.activity_id ||= entry.activity_id
  end

  def set_score
    if score_template.present? && item_payload.present? && state == 'done'
      _score = 0
      score_template.form.catalogs.each do |catalog|
        catalog.items.each do |item|
          _score += item_payload[item.id] * catalog.weight
        end
      end
      self.score = _score.round(2)
    end
  end

  def set_entry_score
    entry&.set_score
  end

  class Export
    def export_headers **args
      [
        { key: 'entry_user_name', name: '被考核人' },
        { key: 'entry_user_code', name: '被考核人工号' },
        { key: 'entry_user_department_name', name: '被考核人部门' },
        { key: 'dimension_name', name: '考核维度' },
        { key: 'user_name', name: '考核人' },
        { key: 'user_code', name: '考核人工号' },
        { key: 'user_department_name', name: '考核人部门' },
        { key: 'state', name: '考核状态', method: :state_zh },
        { key: 'score', name: '考核分' },
      ]
    end

    def state_zh record, attr
      case record.state
      when 'done'
        '已评分'
      else
        '待评分'
      end
    end
  end
end
