# == Schema Information
#
# Table name: contract_tasks
#
#  id                                 :bigint           not null, primary key
#  name(合同的名称)                   :string(255)
#  state(合同任务状态)                :string(255)
#  completed_at(完成时间)             :datetime
#  teacher_id(负责人)                 :bigint
#  school_id(对应学校)                :bigint
#  payload_value(工作流的表单内容)    :json
#  created_at                         :datetime         not null
#  updated_at                         :datetime         not null
#  execute_teacher_id(承办部门负责人) :bigint
#  work_teacher_id                    :bigint
#
# Indexes
#
#  index_contract_tasks_on_execute_teacher_id  (execute_teacher_id)
#  index_contract_tasks_on_school_id           (school_id)
#  index_contract_tasks_on_teacher_id          (teacher_id)
#  index_contract_tasks_on_work_teacher_id     (work_teacher_id)
#

class Contract::Task < ApplicationRecord
  belongs_to :teacher, class_name: '::Teacher'
  belongs_to :execute_teacher, class_name: '::Teacher'
  belongs_to :work_teacher, class_name: '::Teacher'
  delegate :name, :code, :department_name, to: :work_teacher, prefix: true, allow_nil: true
  delegate :name, :code, :department_name, to: :teacher, prefix: true, allow_nil: true
  delegate :name, :code, :department_name, to: :execute_teacher, prefix: true, allow_nil: true
  belongs_to :school
  before_validation :set_school

  attribute :state, :string, default: '执行中'
  enum state: {
    todo: '执行中',
    completed: '已完成',
  }

  after_create :generate_complete_instance

  def work_teacher_code= code
    self.work_teacher = Teacher.find_by(code: code)
  end

  def teacher_code= code
    self.teacher = Teacher.find_by(code: code)
  end

  def execute_teacher_code= code
    self.execute_teacher = Teacher.find_by(code: code)
  end

  include Bpm::Ext::Flowable
  # 完成任务
  acts_as_flowable(
    flag: 'complete',
    flag_name: '完成任务',
    workflow_callbacks: [
      { name: '完成任务', callback_method: :complete_task },
    ],
    workflow_roles: ['任务执行人', '任务负责人', '承接负责人'],
  )

  def complete_task instance
    update! completed_at: Time.zone.now, state: 'completed'
  end

  def instance_roles
    {
      '任务执行人' => work_teacher_id,
      '承接负责人' => execute_teacher_id,
      '任务负责人' => teacher_id,
    }
  end

  def flowable_info
    {
      work_teacher_name: work_teacher_name,
      work_teacher_code: work_teacher_code,
      execute_teacher_name: execute_teacher_name,
      execute_teacher_code: execute_teacher_code,
      teacher_name: teacher_name,
      teacher_code: teacher_code,
      teacher_department: teacher_department_name,
      state: state,
      completed_at: completed_at,
    }
  end

  def generate_complete_instance
    instance = generate_complete_instance_by_user work_teacher
    payload = {
      contract_name: payload_value['合同名称'],
      contract_amount: (payload_value['合同金额'] || payload_value['合同金额（元）']).to_f,
      company_name: payload_value['对方单位名称'],
      company_mobile: payload_value['联系方式'].to_s,
      finance_project_code: payload_value['资金卡编号'],
    }
    instance.update payload: payload
    instance.tokens.first.fire!(action: 'submit')
    instance
  end

  private

  def set_school
    self.school ||= teacher.school
  end
end
