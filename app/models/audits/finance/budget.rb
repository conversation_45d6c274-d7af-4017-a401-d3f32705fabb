class Audits::Finance::Budget < Audits::Base
	def headers
		{
			catalog_id: { method: :catalog },
			project_id: { method: :project },
			origin_id: { method: :origin },
			subject_id: { method: :subject }
		}
	end

	def catalog id; Finance::Catalog.find_by(id: id)&.name; end
	def project id; Finance::Project.find_by(id: id)&.name; end
	def origin id; Finance::Origin.find_by(id: id)&.name; end
	def subject id; Finance::Subject.find_by(id: id)&.name; end
end