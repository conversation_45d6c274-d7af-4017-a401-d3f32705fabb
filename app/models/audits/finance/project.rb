class Audits::Finance::Project < Audits::Base
  def headers
    {
      school_name: { skip_display: true },
      department_code: { method: :department_code },
      major_code: { method: :major_code },
      activity_id: { method: :activity },
      owner_id: { method: :owner },
      state: { method: :states }
    }
  end

  def owner id; Teacher.find_by(id: id)&.name; end
  def activity id; ::Finance::Activity.find_by(id: id)&.school_name; end

  def states state
    {
      'pending' => '未承诺',
      'approving' => '审批中', 
      'using' => '使用中', 
      'finished' => '已完成',
    }['state']
  end

end