class Audits::Base
  attr_accessor :audit, :options

  def initialize(options={})
    @options = options
    @audit   = options[:audit]
  end

  ### you can overwrite this method
  def headers; {}; end

  def load!
    i18n_key, oprations = audit.auditable_type.split('::'), []
    kclass = "Audits::#{audit.auditable_type}".constantize.new(options)
    headers = kclass.headers

    attributes = audit.audited_changes.clone
    attributes.delete_if { |k, v| v.nil? }

    attributes.each do |k, v|
      begin
        key = i18n_key.clone.push(k).unshift('activerecord') * '.'
        key = I18n.t("#{key.downcase}")
        # 自行配置
        attribute = headers[k.to_sym]
        ### 过滤掉有保存记录，但是不展示
        if attribute
          next if attribute[:skip_display]
          if method = attribute[:method]
            v = v.is_a?(Array) ? [kclass.send(method, v.first), kclass.send(method, v.last)] : kclass.send(method, v)
          end
          next if v.blank?
        end
      rescue
        next
      end
      oprations << { key: key, value: v }
    end

    model_name = i18n_key.clone.unshift('model') * '.'
    { oprations: oprations, modelname: I18n.t("#{model_name.downcase}") }
  end

  def school(id); School.find_by(id: id)&.name; end
  def college(id); College.find_by(id: id)&.name; end
  def major(id); Major.find_by(id: id)&.name; end
  def department(id); ::Department.find_by(id: id)&.name; end
  def department_code code; ::Department.find_by(code: code)&.name; end;
  def major_code code; Major.find_by(code: code)&.name; end
end