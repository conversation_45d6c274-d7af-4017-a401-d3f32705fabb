# == Schema Information
#
# Table name: ep_activities
#
#  id                   :bigint           not null, primary key
#  school_id(学校id)    :bigint
#  teacher_id(教师id)   :bigint
#  name(名称)           :string(255)      default("")
#  state(状态)          :string(255)
#  start_at(开始时间)   :datetime
#  end_at(结束时间)     :datetime
#  meta(扩展字段)       :json
#  deleted_at(软删标识) :datetime
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  info(信息)           :json
#
# Indexes
#
#  index_ep_activities_on_school_id   (school_id)
#  index_ep_activities_on_teacher_id  (teacher_id)
#

class Ep::Activity < ApplicationRecord
  include ActsAsPasting::Pasted
  pasted_with ::Teacher, prefix: :manage
  acts_as_pasted :manage_teachers, prefix: :manage, source_type: 'Teacher'
  pasted_with ::Department, prefix: :self_
  pasted_with ::Department, prefix: :subtree_
  pasted_with ::Adminclass

  belongs_to :school
  belongs_to :teacher, class_name: '::Teacher', optional: true
  has_many :attendances, dependent: :destroy
  has_many :registers, through: :attendances
  alias_method :ep_registers, :registers
  has_many :teachers, through: :attendances, source: :user, source_type: 'Teacher'
  has_many :students, through: :attendances, source: :user, source_type: 'Student'

  has_many :ep_activity_inspectors, -> { where(type: 'Inspector::EpActivity') }, class_name: 'Inspector', as: :source
  has_many :ep_inspect_teachers, through: :ep_activity_inspectors,  source: :user, source_type: ::Teacher

  validates :name, presence: true
  enum state: { pending: 'pending', starting: 'starting', completed: 'completed' }
  attribute :state, :string, default: 'pending'
  store_accessor :meta, :questions
  store_accessor :info, :program_ids
  attr_accessor :add_student

  after_commit :reset_attendances!, only: :update, if: :add_student
  # after_update :genarate_activity_job, only: :update, if: :add_student

  def self.stat_by_day day: Time.zone.today
    starting.reduce({}).each do |h, activity|
      activity.info
    end
  end

  # 统计打卡 健康问题
  def statistics(options={})
    options ||= {}
    options.delete('state_eq')
    total_registers = registers.ransack(options).result
    total_count, rates = total_registers.count, {}
    qs = questions || []

    register = {
      total: total_registers.count,
      undo: total_registers.where(state: 'undo').count,
      done: total_registers.where(state: 'done').count
    }

    qs.each do |q|
      key, options = q['key'], q['options']
      rates[key] = {}
      options.each do |option|
        number = total_registers.where(state: 'done').where("registers.meta->'$.\"#{key}\"' = ?", option['key']).count
        rate = total_count > 0 ? (number * 100.0 / total_count).round(2) : 0
        rates[key][option['key']] = { number: number, rate: rate }
      end
    end

    { register: register, question: rates }
  end

  def reset_attendances!
    self.add_student = nil
    student_ids = Student.where(program_id: program_ids, school_id: school_id).pluck(:id)
    update(student_ids: student_ids)
  end

  def nested_infos
    _registers = registers.where(date: Date.today)
    students = _registers.where(user_type: 'Student')
    teachers = _registers.where(user_type: 'Teacher')
    {
      register: {
        student: {
          total: students.count,
          undo:  students.where(state: 'undo').count,
          done:  students.where(state: 'done').count
        },
        teacher: {
          total: teachers.count,
          undo:  teachers.where(state: 'undo').count,
          done:  teachers.where(state: 'done').count
        }
      }
    }
  end

  def programs
    Teaching::Program.where(id: program_ids)
  end

  def generate_statistic_notifications
    date = Time.zone.today
    ep_inspect_teachers.each do |user|
      Wechat::TemplateMessage.create(
        school: user.school,
        app_id: 'wxb1daea392d68c3f5',
        receiver: user,
        notifyable: self,
      ) do |notification|
        notification.message = {
          template_id: 'LJb6Nr0l2T3g7jTDWq0E0oNuIUBtPEbQ5ozw1HoHgWU',
          url: "#{ENV['MOBILE_URL']}/ep/activities",
          topcolor: '#FF0000',
          data: {
            first: {
              color: '#0033FF',
              value: '您部门今日的健康打卡统计报告已生成，请您进入查看',
            },
            keyword1: {
              value: '健康打卡统计报告',
            },
            keyword2: {
              value: "#{date} 健康打卡统计报告",
            },
            keyword3: {
              value: '直接进入查看',
            },
            keyword4: {
              value: "#{date} 健康打卡统计报告",
            },
            remark: {
              color: '#0ABB0A',
              value: "请尽快查看，并催促未完成打卡的人员尽快进行打卡。",
            },
          }
        }
      end
    end
  end

  private
    def genarate_activity_job
      EpActivityJob.perform_later(id: id)
    end
end
