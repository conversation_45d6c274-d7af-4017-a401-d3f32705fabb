# == Schema Information
#
# Table name: ep_attendances
#
#  id                    :bigint           not null, primary key
#  activity_id(活动id)   :bigint
#  college_id(院系id)    :bigint
#  major_id(专业id)      :bigint
#  adminclass_id(班级id) :bigint
#  user_type             :string(255)
#  user_id(用户)         :bigint
#  state(状态)           :integer          default("active")
#  meta(扩展字段)        :json
#  deleted_at(软删标识)  :datetime
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#
# Indexes
#
#  index_ep_attendances_on_activity_id            (activity_id)
#  index_ep_attendances_on_adminclass_id          (adminclass_id)
#  index_ep_attendances_on_college_id             (college_id)
#  index_ep_attendances_on_major_id               (major_id)
#  index_ep_attendances_on_user_type_and_user_id  (user_type,user_id)
#

class Ep::Attendance < ApplicationRecord
  include Ep::Ext::StudentExcel

  default_scope { where(state: :active) }

	belongs_to :activity
  delegate :name, to: :activity, prefix: true
	belongs_to :user,   polymorphic: true
	has_many :registers, as: :source, dependent: :destroy
	alias_method :ep_registers, :registers

	validates :activity_id, uniqueness: { scope: [:user_id, :user_type] }
	enum state: { active: 1, unactive: 0 }
	scope :teacher, -> { where(user_type: 'Teacher') }
	scope :student, -> { where(user_type: 'Student') }
	# before_create :init

	scope :statistics_by_program_id, ->(program_id) {
    student_ids = Teaching::Program.find(program_id).student_ids
    where(user_type: 'Student', user_id: student_ids)
  }

  def self.ransackable_scopes(auth_object = nil)
    [:statistics_by_program_id]
  end

	def statistics options={}
		options ||= {}
		options.merge!(user_type_eq: user_type, user_id_eq: user_id)
		activity.statistics(options)
	end

	def nested_infos
		{
			register: {
				total: registers.count,
				undo:  registers.where(state: 'undo').count,
				done:  registers.where(state: 'done').count,
				today: registers.where(state: 'done').by_day(Date.today).count > 0 ? 'done' : 'undo'
			}
		}
	end

	def reset_register_count!
		update_columns(meta: nested_infos)
	end

	private
		def init
			self.attributes = {
				college_id: user.college_id,
				major_id:   user.major_id,
				adminclass_id: user.is_a?(Student) ? user.adminclass_id : nil
			}
		end
end
