# == Schema Information
#
# Table name: cronjobs
#
#  id         :bigint           not null, primary key
#  cron       :string(255)
#  user_type  :string(255)
#  user_id    :bigint
#  type       :string(255)
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
# Indexes
#
#  index_cronjobs_on_user_type_and_user_id  (user_type,user_id)
#

class Cronjob::ComingAndGoing < Cronjob
  def process
    self.class.transaction do
      skip_dup_record do
        ::ComingAndGoing.create_coming({ user: user }, period: 'periodic')
      end
      skip_dup_record do
        ::ComingAndGoing.create_going({ user: user }, period: 'periodic')
      end
    end
  end

  def skip_dup_record &block
    begin
      block.call
    rescue Error::BaseError => e
      raise e unless e.message.include?('不可重复申请')
    end
  end

  def self.remove_user user
    Cronjob::ComingAndGoing.where(user: user).destroy_all
    Meeting::ActivityComingAndGoing.all.each do |m|
      m.meeting_attendances.where(user: user, state: '已报名').destroy_all
    end
  end
end

# Meeting::ActivityComingAndGoing.all.each do |m|
#   m.meeting_attendances.where(state: '已报名').where('created_at < ?', '2022-02-22 23:59:59 +08:00'.to_time).destroy_all
# end
