class RecorderCacheJob < ApplicationJob
  queue_as :default

  def perform(*args)
    Teaching::Lesson.where(date: Date.today).each do |lesson|
      lesson.students.each do |student|
        # 导入lesson 的 recorder
        Teaching::Recorder::Fast.load_recorder(
          course_id: lesson.course_id,
          user: student,
          source_type: 'Teaching::Lesson',
          source_id: lesson.id,
          lesson_id: lesson.id,
        )
        # 导入lesson_item 的 recorder
        lesson.lesson_plans.each do |lesson_plan|
          lesson_plan.lesson_items.each do |lesson_item|
            Teaching::Recorder::Fast.load_recorder(
              course_id: lesson.course_id,
              user: student,
              source_type: 'Teaching::LessonItem',
              source_id: lesson_item.id,
              lesson_id: lesson.id,
            )
          end
        end
      end
    end
  end
end
