class ExamActivityJob < ApplicationJob
  queue_as :default

  def perform(**args)
    Exam::AnswerSet.doing.find_each do |answer_set|
      activity = answer_set.answerable
      time = activity.duration_in_min.to_i.minutes.since(activity.start_at)
      finished = Time.now >= time
      answer_set.update(state: 'done') if finished
    end

    Exam::Activity.doing.find_each do |activity|
      next unless activity.start_at
      time = activity.duration_in_min.to_i.minutes.since(activity.start_at)
      if Time.zone.now > 1.hours.since(time)
        activity.update state: 'done'
      end
    end
  end
end
