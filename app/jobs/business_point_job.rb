class BusinessPointJob < ApplicationJob
  queue_as :default

  def perform(*args)
    ['2018-2019-2', '2019-2020-1'].each do |semester_code|
      semester = Semester.find_by_code(semester_code)
      date = Time.zone.today > semester.end_on ? semester.end_on - 2.month : Time.zone.today
      # 教师承担课时数
      name = '教师承担课时数'
      h = Teaching::Lesson.where(semester_id: semester.id).group(:teacher_id).sum('teaching_lessons.end_unit - teaching_lessons.start_unit + 1')
      h.each do |teacher_id, lesson_count|
        teacher = Teacher.find_by_id(teacher_id)
        next unless teacher.present?

        BusinessPoint.append(
          name: name,
          source: teacher,
          key: teacher.code,
          point: lesson_count,
          layer: 'teacher',
          date: date,
        )
      end
      # 课程课时数
      name = '课程课时数'
      h = Teaching::Lesson.where(semester_id: semester.id).joins(course: { course_set: :course_dir } ).group('teaching_course_dirs.id').sum('teaching_lessons.end_unit - teaching_lessons.start_unit + 1')
      h.each do |course_id, lesson_count|

        course = Teaching::CourseDir.find(course_id)
        BusinessPoint.append(
          name: name,
          source: course,
          key: course_id,
          point: lesson_count,
          layer: 'course',
          date: date,
        )
      end
    end

    ['2019-2020-2', '2020-2021-1'].each do |semester_code|
      semester = Semester.find_by_code(semester_code)
      date = Time.zone.today > semester.end_on ? semester.end_on - 1.month : Time.zone.today
      # 课程教案与教案首页数量
      name = '课程教案与教案首页数量'
      schedule_lessons = Teaching::ScheduleLesson.where("payload -> '$.schema_page' is NOT NULL").where("payload -> '$.schema_file' is NOT NULL")
      h = schedule_lessons.joins(schedule_course: { course: { course_set: :course_dir} } ).where(teaching_courses: {semester_id: semester.id } ).distinct.group('teaching_course_dirs.id').count
      h.each do |course_id, lesson_count|
        course = Teaching::CourseDir.find(course_id)
        BusinessPoint.append(
          name: name,
          source: course,
          key: course_id,
          point: lesson_count,
          layer: 'course',
          date: date,
        )
      end

      name = '课程后记完成课时数'
      schedule_lessons = Teaching::ScheduleLesson.where("payload -> '$.remark' is NOT NULL")
      h = schedule_lessons.joins(schedule_course: { course: { course_set: :course_dir} } ).where(teaching_courses: {semester_id: semester.id } ).distinct.group('teaching_course_dirs.id').count
      h.each do |course_id, lesson_count|
        course = Teaching::CourseDir.find(course_id)
        BusinessPoint.append(
          name: name,
          source: course,
          key: course_id,
          point: lesson_count,
          layer: 'course',
          date: date,
        )
      end
    end

    semester = Semester.first
    date = Time.zone.today
    name = '教师教案与教案首页数量'
    schedule_lessons = Teaching::ScheduleLesson.where("payload -> '$.schema_page' is NOT NULL").where("payload -> '$.schema_file' is NOT NULL")
    h = schedule_lessons.joins(schedule_course: { course: { course_set: :course_dir} } ).where(teaching_courses: {semester_id: semester.id } ).distinct.group('teaching_schedule_courses.teacher_id').count
    h.each do |teacher_id, lesson_count|
      teacher = Teacher.find teacher_id
      BusinessPoint.append(
        name: name,
        source: teacher,
        key: teacher.code,
        point: lesson_count,
        layer: 'teacher',
        date: date,
      )
    end

    name = '教师后记完成课时数'
    schedule_lessons = Teaching::ScheduleLesson.where("payload -> '$.remark' is NOT NULL")
    h = schedule_lessons.joins(schedule_course: { course: { course_set: :course_dir} } ).where(teaching_courses: {semester_id: semester.id } ).distinct.group('teaching_schedule_courses.teacher_id').count
    h.each do |teacher_id, lesson_count|
      teacher = Teacher.find teacher_id
      BusinessPoint.append(
        name: name,
        source: teacher,
        key: teacher.code,
        point: lesson_count,
        layer: 'teacher',
        date: date,
      )
    end

    # 课程布置课后作业
    ['2019-2020-2', '2020-2021-1'].each do |semester_code|
      semester = Semester.find_by_code(semester_code)
      date = Time.zone.today > semester.end_on ? semester.end_on - 1.month : Time.zone.today
      name = '课程布置课后作业'
      h = Report::Homework.joins(lesson: { course: { course_set: :course_dir } } ).where(teaching_courses: {semester_id: semester.id}).group('teaching_course_dirs.id').count
      h.each do |course_dir_id, homework_count|
        course_dir = Teaching::CourseDir.find(course_dir_id)
        BusinessPoint.append(
          name: name,
          date: date,
          source: course_dir,
          key: course_dir.id,
          teacher: course_dir.teacher&.code,
          point: homework_count,
          layer: 'course',
        )
      end
    end

    # 作业是否修改
    ['2019-2020-2', '2020-2021-1'].each do |semester_code|
      semester = Semester.find_by_code(semester_code)
      date = Time.zone.today > semester.end_on ? semester.end_on - 1.month : Time.zone.today
      h =  Report::Homework::Answer.where(state: 'scored').joins(homework: { lesson: {course: {course_set: :course_dir} } }).where(teaching_courses: { semester_id: semester.id}).group('teaching_course_dirs.id').count
      name = '作业是否修改'
      h.each do |course_dir_id, homework_count|
        course_dir = Teaching::CourseDir.find(course_dir_id)
        BusinessPoint.append(
          name: name,
          date: date,
          source: course_dir,
          key: course_dir.id,
          teacher: course_dir.teacher&.code,
          point: homework_count,
          layer: 'course',
        )
      end
    end

    ['2019-2020-2', '2020-2021-1'].each do |semester_code|
      semester = Semester.find_by_code(semester_code)
      date = Time.zone.today > semester.end_on ? semester.end_on - 1.month : Time.zone.today
      h = Topic.joins(course: { course_set: :course_dir } ).where( teaching_courses: { semester_id: semester.id} ).group('teaching_course_dirs.id').count
      name = '课程网上答疑话题数'
      h.each do |course_dir_id, topic_count|
        course_dir = Teaching::CourseDir.find(course_dir_id)
        BusinessPoint.append(
          name: name,
          date: date,
          source: course_dir,
          key: course_dir.id,
          teacher: course_dir.teacher&.code,
          point: topic_count,
          layer: 'course',
        )
      end
    end

    ['2018-2019-2', '2019-2020-1', '2019-2020-2', '2020-2021-1'].each do |semester_code|
      semester = Semester.find_by_code(semester_code)
      date = Time.zone.today > semester.end_on ? semester.end_on - 2.month : Time.zone.today
      name = '教师承担课时数'
      h = semester.lessons.group(:teacher_id).sum('teaching_lessons.end_unit - teaching_lessons.start_unit + 1')
      h.each do |teacher_id, lesson_count|
        teacher = Teacher.find_by_id(teacher_id)
        next unless teacher.present?

        BusinessPoint.append(
          name: name,
          date: date,
          source: teacher,
          key: teacher.code,
          point: lesson_count,
          layer: 'teacher',
        )
      end
    end

    date = '2019-12-01'
    StieiFund::RoutineBase.all.each do |routine_base|
      amount_name = '资金卡总额'
      amount = routine_base.routine_thirds.sum(&:amount_money).to_f
      used_amount_name = '资金卡使用金额'
      used_amount  = amount - routine_base.routine_thirds.sum(&:avalible_amount).to_f
      BusinessPoint.append(
        name: amount_name,
        date: date,
        source: routine_base,
        key: routine_base.personInCharge,
        point: amount,
        layer: 'teacher',
      )

      BusinessPoint.append(
        name: used_amount_name,
        date: date,
        source: routine_base,
        key: routine_base.personInCharge,
        point: used_amount,
        layer: 'teacher',
      )
    end
  end
end
