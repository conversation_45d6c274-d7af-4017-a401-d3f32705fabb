class LessonRegisterStatJob < ApplicationJob
  queue_as :default

  def perform(*args)
    Teaching::Lesson::Fast.all.each do |fast_lesson|
      begin
        # 已开始评价的课程
        record_lesson = Teaching::Lesson.find_by(id: fast_lesson.lesson_id)
        
        if record_lesson && fast_lesson.register_time_range.first < Time.now
          h = Hash.new(0)
          fast_lesson.fast_registers.each do |register|
            # 刷新数据库对象状态
            # 没有 flush 过，非 undo 的 fast_registers
            register.flush_the_record unless register.flushed || register.state == 'undo'
            h[register.state] += 1
          end
          # 刷新课程统计数据
          record_lesson.update!(register_stat: h)
        end
      ensure
        # 删除 Ohm 中已过了评价阶段的课程
        if fast_lesson.evaluate_time_range.last < Time.now
          fast_lesson.fast_registers.each(&:delete)
          fast_lesson.delete
        end
      end
    end
  end
end
