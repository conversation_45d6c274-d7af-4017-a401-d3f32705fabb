module ApplicationCable
  class Connection < ActionCable::Connection::Base
    # identified_by :current_user
 
    # def connect
    #   self.current_user = find_verified_user
    # end

    # private
    #   def find_verified_user
    #     status = true
    #     user = case params[:type]
    #              when 'teacher'
    #                Teacher.find_by(id: params[:id])
    #              when 'student'
    #                Student.find_by(id: params[:id])
    #            end

    #     if user
    #       time = Time.at params[:timestamp].to_i
    #       status = false if time > 1.minutes.ago && time < 1.minutes.since

    #       # 校验签名
    #       if status
    #         sign = params.delete('sign')
    #         options = params.merge({'code' => user.code })
    #         data = options.sort.map do |k, v|
    #           "#{k}=#{v}" if v.present?
    #         end
    #         data.compact * '&'
    #         status = false unless sign == Digest::MD5.hexdigest(data)
    #       end
    #       status ? user : reject_unauthorized_connection
    #     else
    #       reject_unauthorized_connection
    #     end
    #   end
  end
end
